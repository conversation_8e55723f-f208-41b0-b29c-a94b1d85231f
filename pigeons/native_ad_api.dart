import 'package:pigeon/pigeon.dart';

// README：复制到命令行里执行：
// dart run pigeon --input pigeons/native_ad_api.dart

@ConfigurePigeon(PigeonOptions(
  dartOut: 'lib/generated/pigeons/native_ad_api.dart',
  kotlinOut: 'android/app/src/main/kotlin/com/yupao/gongdijigong/pigeons/NativeAdPigeonApi.kt',
  kotlinOptions: KotlinOptions(
    package: 'com.yupao.gongdijigong.pigeons',
    errorClassName: "NativeAdError",
  ),
))
@HostApi()
abstract class NativeAdApi {
  void loadNativeAd(Map<String, Object?>? params);
  void showNativeAd(Map<String, Object?>? params);
  Map<String, Object?>? getAdInfo(Map<String, Object?>? params);
}

@FlutterApi()
abstract class NativeAdFlutterApi {
  void sendAdLoadFinishEvent(Map<String, Object?>? params);
  void sendAdLoadFailedEvent(Map<String, Object?>? params);
  void sendAdCloseEvent(Map<String, Object?>? params);
}
