import 'package:pigeon/pigeon.dart';

// README：复制到命令行里执行：
// dart run pigeon --input pigeons/navigation_api.dart

@ConfigurePigeon(PigeonOptions(
  dartOut: 'lib/generated/pigeons/navigation_api.dart',
  dartOptions: DartOptions(),
  swiftOut: 'ios/LocalPods/PigeonKit/PigeonKit/Classes/NavigationApi.g.swift',
  kotlinOut: 'android/app/src/main/kotlin/com/yupao/gongdijigong/pigeons/PigeonApi.kt',
  kotlinOptions: KotlinOptions(
    package: 'com.yupao.gongdijigong.pigeons',
    errorClassName: "NavigationError",
  ),
  swiftOptions: SwiftOptions(
    includeErrorClass: false,
  ),
))
class NavigationRequest {
  String? activityName; // 要跳转的 Activity 名称
  Map<String?, Object?>? arguments; // 传递的参数
}

class AccountData {
  String? userId;
  String? token;
  String? uuid;
  String? singleSignToken;
}

@HostApi()
abstract class CallNativeApi {
  void setAccountData(AccountData data);
  void openActivity(NavigationRequest request);
  void loadInterstitialAd({Map<String, Object?>? params});
  void showInterstitialAd({Map<String, Object?>? params});
}

@FlutterApi()
abstract class CallFlutterApi {
  void toFlutterPage(FlutterPageParams params);
}

class FlutterPageParams {
  String? fromNative;
  Map<String, String>? extraParams;
}
