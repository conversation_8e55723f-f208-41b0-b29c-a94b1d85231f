
import 'dart:io';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:fluwx/fluwx.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/wechat_util/wechat_config.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

/// 微信工具类
class WeChatUtil {
  WeChatUtil._();
  static final Fluwx _fluwx = Fluwx();

  /// 微信AppID
  static String get _weChatAppId => WeChatConfig.weChatAppId;

  /// 是否已初始化
  static bool _isInitialized = false;

  /// 初始化微信SDK
  static Future<bool> initialize() async {
    if (_isInitialized) {
      return true;
    }

    try {
      // 检查AppID是否配置
      if (_weChatAppId.isEmpty) {
        return false;
      }

      // 注册微信API
      await _fluwx.registerApi(
        appId: _weChatAppId,
        doOnAndroid: true,
        doOnIOS: true,
        universalLink: WeChatConfig.weChatUniversalLink,
      );

      _isInitialized = true;
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 检查微信是否已安装
  static Future<bool> checkWeChatInstalled() async {
    try {
      return await _fluwx.isWeChatInstalled;
    } catch (e) {
      return false;
    }
  }

  /// 跳转微信小程序
  /// [appId] 小程序AppID（可选）
  /// [originalId] 小程序原始ID（可选，优先使用）
  /// [path] 小程序页面路径（可选）
  /// [type] 小程序类型，默认正式版
  static Future<bool> launchMiniProgram({
    String? appId,
    String? originalId,
    String? path,
    WXMiniProgramType type = WXMiniProgramType.release,
  }) async {
    try {
      if (!await _ensureWeChatReady()) {
        return false;
      }

      // 检查必要参数
      if ((appId == null || appId.isEmpty) && (originalId == null || originalId.isEmpty)) {
        ToastUtil.showToast('小程序ID不能为空');
        return false;
      }

      // 使用原始ID或AppID作为username
      final username = originalId?.isNotEmpty == true ? originalId! : appId!;

      // 创建小程序对象并执行跳转
      final miniProgram = MiniProgram(
        username: username,
        path: path,
        miniProgramType: type,
      );

      final result = await _fluwx.open(target: miniProgram);

      if (!result) {
        ToastUtil.showToast('跳转小程序失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('跳转小程序失败');
      return false;
    }
  }

  /// 分享文本到微信
  /// [text] 分享的文本内容
  /// [scene] 分享场景（会话/朋友圈/收藏）
  static Future<bool> shareText({
    required String text,
    WeChatScene scene = WeChatScene.session,
  }) async {
    try {
      if (!await _ensureWeChatReady()) {
        return false;
      }

      // 分享文本
      final model = WeChatShareTextModel(text, scene: scene);
      final result = await _fluwx.share(model);

      if (result) {
        ToastUtil.showToast('分享成功');
      } else {
        ToastUtil.showToast('分享失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('分享失败');
      return false;
    }
  }

  /// 分享网页到微信
  /// [webpageUrl] 网页链接
  /// [title] 分享标题
  /// [description] 分享描述
  /// [thumbnail] 缩略图数据
  /// [scene] 分享场景
  static Future<bool> shareWebpage({
    required String webpageUrl,
    String? title,
    String? description,
    String? thumbnail,
    WeChatScene scene = WeChatScene.session,
  }) async {
    try {
      if (!await _ensureWeChatReady()) {
        return false;
      }

      // 分享网页
      final model = WeChatShareWebPageModel(
        webpageUrl,
        title: title,
        description: description,
        scene: scene,
      );

      final result = await _fluwx.share(model);

      if (result) {
        ToastUtil.showToast('分享成功');
      } else {
        ToastUtil.showToast('分享失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('分享失败');
      return false;
    }
  }

  /// 分享小程序到微信
  /// [webpageUrl] 兼容低版本的网页链接
  /// [userName] 小程序原始ID
  /// [path] 小程序页面路径
  /// [title] 分享标题
  /// [description] 分享描述
  /// [thumbnail] 缩略图
  /// [scene] 分享场景
  static Future<bool> shareMiniProgram({
    required String webpageUrl,
    required String userName,
    String? path,
    String? title,
    String? description,
    String? thumbnail,
    WeChatScene scene = WeChatScene.session,
  }) async {
    try {
      if (!await _ensureWeChatReady()) {
        return false;
      }

      // 处理缩略图
      Uint8List? thumbData;
      if (thumbnail != null && thumbnail.isNotEmpty) {
        final imageData = await _downloadImageFromUrl(thumbnail);
        if (imageData != null) {
          thumbData = imageData;
        }
      }

      // 分享小程序
      final model = WeChatShareMiniProgramModel(
        webPageUrl: webpageUrl,
        userName: userName,
        path: path ?? '',
        title: title,
        description: description,
        scene: scene,
        thumbData: thumbData,
      );

      final result = await _fluwx.share(model);

      if (result) {
        ToastUtil.showToast('分享成功');
      } else {
        ToastUtil.showToast('分享失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('分享失败');
      return false;
    }
  }

  /// 打开微信应用
  static Future<bool> openWeChatApp() async {
    try {
      if (!await _ensureWeChatReady()) {
        return false;
      }

      final result = await _fluwx.open(target: WeChatApp());

      if (!result) {
        ToastUtil.showToast('打开微信失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('打开微信失败');
      return false;
    }
  }
  /// 分享图片到微信
  /// [imageData] 图片字节数据
  /// [scene] 分享场景（会话/朋友圈/收藏）
  /// [title] 分享标题
  /// [description] 分享描述
  static Future<bool> shareImage({
    required Uint8List imageData,
    WeChatScene scene = WeChatScene.session,
    String? title,
    String? description,
  }) async {
    try {
      if (!await _ensureWeChatReady()) {
        return false;
      }

      // 创建图片分享对象
      final imageToShare = WeChatImageToShare(uint8List: imageData);

      // 分享图片
      final model = WeChatShareImageModel(
        imageToShare,
        scene: scene,
        title: title,
        description: description,
      );

      final result = await _fluwx.share(model);

      if (result) {
        ToastUtil.showToast('分享成功');
      } else {
        ToastUtil.showToast('分享失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('分享失败: $e');
      return false;
    }
  }

  /// 分享文件到微信
  /// [url] 文件下载链接
  /// [title] 分享标题（必选，作为下载的文件名称）
  /// [scene] 分享场景（会话/朋友圈/收藏），默认为会话
  static Future<bool> shareFile({
    required String url,
    required String title,
    WeChatScene scene = WeChatScene.session,
  }) async {
    String? tempFilePath;
    try {
      if (!await _ensureWeChatReady()) {
        return false;
      }

      // 检查URL是否有效
      if (url.isEmpty) {
        ToastUtil.showToast('下载链接不能为空');
        return false;
      }

      // 检查title是否有效
      if (title.isEmpty) {
        ToastUtil.showToast('文件标题不能为空');
        return false;
      }

      // 下载文件到临时目录
      tempFilePath = await _downloadFileFromUrl(url, customFileName: title);
      if (tempFilePath == null) {
        return false;
      }

      // 使用title的扩展名作为suffix
      final safeSuffix = path.extension(title);
      final finalSuffix = safeSuffix.isNotEmpty ? safeSuffix : '.txt';

      final displayTitle = path.basenameWithoutExtension(title);

      // 用 WeChatFile.file 时必须带上 suffix
      final model = WeChatShareFileModel(
        WeChatFile.file(File(tempFilePath), suffix: finalSuffix),
        scene: scene,
        title: displayTitle,
      );

      final result = await _fluwx.share(model);

      if (result) {
        ToastUtil.showToast('分享成功');
      } else {
        ToastUtil.showToast('分享失败');
      }

      return result;
    } catch (e) {
      ToastUtil.showToast('分享失败: $e');
      return false;
    } finally {
      // 清理临时文件
      if (tempFilePath != null) {
        await _cleanupTempFile(tempFilePath);
      }
    }
  }

  static Future<bool> wxPay({
    required String timestamp,
    required String packageValue,
    required String paySign,
    required String signType,
    required String partnerId,
    required String prepayId,
    required String nonceStr,
  }) async {
    try {
      if (!await _ensureWeChatReady()) {
        return false;
      }
      final payment = Payment(
        appId: _weChatAppId,
        partnerId: partnerId,
        prepayId: prepayId,
        packageValue: packageValue,
        nonceStr: nonceStr,
        timestamp: int.parse(timestamp),
        sign: paySign,
        signType: signType,
      );
      return _fluwx.pay(which: payment);
    } catch (e) {
      ToastUtil.showToast('支付失败: $e');
      return false;
    }
  }

  /// 微信SDK初始化
  static Future<bool> _ensureWeChatReady() async {
    try {
      // 检查并初始化微信SDK
      if (!_isInitialized) {
        final initResult = await initialize();
        if (!initResult) {
          ToastUtil.showToast('微信SDK初始化失败');
          return false;
        }
      }

      // 检查微信是否已安装
      final isInstalled = await checkWeChatInstalled();
      if (!isInstalled) {
        ToastUtil.showToast('请先安装微信');
        return false;
      }

      return true;
    } catch (e) {
      ToastUtil.showToast('微信检查失败');
      return false;
    }
  }

  /// 从URL下载文件到临时目录
  /// 返回本地文件路径，失败时返回null
  /// [customFileName] 自定义文件名
  static Future<String?> _downloadFileFromUrl(String url, {required String customFileName}) async {
    try {
      final dio = Dio();

      // 设置超时时间
      dio.options.connectTimeout = const Duration(seconds: 30);
      dio.options.receiveTimeout = const Duration(seconds: 30);

      // 获取临时目录
      final tempDir = await getTemporaryDirectory();

      // 使用传递的文件名
      final filePath = path.join(tempDir.path, customFileName);

      // 下载文件
      final response = await dio.download(url, filePath);

      if (response.statusCode == 200) {
        return filePath;
      } else {
        ToastUtil.showToast('文件下载失败');
        return null;
      }
    } catch (e) {
      ToastUtil.showToast('文件下载失败: $e');
      return null;
    }
  }

  /// 清理临时文件
  static Future<void> _cleanupTempFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      // 静默处理
    }
  }

  /// 从URL下载图片数据
  static Future<Uint8List?> _downloadImageFromUrl(String url) async {
    try {
      final dio = Dio();
      dio.options.connectTimeout = const Duration(seconds: 10);
      dio.options.receiveTimeout = const Duration(seconds: 10);

      final response = await dio.get(
        url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200 && response.data != null) {
        final imageData = Uint8List.fromList(response.data);

        // 微信要求不超过128KB
        if (imageData.length > 128 * 1024) {
          return null;
        }

        return imageData;
      }

      return null;
    } catch (e) {
      return null;
    }
  }
}
