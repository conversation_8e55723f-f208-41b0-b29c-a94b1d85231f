/// 记工相关工具
class WorklogUtil {
  /// 映射记录类型到显示文本
  static String? mappingRecordType(int? type) {
    switch (type) {
      case 1:
        return "点工";
      case 2:
        return "工量";
      case 3:
        return "短工";
      case 4:
        return "借支";
      case 5:
        return "支出";
      case 6:
        return "包工";
      case 7:
        return "小时工";
      case 8:
        return "收入";
      case 9:
        return "工资";
      case 10:
        return "其它费用";
      default:
        return null;
    }
  }
}
