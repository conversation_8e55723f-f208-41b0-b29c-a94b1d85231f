import 'dart:async';

import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/net_util/net_env.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/user_info/user_info_utils.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_service/net_resp_interceptor.dart';

import '../ui_util/toast_util.dart';

class NetInterceptorImpl implements NetRespInterceptor {
  @override
  void intercept(RespFail respFail, List<int>? focusErrCode) {
    print('respFail刘德华:${focusErrCode}');
    //具体的业务想要特殊处理错误码
    if (!(focusErrCode?.contains(respFail.code) ?? false)) {
      _handleError(respFail);
    }
  }

  @override
  void hideLoading() {
    ToastUtil.hideLoading();
  }

  @override
  void showLoading() {
    ToastUtil.showLoading();
  }

  _handleError(RespFail respFail) {
    print('respFail:${respFail.code}');
    if (respFail.code != null) {
      //通用错误处理
      if (respFail.code! > 0) {
        //后端有返回，只是标记了业务错误
        switch (respFail.code) {
          case BizError.BIZ_ERROR_UN_LOGIN:
            _handleUnLogin(respFail);
            break;
          default:
            ToastUtil.showToast("${respFail.errorMsg}");
            break;
        }
      } else {
        //非后端返回的业务错误，可能是网络问题或者客户端入参、解析等问题
        String errorMsg = respFail.errorMsg.isNullOrEmpty()?"接口返回异常":respFail.errorMsg!;
        switch (respFail.code) {
          case ERROR_TYPE.NET_ERROR:
            if (NetEnv().isReleaseMode()) {
              ToastUtil.showToast("网络异常，请稍后再试。");
            } else {
              ToastUtil.showToast(errorMsg);
            }
            break;
          case ERROR_TYPE.PRE_PARSE_ERROR:
          case ERROR_TYPE.JSON_PARSE_ERROR:
            if (NetEnv().isReleaseMode()) {
              ToastUtil.showToast("数据异常，请联系客服。");
            } else {
              ToastUtil.showToast(errorMsg);
            }
            break;
          default:
            ToastUtil.showToast(errorMsg);
            break;
        }
      }
    }
  }

  void _handleUnLogin(RespFail respFail) {
    final message = respFail.errorMsg.isNullOrEmpty()
        ? "登录状态已失效，请重新登录"
        : respFail.errorMsg!;
    ToastUtil.showToast(message);
    Future.microtask(() async {
      try {
        await UserInfoUtils.clearAllUserData();
        YPRoute.openPage(RouteNameCollection.login, clearStackAndPush: true);
      } catch (e) {
        YPRoute.openPage(RouteNameCollection.login, clearStackAndPush: true);
      }
    });
  }
}

class BizError {
  //test could delete if no need
  static const int BIZ_ERROR_CODE = 1000;
  static const int BIZ_ERROR_UN_LOGIN = 401;
  static const String BIZ_ERROR_MSG = "业务错误";
}
