import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../ui_util/colors_util.dart';

/// 按钮构建器
class ButtonBuilder {
  /// 构建一个填充的主题色按钮
  /// [text] 文本
  /// [onPressed] 按钮点击回调; 设置为 `null` 时应用禁用样式
  /// [fontSize] 默认为 `16.sp` 可以更改以适应不同大小的按钮
  /// [radius] 圆角默认 `4.w` 可以更改以适应不同大小的按钮
  static Widget buildPrimaryFilledButton({
    String? text,
    VoidCallback? onPressed,
    double? fontSize,
    double? radius,
  }) =>
      FilledButton(
        onPressed: onPressed,
        style: ButtonStyle(
          padding: WidgetStateProperty.all(
            EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
          ),
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(radius ?? 4.w),
            ),
          ),
          foregroundColor: WidgetStateProperty.resolveWith<Color>((states) =>
              states.contains(WidgetState.disabled)
                  ? ColorsUtil.black25
                  : Colors.white),
          backgroundColor: WidgetStateProperty.resolveWith<Color>((states) =>
              states.contains(WidgetState.disabled)
                  ? const Color(0XFFF0F0F0)
                  : ColorsUtil.ypPrimaryColor),
        ),
        child: Text(
          text ?? '',
          // Text 文本的颜色由 FilledButton 的 foregroundColor 决定
          style: TextStyle(fontSize: fontSize ?? 16.sp),
        ),
      );
}
