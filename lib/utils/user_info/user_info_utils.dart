import 'package:gdjg_pure_flutter/feature/tabbar/us/identity_us.dart';
import 'package:gdjg_pure_flutter/utils/store_util/kv_util.dart';
import 'package:gdjg_pure_flutter/utils/store_util/sp_keys.dart';

/// 用户信息工具类
class UserInfoUtils {
  UserInfoUtils._();

  /// 获取用户身份
  static UserIdentity getIdentity() {
    final savedIdentity = KVUtil.getKV().getInt(
      StoreKeys.key_user_identity_preference,
      defaultValue: 2
    );
    return UserIdentity.fromApiValue(savedIdentity);
  }

  /// 保存用户身份
  static Future<void> saveIdentity(UserIdentity identity) async {
    KVUtil.getKV().setInt(StoreKeys.key_user_identity_preference, identity.apiValue);
  }

  /// 检查是否有保存的身份偏好
  static bool hasIdentityPreference() {
    final savedIdentity = KVUtil.getKV().getInt(
      StoreKeys.key_user_identity_preference,
      defaultValue: 0
    );
    return savedIdentity != 0;
  }

  /// 获取用户数据JSON字符串
  static String? getUserDataJson() {
    return KVUtil.getKV("user_single").getString(StoreKeys.key_user_data_json);
  }

  /// 检查用户是否已登录
  static bool isLoggedIn() {
    final token = KVUtil.getKV("user_single").getString(StoreKeys.key_jgjz_token);
    return token?.isNotEmpty ?? false;
  }

  /// 清除所有用户数据
  static Future<void> clearAllUserData() async {
    final userKV = KVUtil.getKV("user_single");
    final defaultKV = KVUtil.getKV();

    // 清除用户账户信息
    userKV.mmkv.removeValue(StoreKeys.key_jgjz_token);
    userKV.mmkv.removeValue(StoreKeys.key_user_data_json);
    userKV.mmkv.removeValue(StoreKeys.key_user_data_tel);
    userKV.mmkv.removeValue(StoreKeys.key_user_data_is_new_member);

    // 清除用户身份偏好
    defaultKV.mmkv.removeValue(StoreKeys.key_user_identity_preference);
  }
}
