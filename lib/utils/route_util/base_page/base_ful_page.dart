import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/page_extra.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/page_lifecycle.dart';
import 'package:gdjg_pure_flutter/utils/route_util/observer/route_observer.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

//A页面打开B页面整体生命周期如下
//A onPageHide->B onPageCreate->B onPageShow->B onPageRoute-> B build

//B返回A
//A onReceiveFromLast->A onPageShow->B onPageHide->B onPageDestroy

typedef BackCloseCallback = bool Function();

//页面级别的Widget(可以路由）需要继承该基类，其他子View不作要求
abstract class BaseFulPage extends StatefulWidget {
  final YPAppBar? appBar;

  final bool canBack; //true 手势/物理返回关闭；false，拦截，不直接关闭
  final BackCloseCallback? backCloseCallback; //返回true，关闭当前页面
  const BaseFulPage({
    super.key,
    this.canBack = true,
    this.appBar = const YPAppBar(),
    this.backCloseCallback,
  });
}

//继承自BaseFulPageState，无需重写initState、didChangeDependencies、dispose 方法，可用PageLifecycle替代
abstract class BaseFulPageState<T extends BaseFulPage> extends State<T>
    with WidgetsBindingObserver, RouteAware, PageLifecycle, PageExtra {
  String _TAG = '';

  int exitAppClickTimes = 0;

  //用于外部动态设置标题
  String? dynamicTitle;

  //外部动态设置AppBar右侧点击事件
  VoidCallback? dynamicRightResTap;

  // 外部动态设置AppBar右侧文案
  String? dynamicRightResText;

  // 外部动态设置AppBar右侧图标IconFont
  IconFont? dynamicRightResIconFont;

  /// 自定义右侧视图
  Widget? dynamicRightWidget;

  Object? _tempBackData;

  @override
  void initState() {
    super.initState();
    _TAG = "yp_lifecycle ${runtimeType.toString()}";
    yprint("$_TAG initState");
    WidgetsBinding.instance.addObserver(this);
    onPageCreate();

    onPageRoute(YPRoute.getTopRoute()?.settings.arguments, false);

    pageRouteObserver.subscribe(this, YPRoute.getTopRoute()!);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    pageRouteObserver.unsubscribe(this);
    yprint("$_TAG dispose");
    onPageDestroy();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        yprint('$_TAG resumed');
        onPageShow();
        break;
      case AppLifecycleState.inactive:
        yprint('$_TAG inactive');
        break;
      case AppLifecycleState.paused:
        yprint('$_TAG paused');
        onPageHide();
        break;
      case AppLifecycleState.detached:
        yprint('$_TAG detached');
        break;
      case AppLifecycleState.hidden:
        yprint('$_TAG hidden');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (!didPop) {
            if (YPRoute.isDialogShowing()) {
              yprint("do nothing");
            } else {
              if (YPRoute.onlyOnePageExist()) {
                exitAppClickTimes++;
                if (exitAppClickTimes == 1) {
                  ToastUtil.showToast("再按一次退出程序");
                  Future.delayed(Duration(seconds: 2), () {
                    exitAppClickTimes = 0;
                  });
                } else if (exitAppClickTimes == 2) {
                  exitAppClickTimes = 0;
                  YPRoute.closePage();
                }
              } else {
                if (!widget.canBack) {
                  if (widget.backCloseCallback != null) {
                    bool needClosePage = widget.backCloseCallback!();
                    if (needClosePage) {
                      YPRoute.closePage(_getRealBackData());
                    }
                  } else {
                    final bool? intercept = callbackIntercept();
                    if(intercept != null){
                      if(intercept){
                        YPRoute.closePage(_getRealBackData());
                      }
                    }else{
                      //统一处理，弹窗
                      yprint("good");
                      YPRoute.closePage(_getRealBackData());

                    }
                  }
                } else {
                  YPRoute.closePage(_getRealBackData());
                }
              }
            }
          }
        },
        child: Container(
          decoration: getPageBackgroundDecoration(),
          child: _buildPage(context),
        ));
  }

  Widget _buildPage(BuildContext context) {
    if (widget.appBar != null) {
      String realTitle = dynamicTitle ?? widget.appBar?.title ?? "";
      String rightText = dynamicRightResText ?? widget.appBar?.rightResText ?? "";
      if (checkNoRightView()) {
        return Scaffold(
            resizeToAvoidBottomInset: false,
            backgroundColor: getPageBackgroundDecoration() == null ? Theme.of(context).scaffoldBackgroundColor : Colors.transparent,
            appBar: AppBarUtil.buildCommonAppBar(
                backgroundColor: getPageBackgroundDecoration() == null ? Colors.white : Colors.transparent,
                title: realTitle, onBackTap: () => YPRoute.closePage(_getRealBackData())),
            body: yBuild(context));
      } else {
        return Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: AppBarUtil.buildWithResourceWidget(
                title: realTitle,
                onBackTap: () => YPRoute.closePage(_getRealBackData()),
                resourceText: rightText,
                resourceIcon: widget.appBar?.rightResIcon,
                resourceIconFont: dynamicRightResIconFont ?? widget.appBar?.rightResIconFont,
                resourceBgColor: widget.appBar?.rightBackgroundColor,
                resourceTextColor: widget.appBar?.rightResTextColor,
                customWidget: dynamicRightWidget,
                onResourceTap: dynamicRightResTap ?? widget.appBar?.rightResTap),
            body: yBuild(context));
      }
    } else {
      return yBuild(context);
    }
  }

  /// 判断右侧视图是否不展示
  bool checkNoRightView() {
    return dynamicRightWidget == null &&
        dynamicRightResText == null &&
        dynamicRightResIconFont == null &&
        widget.appBar!.rightResIcon.isNullOrEmpty() == true &&
        widget.appBar!.rightResIconFont == null &&
        widget.appBar!.rightResText.isNullOrEmpty() == true &&
        dynamicRightResTap == null;
  }

  Widget yBuild(BuildContext context);

  @override
  void didPop() {
    //pop 返回页面时回调，比如B返回A，B会执行这个回调，B会销毁
    super.didPop();
    yprint("$_TAG didPop");
    onPageHide();
  }

  @override
  void didPush() {
    //push 进入页面时回调，比如A跳转到B，B回调执行这个方法
    super.didPush();
    yprint("$_TAG didPush");
    onPageShow();
  }

  @override
  void didPopNext() {
    //pop 返回页面时回调，比如B返回A，A回调执行这个方法，A执行后，B的生命周期开始动
    super.didPopNext();
    yprint("$_TAG didPopNext");
    if (YPRoute.validPageShow()) {
      Object? routeCache = YPRoute.getLaunchModeCache();
      if (routeCache != null) {
        onPageRoute(routeCache, true);
        YPRoute.clearLaunchModeCache();
      }

      onReceiveFromNext(YPRoute.getPopResult());
      onPageShow();
    }
  }

  @override
  void didPushNext() {
    //push 进入下一个页面回调，比如A跳转到B，A回调执行这个方法，先走A的生命周期
    super.didPushNext();
    yprint("$_TAG didPushNext");
    onPageHide();
  }

  @override
  void onPageCreate() {
    yprint("$_TAG onPageCreate");
  }

  @override
  void onPageShow() {
    yprint("$_TAG onPageShow");
  }

  @override
  void onPageHide() {
    yprint("$_TAG onPageHide");
  }

  @override
  void onPageDestroy() {
    yprint("$_TAG onPageDestroy");
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    yprint("$_TAG onPageRoute");
  }

  @override
  Object? onSend2Previous() {
    return null;
  }

  @override
  void onReceiveFromNext(Object? backData) {
    yprint("$_TAG onReceiveFromLast");
  }

  //以下是外部主动调用的方法=============开始

  //A页面跳转B页面，B页面点击某个按钮后才会把数据带给A页面，则调用这个方法
  //无论如何都会带数据给页面的话，则只需要重写onSend2Previous方法即可
  void setBackData(Object? backData) {
    _tempBackData = backData;
  }

  /// 动态更新页面标题
  /// 支持延迟设置标题，在API调用完成后调用此方法更新标题
  void updateDynamicTitle(String? title) {
    if (mounted) {
      setState(() {
        dynamicTitle = title;
      });
    }
  }

  /// 动态更新AppBar右侧文案
  void updateDynamicRightText(String? text) {
    if (mounted) {
      setState(() {
        dynamicRightResText = text;
      });
    }
  }

  /// 动态更新AppBar右侧点击事件
  void updateDynamicRightTap(VoidCallback? callback) {
    if (mounted) {
      setState(() {
        dynamicRightResTap = callback;
      });
    }
  }

  /// 动态更新AppBar右侧图标
  void updateDynamicRightIcon(IconFont? iconFont) {
    if (mounted) {
      setState(() {
        dynamicRightResIconFont = iconFont;
      });
    }
  }

  /// 动态更新AppBar右侧自定义视图
  void updateDynamicRightWidget(Widget? widget) {
    if (mounted) {
      setState(() {
        dynamicRightWidget = widget;
      });
    }
  }

  //以上是外部主动调用的方法=============结束

  Object? _getRealBackData() {
    return _tempBackData ?? onSend2Previous();
  }

  Decoration? getPageBackgroundDecoration() {
    return null;
  }
  bool? callbackIntercept() {
    return null;
  }
}

class YPAppBar {
  final String? title;
  final String? rightResIcon;

  /// 右侧视图图标允许使用 IconFont
  final IconFont? rightResIconFont;
  final String? rightResText;
  final VoidCallback? rightResTap;

  /// 右侧视图背景色
  final Color? rightBackgroundColor;

  /// 右侧视图文字颜色
  final Color? rightResTextColor;

  const YPAppBar({
    this.title,
    this.rightResIcon,
    this.rightResText,
    this.rightResTap,
    this.rightResIconFont,
    this.rightBackgroundColor,
    this.rightResTextColor,
  });
}
