import 'package:flutter/cupertino.dart';

class WidgetUtils {

  /// 给定一组Key, 计算这些widget的边界框
  /// 需要在Widget绘制后才能计算边界
  /// 针对没有绘制的Widget, 没有确切大小的Widget (SliverToBoxAdapter) 回略过当前的边界计算
  /// 如果没有任何一个Widget可以有效的测量,返回null
  ///
  static Rect? calculateBounding(List<GlobalKey> keys) {
    Rect? result;

    for (final key in keys) {
      final BuildContext? context = key.currentContext;
      if (context == null) continue;

      final renderObject = context.findRenderObject();
      if (renderObject is! RenderBox) continue;

      final Offset offset = renderObject.localToGlobal(Offset.zero);
      final Size size = renderObject.size;
      final rect = offset & size;

      result = result == null ? rect : result.expandToInclude(rect);
    }

    return result; // 如果所有 key 都无效，返回 null
  }
}
