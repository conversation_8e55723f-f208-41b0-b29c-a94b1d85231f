import 'package:flutter/material.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

class GuideUtils {
  static TutorialCoachMark createSingleFocusTutorialCoachMark(
    final Rect rect,
    final void Function() onFinish,
    final List<TargetContent> content,
  ) =>
      TutorialCoachMark(
        colorShadow: Colors.black,
        paddingFocus: 0,
        opacityShadow: 0.4,
        skipWidget: const SizedBox.shrink(),
        targets: [
          TargetFocus(
            targetPosition: TargetPosition(rect.size, rect.topLeft),
            shape: ShapeLightFocus.RRect,
            pulseVariation: Tween<double>(begin: 1.0, end: 1.0),
            focusAnimationDuration: Duration(milliseconds: 160),
            enableOverlayTab: false,
            contents: [
              ...content,
              _createFocusOverlay(rect, onFinish),
            ],
          ),
        ],
      );

  /// 创建一个完全覆盖Focus的透明层
  /// 点击后执行和点击下一步完全相同的逻辑
  /// 使用[TutorialCoachMark.onClickTarget] 可以实现几乎相同的功能, (设置[TargetFocus.enableOverlayTab] = true)
  /// 但是启用[TargetFocus.enableOverlayTab],点击Focus会关闭[TutorialCoachMark]
  /// 闭包内容是跳转页面时, 有时候不会关闭[TutorialCoachMark]
  /// 可以强制在闭包内调用[TutorialCoachMark.finish]解决, 但是逻辑很奇怪, 本来应该自动
  /// 关闭的, 这样处理看起来像是关闭了两次.
  /// 这里采用统一的叠加透明widget的方案进行统一处理
  static TargetContent _createFocusOverlay(
    final Rect rect,
    final void Function() onFinish,
  ) =>
      TargetContent(
        align: ContentAlign.custom,
        customPosition: CustomTargetContentPosition(
          top: rect.top,
          left: rect.left,
        ),
        padding: EdgeInsets.zero,
        child: GestureDetector(
          onTap: onFinish,
          behavior: HitTestBehavior.translucent,
          child: SizedBox(
            width: rect.width,
            height: rect.height,
          ),
        ),
      );
}
