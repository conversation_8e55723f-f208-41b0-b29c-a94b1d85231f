import 'package:gdjg_pure_flutter/utils/extensions/sentinel.dart';

extension ListExtensions<T> on List<T> {
  /// 列表分隔符
  /// 对于列表在项之间插入新的项; 需要提供一个方法[supplier]获取插入的项
  /// 同时支持 前缀[prefix]和后缀[suffix]
  List<T> separated(
    T Function() supplier, {
    Sentinel<T> prefix = const Undefined(),
    Sentinel<T> suffix = const Undefined(),
  }) {
    final List<T> result = [];

    if (isEmpty) {
      if (suffix is Defined<T>) {
        result.add(suffix.value);
      }
      if (prefix is Defined<T>) {
        result.add(prefix.value);
      }
      return result;
    }

    if (prefix is Defined<T>) {
      result.add(prefix.value);
    }

    for (int i = 0; i < length; i += 1) {
      result.add(this[i]);
      if (i < length - 1) {
        result.add(supplier());
      }
    }

    if (suffix is Defined<T>) {
      result.add(suffix.value);
    }

    return result;
  }
}
