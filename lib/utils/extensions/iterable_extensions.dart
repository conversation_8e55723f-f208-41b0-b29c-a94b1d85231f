extension IterableExtensions<T> on Iterable<T> {
  /// 找到最小值对应的元素，如果集合为空返回 null
  T? minOrNull(Comparable Function(T element) selector) {
    if (isEmpty) return null;

    var minElem = first;
    var minValue = selector(minElem);

    for (var elem in skip(1)) {
      final value = selector(elem);
      if (value.compareTo(minValue) < 0) {
        minElem = elem;
        minValue = value;
      }
    }
    return minElem;
  }

  /// 找到最大值对应的元素，如果集合为空返回 null
  T? maxOrNull(Comparable Function(T element) selector) {
    if (isEmpty) return null;

    var maxElem = first;
    var maxValue = selector(maxElem);

    for (var elem in skip(1)) {
      final value = selector(elem);
      if (value.compareTo(maxValue) > 0) {
        maxElem = elem;
        maxValue = value;
      }
    }
    return maxElem;
  }
}
