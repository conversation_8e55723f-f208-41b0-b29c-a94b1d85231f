import 'package:gdjg_pure_flutter/ad/repo/ad_repo.dart';
import 'package:gdjg_pure_flutter/generated/pigeons/navigation_api.dart';
import 'package:get/get.dart';

class AdInterstitialVM extends GetxController {
  var isShowInterstitial = false.obs;
  final _adConfigRepo = AdConfigRepo();
  final _api = CallNativeApi();

  @override
  void onInit() {
    super.onInit();
    ever(isShowInterstitial, (value) {
      print("=====value:$value");
      if (value == true) {
        // 调用原生方法 showInterstitialAd, 打开原生广告页面
        _api.showInterstitialAd();
      }
    });
  }

  /// 获取是否展示插屏广告
  void getLoadAdInterstitial(int identity) async {
    var res = await _adConfigRepo.getSettingList(identity: identity);
    if (res.isOK()) {
      var adPopRes = res.getSucData();
      print("AdConfig: main_page - onPageShow-adPopRes:$adPopRes");
      var popInter = adPopRes?.popInter ?? "";
      if (popInter == "1") {
        _api.loadInterstitialAd();
      }
    }
  }
}

class NativeAdVM extends GetxController {
  var vipTipContentText = "".obs;
  var vipTipPriceText = "".obs;
  var vipTipUnitText = "".obs;
  final _adConfigRepo = AdConfigRepo();

  /// 获取VIP相关配置
  void getVIPConfig(int identity) async {
    var res = await _adConfigRepo.getVIPConfig(identity: identity);
    if (res.isOK()) {
      final settingResp = res.getSucData();
      var mVipTip = settingResp?.vip?.vipTip;
      print("---- mVipTip:$mVipTip");
      // if (mVipTip == null || mVipTip.isEmpty) {
      //   vipTipLeftText.value = "去广告会员，限时特惠中";
      // } else {
      var tips = mVipTip!.split("@");
      if (tips.isNotEmpty) {
        vipTipContentText.value = tips[0];
      }
      if (tips.length > 1) {
        final priceText = double.tryParse(tips[1]);
        if (priceText != null) {
          vipTipPriceText.value = priceText.toString();
        }
      }
      if (tips.length > 2) {
        vipTipUnitText.value = tips[2];
      }
      // }
    }
  }
}
