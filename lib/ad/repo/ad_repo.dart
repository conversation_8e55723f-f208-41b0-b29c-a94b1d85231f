import 'package:net_plugin/pigeon/net_result/resp_result.dart';

import 'ad_rds.dart';
import 'model/ad_mediator_preload_biz_model.dart';
import 'model/setting_net_model_biz_model.dart';
import 'model/vip_status_biz_model.dart';

/// 广告配置仓库
class AdConfigRepo {
  final _rds = AdConfigRds();

  /// 获取是否加载插屏广告
  Future<RespResult<AdMediatorPreloadBizModel>> getSettingList({required int identity}) async {
    final result = await _rds.getLoadAdInterstitial(identity: identity.toString());
    return result.map((netModel) => netModel?.transform() ?? AdMediatorPreloadBizModel());
  }

  /// 获取VIP相关配置
  Future<RespResult<SettingNetModelBizModel>> getVIPConfig({required int identity}) async {
    final result = await _rds.getVIPConfig(identity: identity.toString());
    return result.map((netModel) => netModel?.transform() ?? SettingNetModelBizModel());
  }

  /// 获取VIP相关配置
  Future<RespResult<VipStatusBizModel>> getVIPStatus() async {
    final result = await _rds.getVIPStatus();
    return result.map((netModel) => netModel?.transform() ?? VipStatusBizModel());
  }
}
