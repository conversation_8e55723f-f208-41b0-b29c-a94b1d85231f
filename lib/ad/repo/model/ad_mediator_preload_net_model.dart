import 'ad_mediator_preload_biz_model.dart';

class AdMediatorPreloadNetModel {
  /// 是否立刻预加载插屏弹窗 1-加载
  String? pop_inter;

  AdMediatorPreloadNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (pop_inter != null) map["pop_inter"] = pop_inter!;
    return map;
  }

  factory AdMediatorPreloadNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = AdMediatorPreloadNetModel();
    netModel.pop_inter = json["pop_inter"].toString();
    return netModel;
  }

  AdMediatorPreloadBizModel transform() {
    return AdMediatorPreloadBizModel(
      popInter: pop_inter ?? "",
    );
  }
}
