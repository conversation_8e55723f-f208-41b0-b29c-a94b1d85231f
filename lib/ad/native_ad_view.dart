import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/ad/ad_native_vm.dart';
import 'package:gdjg_pure_flutter/generated/pigeons/native_ad_api.dart';
import 'package:gdjg_pure_flutter/utils/common/pigeon_flutter_api.dart';
import 'package:get/get.dart';

class NativeAdView extends StatefulWidget {
  final Map<String, dynamic> params;
  final AdNativeVM adNativeVM;
  const NativeAdView(this.params, this.adNativeVM, {super.key});

  @override
  State<NativeAdView> createState() => _NativeAdViewState();
}

class _NativeAdViewState extends State<NativeAdView> {
  AdNativeVM get adVM => widget.adNativeVM;

  @override
  void initState() {
    super.initState();
    print("AdConfig, NativeAdView==initState");
  }

  @override
  void dispose() {
    print("AdConfig, NativeAdView==dispose");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const String viewType = 'native_ad_view';
    final adLocation = widget.params["adLocation"];
    final isMonthFlowAd = adLocation == AdConst.monthFlow;
    final isTabCalendar = adLocation == AdConst.tabCalendar;

    return Obx(() {
      final height = adVM.mNativeAdHeight.value;
      print("AdConfig, NativeAdView==$height");

      // 高度为 0 时不渲染 PlatformView，避免不必要的创建
      if (height <= 0) {
        return const SizedBox.shrink();
      }

      return Container(
        color: Colors.transparent,
        margin: EdgeInsets.only(top: isMonthFlowAd ? 8.0 : 0.0, bottom: isTabCalendar ? 8.0 : 0.0),
        height: height,
        child: _buildPlatformView(viewType),
      );
    });
  }

  Widget _buildPlatformView(String viewType) {
    // return AndroidView(
    //   viewType: viewType,
    //   layoutDirection: TextDirection.ltr,
    //   creationParams: widget.params,
    //   creationParamsCodec: const StandardMessageCodec(),
    // );
    return PlatformViewLink(
      viewType: viewType,
      surfaceFactory: (context, controller) {
        return AndroidViewSurface(
          controller: controller as AndroidViewController,
          gestureRecognizers: const <Factory<OneSequenceGestureRecognizer>>{},
          hitTestBehavior: PlatformViewHitTestBehavior.opaque,
        );
      },
      onCreatePlatformView: (params) {
        return PlatformViewsService.initSurfaceAndroidView(
          id: params.id,
          viewType: viewType,
          layoutDirection: TextDirection.ltr,
          creationParams: widget.params,
          creationParamsCodec: const StandardMessageCodec(),
          onFocus: () {
            params.onFocusChanged(true);
          },
        )
          ..addOnPlatformViewCreatedListener(params.onPlatformViewCreated)
          ..create();
      },
    );
  }
}
