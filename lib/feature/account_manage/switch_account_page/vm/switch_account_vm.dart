import "package:gdjg_pure_flutter/data/account/repo/auth_repo.dart";
import "package:gdjg_pure_flutter/data/account_manage_data/ds/model/param/decrypt_tel_param_model.dart";
import "package:gdjg_pure_flutter/data/account_manage_data/repo/account_manage_repo.dart";
import "package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_create_dept_req_entity.dart";
import "package:gdjg_pure_flutter/feature/account/login/code_login/entity/login_props.dart";
import "package:gdjg_pure_flutter/feature/account_manage/switch_account_page/vm/switch_account_us.dart";
import "package:gdjg_pure_flutter/init_module/init_route.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:gdjg_pure_flutter/utils/system_util/yprint.dart";
import "package:get/get.dart";

/// 切换账号
class SwitchAccountVM extends GetxController {
  final _repo = AccountManageRepo();
  final _authRepo = AuthRepo();
  final us = SwitchAccountListUS();

  /// 初始化数据
  void init() {
    us.setCurrentPhone(maskPhoneNumber(_authRepo.getAccount().tel));
    _loadHistoryPhones();
  }
  String maskPhoneNumber(String phone) {
    if (phone.length < 11) return phone; // 确保是11位手机号

    // 取前3位和后4位，中间用****填充
    return '${phone.substring(0, 3)}*****${phone.substring(8)}';
  }

  /// 加载历史手机号数据
  Future<void> _loadHistoryPhones() async {
    try {
      final result = await _repo.getSwitchPhoneList();
      if (result.isOK()) {
        final data = result.success?.data?.list;
        if (data != null) {
          final phonesList = data
              .map((phone) => SwitchAccountUS()
                ..setPhone(phone.tel)
                ..setPhoneHash(phone.telHash))
              .toList();
          if (phonesList.isNotEmpty) {
            us.setSwitchPhoneList(phonesList);
          }
        }
      }
    } catch (e) {
      yprint(e.toString());
    }
  }

  Future<void> onSelectLoginTap(String hashPhone) async {
    var result = await _repo.decryptTel(DecryptTelParamModel(
      hash_tel: hashPhone,
    ));
    if (result.isOK()) {
      final phone = result.success?.data?.tel ?? '';
      YPRoute.openPage(RouteNameCollection.login,
          params: LoginProps(phone: phone));
    }
  }
}
