import "package:gdjg_pure_flutter/data/account_manage_data/repo/account_manage_repo.dart";
import "package:gdjg_pure_flutter/feature/account_manage/history_phone_page/vm/history_phone_us.dart";
import "package:gdjg_pure_flutter/utils/system_util/yprint.dart";

/// 历史手机号ViewModel
class HistoryPhoneVM {
  final _repo = AccountManageRepo();
  final us = HistoryPhoneListUS();

  void init() {
    _loadHistoryPhones();
  }

  /// 加载历史手机号数据
  Future<void> _loadHistoryPhones() async {
    try {
      final result = await _repo.getRelatedTel();
      // var list = [
      //   "155*****444(当前号码)",
      //   "180*****111",
      //   "188*****406",
      //   "136*****559",
      //   "161*****686",
      //   "133*****996",
      //   "146*****764",
      //   "197*****466"
      // ];
      var list = result.success?.data?.list;
      if (result.isOK() && list != null) {
        final phones = list
            .map((phone) => HistoryPhoneUS()..setCurrentPhone(phone))
            .toList();
        if (phones.isNotEmpty) {
          us.setHistoryPhones(phones);
        }
        us.setIsFinishInit(true);
      }
    } catch (e) {
      us.setIsFinishInit(false);
      yprint(e.toString());
    }
  }
}
