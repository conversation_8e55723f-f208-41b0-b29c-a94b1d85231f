import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/data/account/repo/auth_repo.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/ds/model/param/member_send_code_param_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/ds/model/param/member_update_info_param_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/repo/account_manage_repo.dart';
import 'package:gdjg_pure_flutter/feature/account_manage/change_bind_phone/vm/change_bind_phone_us.dart';
import 'package:gdjg_pure_flutter/utils/regex/regex_utils.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 换绑手机号ViewModel
class ChangeBindPhoneVM {
  final us = ChangeBindPhoneUS();
  final _authRepo = AuthRepo();
  final _repo = AccountManageRepo();

  /// 初始化页面数据
  Future<void> init() async {
    try {
      final currentPhone = _authRepo.getAccount().tel;
      us.setCurrentPhone(currentPhone);
    } catch (e) {
      us.setCurrentPhone('');
    }
  }

  /// 发送验证码
  Future<void> sendCode(String newPhone) async {
    if (us.isSendingCode) return;
    try {
      us.setIsSendingCode(true);
      us.setNewPhone(newPhone);
      var res = await _repo.sendCode(MemberSendCodeParamModel(tel: newPhone));
      if (res.fail?.errorMsg != null) {
        ToastUtil.showToast("${res.fail?.errorMsg}");
      }
    } finally {
      us.setIsSendingCode(false);
    }
  }

  /// 更新手机号
  Future<bool> updatePhoneNumber(String phone, String code) async {
    if (us.isSubmitting) return false;
    try {
      us.setIsSubmitting(true);
      var res = await _repo.updatePhoneNumber(
        MemberUpdateInfoParamModel(
          type: 'tel',
          username: _authRepo.getAccount().username,
          tel: phone,
          code: code,
        ),
      );
      if (res.isOK()) {
        ToastUtil.showToast('修改成功');
        return true;
      } else {
        ToastUtil.showToast("${res.fail?.errorMsg}");
        return false;
      }
    } catch (e) {
      return false;
    } finally {
      us.setIsSubmitting(false);
    }
  }

  /// 验证表单状态
  bool isFormValid(TextEditingController phoneController,
      TextEditingController codeController) {
    final phone = phoneController.text;
    final code = codeController.text;
    // 验证手机号
    if (!RegexUtils.isMobile(phone)) return false;

    // 验证验证码（4位数字）
    if (!RegexUtils.isVerificationCode(code)) return false;

    return true;
  }
}
