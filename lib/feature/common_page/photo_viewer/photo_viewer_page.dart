import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:gdjg_pure_flutter/feature/common_page/photo_viewer/photo_viewer_props.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';

/// 图片查看器页面
class PhotoViewerPage extends BaseFulPage {
  const PhotoViewerPage({super.key}) : super(appBar: null);

  @override
  State createState() => _PhotoViewerPageState();
}

class _PhotoViewerPageState extends BaseFulPageState {
  late PhotoViewerProps _props;
  late PageController _pageController;
  late int _currentIndex;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    _props = routeParams as PhotoViewerProps;
    _currentIndex = _props.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          PhotoViewGallery.builder(
            scrollPhysics: const BouncingScrollPhysics(),
            builder: (BuildContext context, int index) {
              return PhotoViewGalleryPageOptions(
                imageProvider: NetworkImage(_props.imageUrls[index]),
                initialScale: PhotoViewComputedScale.contained,
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 2.0,
                heroAttributes: null,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Colors.black,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image,
                            color: Colors.white54,
                            size: 64.w,
                          ),
                          SizedBox(height: 16.h),
                          Text(
                            '图片加载失败',
                            style: TextStyle(
                              color: Colors.white54,
                              fontSize: 16.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },

              );
            },
            itemCount: _props.imageUrls.length,
            backgroundDecoration: const BoxDecoration(color: Colors.black),
            pageController: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentIndex = index;
              });
            },
          ),
          
          // 顶部导航栏
          _buildTopBar(),
        ],
      ),
    );
  }

  /// AppBar
  Widget _buildTopBar() {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.only(
          top: MediaQuery.of(context).padding.top,
          left: 16.w,
          right: 16.w,
          bottom: 16.h,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black.withValues(alpha: 0.7),
              Colors.transparent,
            ],
          ),
        ),
        child: Row(
          children: [
            GestureDetector(
              onTap: () => YPRoute.closePage(),
              child: Container(
                padding: EdgeInsets.all(8.w),
                child: Image.asset(
                  Assets.commonIconArrowBack,
                  width: 24.w,
                  height: 24.w,
                  color: Colors.white,
                ),
              ),
            ),
            
            // Title
            Expanded(
              child: Center(
                child: Column(
                  children: [
                    if (_props.title != null) ...[
                      Text(
                        _props.title!,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 4.h),
                    ],
                    Text(
                      '${_currentIndex + 1}/${_props.imageUrls.length}',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14.sp,
                      ),
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(width: 40.w),
          ],
        ),
      ),
    );
  }
}
