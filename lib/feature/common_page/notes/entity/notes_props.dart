/// @date 2025/07/07
/// @description Notes页面来源枚举
enum NotesPageSource {
  /// 工资流水页面
  groupEditRecordWork('group_edit_record_work'),

  /// 其他
  other('other');

  const NotesPageSource(this.value);

  final String value;

  /// 根据字符串值获取枚举
  static NotesPageSource fromString(String? value) {
    for (NotesPageSource source in NotesPageSource.values) {
      if (source.value == value) {
        return source;
      }
    }
    return NotesPageSource.other;
  }
}

/// @date 2025/07/07
/// @description Notes页入参
class NotesProps {
  NotesPageSource? pageSource; // 页面来源枚举
  String? value; // 默认文案

  NotesProps({this.pageSource, this.value});
}
