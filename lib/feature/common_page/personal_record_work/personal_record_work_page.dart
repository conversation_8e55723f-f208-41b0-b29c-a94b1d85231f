import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/common_page/personal_record_work/vm/personal_record_work_vm.dart';
import 'package:gdjg_pure_flutter/feature/common_page/personal_record_work/point_work/point_work_tab.dart';
import 'package:gdjg_pure_flutter/feature/common_page/personal_record_work/contract_work/contract_work_tab.dart';
import 'package:gdjg_pure_flutter/feature/common_page/personal_record_work/short_work/short_work_tab.dart';
import 'package:gdjg_pure_flutter/feature/common_page/personal_record_work/work_quantity/work_quantity_tab.dart';
import 'package:gdjg_pure_flutter/feature/common_page/personal_record_work/other_fees/other_fees_tab.dart';
import 'package:gdjg_pure_flutter/feature/new_user_guide/config/guide_step_ui_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/guide/worker_guide_provider.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:get/get.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../../../utils/system_util/yprint.dart';
import '../../../utils/ui_util/widget_util.dart';
import '../../new_user_guide/new_user_guide_mixin.dart';

/// 个人记工页面
class PersonalRecordWorkPage extends BaseFulPage {
  const PersonalRecordWorkPage({super.key})
      : super(appBar: const YPAppBar(title: "个人记工"), canBack: false);

  @override
  State<PersonalRecordWorkPage> createState() => _PersonalRecordWorkPageState();
}

class _PersonalRecordWorkPageState
    extends BaseFulPageState<PersonalRecordWorkPage>
    with SingleTickerProviderStateMixin, NewUserGuideMixin {
  late PersonalRecordWorkVM vm;
  late TabController _tabController;
  final GlobalKey _globalKeyTab = GlobalKey();
  final GlobalKey _globalKeyWorkTime = GlobalKey();
  final GlobalKey aa = GlobalKey();

  TutorialCoachMark? _tutorialCoachMarkStep2;
  TutorialCoachMark? _tutorialCoachMarkStep3;

  final GlobalKey _worktimeKey = GlobalKey();
  final GlobalKey _overtimeKey = GlobalKey();

  @override
  void onPageCreate() {
    super.onPageCreate();
    _tabController = TabController(length: 5, vsync: this);
    // 调用接口获取参数
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 可选：在合适的时机设置外部引导步骤
      // setExternalGuideSteps([
      //   GuideTarget(type: GuideStepType.guideWorkTime, targetKey:_globalKeyWorkTime ),
      // ]);
      updateGuideShowState(true);
    });
  }

  @override
  void onGuideFinished(bool isSuccess) {
    yprint("====检查是否还有步骤需要引导=${isSuccess}===");
    super.onGuideFinished(isSuccess);
  }

  @override
  List<GuideTargetUIModel> getGuideSteps() {
    return [
      // GuideTargetUIModel(
      //     stepType: GuideStepType.guideRecordBar, globalKey: _globalKeyTab),
      GuideTargetUIModel(
          stepType: GuideStepType.guideWorkTime, globalKey: _globalKeyWorkTime),
    ];
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    double? deptId;
    if (routeParams != null) {
      final params = routeParams as Map<String, dynamic>?;
      if (params != null && params['deptId'] != null) {
        final deptIdValue = params['deptId'];
        deptId = deptIdValue;
      }
    }
    vm = PersonalRecordWorkVM(deptId: deptId);
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      checkGuideStep2();
    });
  }

  @override
  bool? callbackIntercept() =>
      _tutorialCoachMarkStep2?.isShowing != true &&
      _tutorialCoachMarkStep3?.isShowing != true;

  Future<void> checkGuideStep2() async {
    if (await vm.checkGuideStep2()) {
      _showTutorialStep2();
    } else {
      checkGuideStep3();
    }
  }

  Future<void> checkGuideStep3() async {
    if (_tabController.index == 0 && await vm.checkGuideStep3()) {
      _showTutorialStep3();
    }
  }

  void _showTutorialStep2() {
    final Rect? rect = WidgetUtils.calculateBounding([_globalKeyTab]);
    if (rect == null) {
      return;
    }
    _tutorialCoachMarkStep2?.finish();
    _tutorialCoachMarkStep2 = WorkerGuideProvider.showStep2(
      context,
      rect,
      () {
        _tutorialCoachMarkStep2?.finish();
        checkGuideStep3();
      },
    );
    vm.completeGuideStep2();
  }

  void _showTutorialStep3() {
    final Rect? rect = WidgetUtils.calculateBounding(
      [
        _worktimeKey,
        _overtimeKey,
      ],
    );
    if (rect == null) {
      return;
    }
    _tutorialCoachMarkStep3?.finish();
    _tutorialCoachMarkStep3 = WorkerGuideProvider.showStep3(
      context,
      rect,
      () => _tutorialCoachMarkStep3?.finish(),
    );
    vm.completeGuideStep3();
  }

  @override
  void onPageDestroy() {
    _tabController.dispose();
    vm.dispose();
    super.onPageDestroy();
  }

  @override
  Widget yBuild(BuildContext context) {
    final content = Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 5.h),
          _buildDateSection(),
          SizedBox(height: 2.h),
          _buildProjectSection(),
          SizedBox(height: 8.h),
          Expanded(child: _buildWorkTypeTabSection()),
        ],
      ),
    );
    // 使用混入提供的方法构建包含蒙层的widget树
    return buildWithGuideOverlay(content);
  }

  /// 构建日期选择区域
  Widget _buildDateSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      color: const Color(0xFFFFFFFF),
      child: GestureDetector(
        onTap: () {},
        child: Row(
          children: [
            Text(
              '日期：',
              style: TextStyle(fontSize: 18.sp, color: const Color(0xFF000000)),
            ),
            Obx(() => Text(
                  vm.date.isEmpty ? '' : vm.date,
                  style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF000000),
                      fontWeight: FontWeight.w800),
                )),
            const Spacer(),
            Text(
              '可多选',
              style: TextStyle(fontSize: 14.sp, color: const Color(0xFF999999)),
            ),
            SizedBox(width: 4.w),
            Icon(Icons.arrow_forward_ios,
                size: 14.sp, color: const Color(0xFF999999)),
          ],
        ),
      ),
    );
  }

  /// 构建项目区域
  Widget _buildProjectSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      color: const Color(0xFFFFFFFF),
      child: Row(
        children: [
          Text(
            '项目：',
            style: TextStyle(fontSize: 18.sp, color: const Color(0xFF000000)),
          ),
          Obx(() => Text(
                vm.projectId.isEmpty ? '' : vm.projectId,
                style: TextStyle(
                    fontSize: 18.sp,
                    color: const Color(0xFF000000),
                    fontWeight: FontWeight.w800),
              )),
        ],
      ),
    );
  }

  /// 构建工种TabBar区域
  Widget _buildWorkTypeTabSection() {
    return Column(
      children: [
        Container(
          key: _globalKeyTab,
          width: double.infinity,
          color: const Color(0xFFFFFFFF),
          child: TabBar(
            controller: _tabController,
            dividerColor: const Color(0x00000000),
            tabs: vm.workTypes.map((type) => Tab(text: type)).toList(),
            labelColor: const Color(0xFF000000),
            unselectedLabelColor: const Color(0xFF000000),
            labelPadding: EdgeInsets.symmetric(horizontal: 2.w),
            labelStyle: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w400,
            ),
            indicatorColor: const Color(0xFF5290FD),
            indicatorSize: TabBarIndicatorSize.label,
            onTap: vm.selectWorkType,
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              PointWorkTab(
                worktimeKey: _worktimeKey,
                overtimeKey: _overtimeKey,
              ),
              ContractWorkTab(),
              ShortWorkTab(),
              WorkQuantityTab(),
              OtherFeesTab(),
            ],
          ),
        ),
      ],
    );
  }
}
