import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/common_page/personal_record_work/point_work/vm/point_work_vm.dart';
import 'package:get/get.dart';

/// 点工Tab页面
class PointWorkTab extends StatefulWidget {
  final GlobalKey worktimeKey;
  final GlobalKey overtimeKey;

  const PointWorkTab({
    super.key,
    required this.worktimeKey,
    required this.overtimeKey,
  });

  @override
  State<PointWorkTab> createState() => _PointWorkTabState();
}

class _PointWorkTabState extends State<PointWorkTab>
    with AutomaticKeepAliveClientMixin {
  late PointWorkVM vm;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    vm = PointWorkVM();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return Column(
      children: [
        // 上班状态选择区域
        _buildWorkStatusSection(),
        // 加班选择区域
        _buildOvertimeSection(),
        // 工资显示区域
        _buildSalarySection(),
        // 照片上传区域
        _buildPhotoSection(),
        // 备注区域
        _buildRemarkSection(),
        // 确认按钮
        _buildConfirmButton(),
      ],
    );
  }

  /// 上班状态选择区域
  Widget _buildWorkStatusSection() {
    return Container(
      key: widget.worktimeKey,
      width: double.infinity,
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '上班：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF000000),
                ),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildWorkTimeButton(0, null),
                    _buildWorkTimeButton(1, '半个工'),
                    _buildWorkTimeButton(2, '选小时'),
                    _buildWorkTimeButton(3, '休息'),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              SizedBox(width: 48.w),
              _buildWorkTimeButton(4, '上下午'),
              const Spacer(flex: 6),
            ],
          ),
        ],
      ),
    );
  }

  /// 工作时长按钮
  Widget _buildWorkTimeButton(int index, String? text) {
    return Obx(() {
      final isSelected = vm.us.selectedWorkTimeIndex == index;

      // 1个工和选小时按钮根据选择状态显示，其他按钮显示固定文本
      String displayText;
      if (index == 0) {
        displayText = vm.us.workHoursButtonText;
      } else if (index == 2) {
        displayText = vm.us.selectedHoursButtonText;
      } else {
        displayText = text!;
      }

      return GestureDetector(
        onTap: () {
          vm.selectWorkTimeButton(index);

          // 根据按钮类型显示对应弹窗
          if (index == 0) {
            vm.showWorkHoursKeyboard(context);
          } else if (index == 2) {
            vm.showHourSelectionDialog(context);
          } else if (index == 4) {
            vm.showTimeSlotDialog();
          }
        },
        child: IntrinsicWidth(
          child: Container(
            height: 34.h,
            constraints: BoxConstraints(minWidth: 50.w),
            decoration: BoxDecoration(
              color: isSelected
                  ? const Color(0xFF5290FD)
                  : const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(2.r),
            ),
            child: Center(
              child: index == 0
                  ? Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            displayText,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: isSelected
                                  ? Colors.white
                                  : const Color(0xFF000000),
                            ),
                            maxLines: 1,
                          ),
                          SizedBox(width: 2.w),
                          Icon(
                            Icons.border_color,
                            size: 14.sp,
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFF000000),
                          ),
                        ],
                      ),
                    )
                  : Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      child: Text(
                        displayText,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: isSelected
                              ? Colors.white
                              : const Color(0xFF000000),
                        ),
                        maxLines: 1,
                      ),
                    ),
            ),
          ),
        ),
      );
    });
  }

  /// 加班选择区域
  Widget _buildOvertimeSection() {
    return Container(
      key: widget.overtimeKey,
      width: double.infinity,
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 10.h),
      color: Colors.white,
      child: Row(
        children: [
          Text(
            '加班：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          Expanded(
            child: _buildOvertimeButton(),
          ),
          const Spacer(),
          const Spacer(),
          const Spacer(),
        ],
      ),
    );
  }

  Widget _buildOvertimeButton() {
    return Obx(() {
      final hasOvertime = vm.us.hasOvertime;
      return GestureDetector(
        onTap: () {
          vm.showOvertimeDialog();
        },
        child: Container(
          height: 34.h,
          decoration: BoxDecoration(
            color:
                hasOvertime ? const Color(0xFF5290FD) : const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(2.r),
          ),
          child: Center(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '无加班',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: hasOvertime ? Colors.white : const Color(0xFF000000),
                  ),
                ),
                SizedBox(width: 4.w),
                Icon(
                  Icons.border_color,
                  size: 14.sp,
                  color: hasOvertime ? Colors.white : const Color(0xFF000000),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  /// 工资设置区域
  Widget _buildSalarySection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '工资：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF000000),
                ),
              ),
              Obx(() => Text(
                    '${vm.us.salary.toStringAsFixed(1)}元',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF000000),
                      fontWeight: FontWeight.bold,
                    ),
                  )),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  vm.showSalaryCalculationDialog();
                },
                child: Row(
                  children: [
                    Text(
                      '设置工价',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF999999),
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16.sp,
                      color: const Color(0xFF999999),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 照片上传区域
  Widget _buildPhotoSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '照片：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF000000),
                ),
              ),
              GestureDetector(
                onTap: () {
                  // TODO: 律师提醒弹窗
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2.r),
                    border: Border.all(
                      color: const Color(0xFF5290FD),
                      width: 1.w,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.play_circle_outline,
                        size: 14.sp,
                        color: const Color(0xFF5290FD),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        '律师提醒',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFF5290FD),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 5.h),
          Row(
            children: [
              Text(
                '照片：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.transparent,
                ),
              ),
              Text(
                '上传工作照片，留证据，工资有保障',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: const Color(0xFF5290FD),
                ),
              ),
            ],
          ),
          SizedBox(height: 5.h),
          Row(
            children: [
              Text(
                '照片：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Colors.transparent,
                ),
              ),
              GestureDetector(
                onTap: () {
                  vm.showPhotoSelectionDialog();
                },
                child: Container(
                  width: 80.w,
                  height: 80.w,
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F5),
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: const Color(0xFFE0E0E0),
                      width: 1.w,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.file_upload_outlined,
                        size: 24.sp,
                        color: const Color(0xFF999999),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '添加照片/视频',
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: const Color(0xFF999999),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建备注区域
  Widget _buildRemarkSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        children: [
          Text(
            '备注：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                // TODO: 跳转到备注编辑页面
              },
              child: Container(
                height: 90.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(2.r),
                ),
                child: Padding(
                  padding: EdgeInsets.only(left: 12.w, top: 6.h),
                  child: Text(
                    vm.us.remark.isEmpty ? '请输入备注...' : vm.us.remark,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: vm.us.remark.isEmpty
                          ? const Color(0xFF999999)
                          : const Color(0xFF000000),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建确认按钮
  Widget _buildConfirmButton() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: GestureDetector(
        onTap: () {
          vm.confirmSubmit();
        },
        child: Container(
          height: 48.h,
          decoration: BoxDecoration(
            color: const Color(0xFF5290FD),
            borderRadius: BorderRadius.circular(24.r),
          ),
          child: Center(
            child: Text(
              '确认',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
