
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/test/view/test_item_card.dart';
import 'package:gdjg_pure_flutter/feature/test/vm/test_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';

class JGTestPage extends BaseFulPage {
  const JGTestPage({super.key});

  @override
  State createState() => _JGTestPage();
}
class _JGTestPage extends BaseFulPageState {

  TestVM vm = TestVM();
  @override
  void initState() {
    super.initState();
    vm.queryProjectList();
  }


  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
        body: Column(
          children: [
            Row(
              children: [
                SizedBox(width: 10),
                IconFont(IconNames.saasAddProject),
                SizedBox(width: 10),
                IconFont(IconNames.saasAIconxiangongchengguanli, size: 24),
                SizedBox(width: 10),
                IconFont(IconNames.saasBianzu10, size: 24, color: '#ff0000'),
                SizedBox(width: 10),
                IconFont(IconNames.saasEyeOpen, size: 24, color: '#ff0000'),
              ],
            ),
            ElevatedButton(
              onPressed: () {
                YPRoute.openPage(RouteNameCollection.login);
              },
              child: Text('刷新'),
            ),
            ElevatedButton(
              onPressed: () {
                YPRoute.openPage(RouteNameCollection.personalRecordWorkPoints);
              },
              child: Text('个人记工'),
            ),
            ElevatedButton(
              onPressed: () {
                YPRoute.openPage(RouteNameCollection.buyVipPage);
              },
              child: Text('VIP'),
            ),
          ],
        )
    );
  }
}
