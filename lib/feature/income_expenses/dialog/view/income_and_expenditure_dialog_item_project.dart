import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

import '../../ui_model/annual_incomes_pend_dept_ui_model.dart';
import '../income_spend_select_pro_dialog.dart';

/// 项目选择卡片组件
///
/// 项目项组件
class IncomeAndExpenditureDialogItemProject extends StatelessWidget {
  final AnnualIncomeSpendDeptUIModel info;
  final AnnualIncomeSpendDeptUIModel selectInfo;
  final VoidCallback onTap;

  const IncomeAndExpenditureDialogItemProject({
    super.key,
    required this.info,
    required this.selectInfo,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        child: Row(
          children: [
            // 左侧状态标签
            _buildStatusBadge(),
            SizedBox(width: 8.w),
            // 中间项目信息
            Expanded(child: _buildProjectInfo()),
            // 右侧选中图标
            if (selectInfo.deptId==info.deptId && selectInfo.name==info.name)
              Icon(
                Icons.check,
                color: Colors.blue,
                size: 20.w,
              ),
          ],
        ),
      ),
    );
  }

  /// 构建状态标签
  Widget _buildStatusBadge() {
    final bool isActive = info.status == 1;
    final Color backgroundColor =
        isActive ? const Color(0x1A5290FD) : const Color(0xFFF0F0F0);
    final Color textColor =
        isActive ? const Color(0xFF5290FD) : const Color(0xFF666666);
    final String text = isActive ? "在场" : "已结清";

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(4.r),
      ),
      padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 8.h),
      child: Text(
        text,
        style: TextStyle(
          color: textColor,
          fontSize: 12.sp,
        ),
      ),
    );
  }

  /// 构建项目信息文本
  Widget _buildProjectInfo() {
    final Color textColor =
        info.isSelected ? const Color(0xFF5290FD) : const Color(0xFF323232);
    final String prefix = info.identity == 1 ? "（班组）" : "（个人）";

    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: "$prefix ",
            style: TextStyle(
              color: textColor,
              fontSize: 16.sp,
            ),
          ),
          TextSpan(
            text: info.name,
            style: TextStyle(
              color: textColor,
              fontSize: 15.sp,
            ),
          ),
        ],
      ),
    );
  }
}
