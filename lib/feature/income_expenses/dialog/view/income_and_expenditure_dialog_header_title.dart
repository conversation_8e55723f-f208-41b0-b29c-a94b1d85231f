import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 年份选择弹窗的头部组件
class IncomeAndExpenditureDialogHeaderTitle extends StatelessWidget {
  final VoidCallback onClose;
  final VoidCallback? onClear;
  final String title;
  final bool showClearButton;

  const IncomeAndExpenditureDialogHeaderTitle({
    super.key,
    required this.title,
    required this.onClose,
    this.onClear,
    this.showClearButton = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 44.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Stack(
        children: [
          _buildCloseButton(),
          _buildTitle(),
          if (showClearButton) _buildClearButton(),
        ],
      ),
    );
  }

  /// 构建左侧关闭按钮
  Widget _buildCloseButton() {
    return Align(
      alignment: Alignment.centerLeft,
      child: GestureDetector(
        onTap: onClose,
        child: Image.asset(
          'assets/images/common/icon_close.webp',
          width: 20.w,
          height: 20.h,
        ),
      ),
    );
  }

  /// 构建中间标题（绝对居中）
  Widget _buildTitle() {
    return Positioned.fill(
      child: Center(
        child: Text(
          title,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF323233),
          ),
        ),
      ),
    );
  }

  /// 构建右侧清空按钮
  Widget _buildClearButton() {
    return Align(
      alignment: Alignment.centerRight,
      child: GestureDetector(
        onTap: onClear,
        child: Text(
          '清空选择',
          style: TextStyle(
            fontSize: 15.sp,
            color: const Color(0xFF323233),
          ),
        ),
      ),
    );
  }
}