import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 年份选择卡片组件
///
/// 显示单个年份的选择卡片，点击时触发回调
class IncomeAndExpenditureDialogItemGridview extends StatelessWidget {
  /// 年份值
  final String value;

  /// 点击回调
  final VoidCallback onTap;

  final bool isSelected;

  const IncomeAndExpenditureDialogItemGridview({
    super.key,
    required this.value,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        // 设置年份卡片的样式：蓝色背景，圆角
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF5290FD) : const Color(0xFFF0F0F0),
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Center(
          // 年份文本显示
          child: Text(
            "$value",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected ?  Colors.white : const Color(0xFF323233),
              fontSize: 14.sp,
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }
}