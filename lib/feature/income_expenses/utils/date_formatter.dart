import 'package:intl/intl.dart';

class DateFormatter {
  /// 格式化日期字符串
  /// [dateString]：输入的日期字符串，格式为 "yyyy-MM-dd"（如 "2025-07-28"）
  /// 返回格式：
  /// - 若为今天："7月28日-今天"
  /// - 若为其他日期："7月28日-周几"
  static String formatDate(String dateString) {
    try {
      // 1. 将字符串解析为DateTime对象
      DateTime date = DateFormat("yyyy-MM-dd").parse(dateString);
      // 2. 获取今天的日期（忽略时间部分）
      DateTime today = DateTime.now();
      DateTime todayWithoutTime = DateTime(today.year, today.month, today.day);
      DateTime dateWithoutTime = DateTime(date.year, date.month, date.day);

      // 3. 判断是否为今天
      bool isToday = dateWithoutTime == todayWithoutTime;

      // 4. 格式化月份和日期（如 "7月28日"）
      String monthDay = "${date.month}月${date.day}日";

      // 5. 处理星期（1-7 对应 周一到周日）
      String weekday;
      switch (date.weekday) {
        case DateTime.monday:
          weekday = "周一";
          break;
        case DateTime.tuesday:
          weekday = "周二";
          break;
        case DateTime.wednesday:
          weekday = "周三";
          break;
        case DateTime.thursday:
          weekday = "周四";
          break;
        case DateTime.friday:
          weekday = "周五";
          break;
        case DateTime.saturday:
          weekday = "周六";
          break;
        case DateTime.sunday:
          weekday = "周日";
          break;
        default:
          weekday = "";
      }
      // 6. 拼接结果
      return isToday ? "$monthDay-今天" : "$monthDay-$weekday";
    } catch (e) {
      // 解析失败时返回原始字符串
      return dateString;
    }
  }
}
