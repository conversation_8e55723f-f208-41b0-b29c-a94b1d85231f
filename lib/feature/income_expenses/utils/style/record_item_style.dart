import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RecordItemStyle {
  // /// 标签默认字体大小（全局统一）
  // static double get defaultFontSize => 17;
  //
  // /// 标签默认颜色（深灰色，适用于大多数场景）
  // static Color get defaultColor => const Color(0xFF323232);

  /// 整体上下内边距（默认：10.h）
  static double get verticalPadding => 10;

  /// 整体左右内边距（默认：0）
  static double get horizontalPadding => 0;

  /// 右侧图片宽度（默认：12.w）
  static double get imageArrowWidth=> 12;

  /// 右侧图片高度（默认：12.w）
  static double get imageArrowHeight=> 12;

  /// 获取默认标签样式
  static TextStyle leftTextStyle({
    double textSize = 17,
    int textColor = 0xFF323232,
  }) =>
      TextStyle(
        fontSize: textSize.sp,
        color: Color(textColor),
      );

  /// 选中状态文本样式
  static TextStyle selectTextStyle({
    double textSize = 17,
    int textColor = 0xFF323232,
    FontWeight fontWeight = FontWeight.w500,
  }) =>
      TextStyle(
        fontSize: textSize.sp,
        color: Color(textColor),
        fontWeight: fontWeight,
      );
}
