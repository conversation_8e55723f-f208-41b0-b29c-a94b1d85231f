import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/income_expenses/utils/record_type_constants.dart';
import 'package:gdjg_pure_flutter/feature/income_expenses/vm/annual_incomes_pend_manage_type_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';

import '../../utils/ui_util/button_util.dart';
import 'manage_type/view/expense_income_select_icon_view.dart';

/// 日常收支页面-更多分类管理
class AnnualIncomeAddTypePage extends BaseFulPage {
  const AnnualIncomeAddTypePage({super.key})
      : super(appBar: const YPAppBar(title: "添加新分类"));

  @override
  State createState() => _AnnualIncomeAddTypePageState();
}

class _AnnualIncomeAddTypePageState extends BaseFulPageState
    with SingleTickerProviderStateMixin {
  final AnnualIncomesPendManageTypeVm _vm = AnnualIncomesPendManageTypeVm();

  // 用于控制输入框内容
  final TextEditingController _controller = TextEditingController();
  int recordType = RecordTypeConstants.income;

  @override
  void onPageCreate() {
    _vm.getUserSourceImg(recordType);
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var mParams = routeParams as Map<String, dynamic>?;
    if (null != mParams && mParams.containsKey("is_income")) {
      var value = mParams["is_income"];
      if (value is bool) {
        recordType = value ? RecordTypeConstants.income : RecordTypeConstants.expense;
      }
    }
  }

  /// 构建页面UI入口
  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Container(
        // 为水平布局添加内边距（替代ListView的padding）
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Column(
          // mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTitleText('新增分类名称'),
            //输入框
            _buildLimitInput(_controller),
            _buildTitleText('选择图标'),
            // 选择类型
            ExpenseIncomeSelectIconView(
              us: _vm.us,
            ),
          ],
        ),
      ),
      bottomNavigationBar: _buildAddButton(), // 底部添加按钮
    );
  }

  /// 构建"更多类型"标题
  Widget _buildTitleText(String text) {
    return Text(text,
        style: TextStyle(
            fontSize: 17,
            color: Color(0xD9000000),
            fontWeight: FontWeight.w500));
  }

  /// 构建底部确认按钮
  Widget _buildAddButton() {
    return Container(
      width: double.infinity,
      height: 70.h,
      color: Colors.white,
      padding: EdgeInsets.only(top: 10.h, left: 16.w, right: 16.w),
      child: Column(
        children: [
          ButtonUtil.buildCommonButton(
            text: '添加新分类',
            onPressed: () => {
              _vm.addUserSource(_controller.text, recordType),
              // YPRoute.openPage(RouteNameCollection.annualIncomeAddTypePage)
              //     ?.then((res) {
              //   if (res != null) {
              //     // widget.vm.us.updateRemarkInfo(res as String);
              //   }
              // })
            },
          ),
        ],
      ),
    );
  }

  /// 抽取的输入框方法
  /// 抽取的输入框方法
  Widget _buildLimitInput(TextEditingController controller) {
    return Padding(
      // 上下间距 16，左右无间距（让输入框自己填满屏幕）
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Color(0XFFF7F7F7),
          borderRadius: BorderRadius.circular(4),
        ),
        child: TextField(
          controller: controller,
          inputFormatters: [
            // 长度限制：最多4个汉字（一个汉字占1个长度，字母/数字也占1，若需严格区分，需自定义过滤器）
            LengthLimitingTextInputFormatter(4),
            // 可选：正则过滤，仅允许中文（根据需求决定是否开启）
            FilteringTextInputFormatter.allow(RegExp(r'[\u4e00-\u9fa5]')),
          ],
          decoration: InputDecoration(
            border: InputBorder.none,
            // 占位提示文字
            hintText: '限4个汉字',
            hintStyle: TextStyle(
              color: Colors.grey.shade400, // 提示文字颜色
              fontSize: 16,
            ),
            // 内边距，让内容与边框有间距
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 10,
            ),
          ),
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
          ),
          onChanged: (value) {},
        ),
      ),
    );
  }

  @override
  void dispose() {
    // 释放控制器资源
    _controller.dispose();
    super.dispose();
  }
}
