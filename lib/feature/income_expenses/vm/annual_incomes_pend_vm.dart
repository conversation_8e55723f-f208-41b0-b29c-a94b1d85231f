import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/feature/income_expenses/ui_model/annual_incomes_second_item_view_ui_model.dart';
import 'package:gdjg_pure_flutter/feature/income_expenses/ui_model/annual_incomes_third_item_view_ui_model.dart';

import '../../../data/annual_income_spend/ds/model/param/business_get_bk_index_param_model.dart';
import '../../../data/annual_income_spend/ds/model/param/business_get_bk_list_param_model.dart';
import '../../../data/annual_income_spend/ds/model/param/business_get_bookkeeping_excel_code_param_model.dart';
import '../../../data/annual_income_spend/repo/annual_income_spend_repo.dart';
import '../../../data/annual_income_spend/repo/model/business_get_bk_index_biz_model.dart';
import '../../../data/annual_income_spend/repo/model/business_get_bk_list_biz_model.dart';
import '../../../utils/ui_util/toast_util.dart';
import '../dialog/income_spend_select_add_record_dialog.dart';
import '../dialog/income_spend_select_pro_dialog.dart';
import '../ui_model/annual_incomes_first_item_view_ui_model.dart';
import '../ui_model/annual_incomes_pend_dept_ui_model.dart';
import '../ui_model/annual_incomes_total_head_view_ui_model.dart';
import '../us/annual_incomes_pend_dept_us.dart';
import 'annual_income_spend_converter.dart';

class AnnualIncomeSpendVm {
  final _annualIncomeSpendRepo = AnnualIncomeSpendRepo();
  final us = AnnualIncomeSpendDeptUS();
  final _converter = AnnualIncomeSpendConverter(); // 创建转换器实例

  /// 获取在线记账表地址
  Future<void> getBookkeepingExcelCode() async {
    final resp = await _annualIncomeSpendRepo.getBookkeepingExcelCode(
        BusinessGetBookkeepingExcelCodeParamModel(
            start_date: "", end_date: ""));
    if (resp.isOK()) {
      print(
          "====fetchData==getBookkeepingExcelCode=ok=11=${resp.getSucData()?.url}");
    } else {
      ToastUtil.showToast('获取报表地址失败：${resp.fail?.errorMsg}');
    }
  }

  /// 获取记账年
  Future<void> getYearList() async {
    final resp = await _annualIncomeSpendRepo.getBookYear();
    if (resp.isOK()) {
      us.setYearList(_converter.convertBookYear(resp.getSucData()?.list));
    }
  }

  /// 获取项目列表
  Future<void> getProList() async {
    final resp = await _annualIncomeSpendRepo.getProList();
    if (resp.isOK()) {
      us.setProList(_converter.convertProList(resp.getSucData()?.list));
    }
  }

  /// 获取收入支出类型列表
  /// 并行请求两个接口，按type分别存储各自数据
  Future<void> getTypeList() async {
    try {
      // 并行发起两个接口请求
      final responses = await Future.wait([
        _annualIncomeSpendRepo.getTypeList(1),
        _annualIncomeSpendRepo.getTypeList(2),
      ]);

      // 处理第一个接口的响应
      if (responses[0].isOK()) {
        final typeList1 =
            _converter.convertTypeList(responses[0].getSucData()?.list);
        us.setIncomeList(typeList1); // 收入类型-第一个接口数据
      } else {
        debugPrint("第一个类型列表接口请求失败");
      }

      // 处理第二个接口的响应
      if (responses[1].isOK()) {
        final typeList2 =
            _converter.convertTypeList(responses[1].getSucData()?.list);
        us.setSpendList(typeList2); // 支出类型-第二个接口数据
      } else {
        debugPrint("第二个类型列表接口请求失败");
      }
    } catch (e) {
      debugPrint("类型列表请求异常: $e");
    }
  }

  /// 获取记工统计-年（包含头部）
  Future<void> getBkIndexOfYear() async {
    final resp = await _annualIncomeSpendRepo.getBkIndexOfYear(
        BusinessGetBkIndexParamModel(
            year: us.selectionYearInfo,
            work_note: us.selectionProInfo.id > 0
                ? us.selectionProInfo.id.toInt().toString()
                : "",
            bookkeeping_source: us.selectionTypeInfo.id > 0
                ? us.selectionTypeInfo.id.toInt().toString()
                : ""));
    if (resp.isOK()) {
      //头部数据
      us.setTotalHeadData(_converter.convertBkHeadData(resp.getSucData()));
      //将记工统计-转换一级列表数据
      us.setRecordItemList(_converter.convertBkIndex(resp.getSucData()));
      if (us.recordItemList.isNotEmpty) {
        //获取二级数据
        getBkIndexOfMonth(us.selectionYearInfo);
      }
    }
  }

  ///获取记工统计-月(包含日)
  Future<void> getBkIndexOfMonth(int year) async {
    var time = _converter.getMonthRange(us.recordItemList, year);
    //获取二级数据
    final resp = await _annualIncomeSpendRepo.getBkIndexOfMonth(
        BusinessGetBkListParamModel(
            start_month: time[AnnualIncomeSpendConverter.START_MONTH],
            end_month: time[AnnualIncomeSpendConverter.END_MONTH],
            time_type: AnnualIncomeSpendConverter.MONTH,
            work_note: us.selectionProInfo.id > 0
                ? us.selectionProInfo.id.toInt().toString()
                : "",
            bookkeeping_source: us.selectionTypeInfo.id > 0
                ? us.selectionTypeInfo.id.toInt().toString()
                : ""));

    if (resp.isOK()) {
      var firstList = _converter.convertBkList(resp.getSucData());
      // 核心逻辑：匹配月份并复制内容
      for (var itemA in us.recordItemList) {
        // 在B列表中查找相同月份的元素
        final matchingItemB =
            firstList.firstWhereOrNull((itemB) => itemB.month == itemA.month);
        // 如果找到匹配项，则复制list
        if (matchingItemB != null) {
          itemA.list = matchingItemB.list;
        }
      }
    }
  }

// Future<void> fetchDeptList() async {
//   final resp = await _annualIncomeSpendRepo.fetchDeptList();
//   if (resp.isOK()) {}
// }
}
