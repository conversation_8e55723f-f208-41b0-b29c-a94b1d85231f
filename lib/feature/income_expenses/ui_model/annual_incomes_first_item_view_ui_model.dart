import 'package:gdjg_pure_flutter/feature/income_expenses/ui_model/annual_incomes_third_item_view_ui_model.dart';

import 'annual_incomes_pend_ui_model.dart';
import 'annual_incomes_second_item_view_ui_model.dart';

class AnnualIncomesFirstItemViewUiModel {
  //月
  int month;

  //收入
  String income;

  //支出
  String expend;

  //
  double hasBookkeeping;

  List<AnnualIncomesSecondItemViewUiModel> list;
  bool isExpanded = false;
  List<AnnualIncomeSpendImgUIModel>? url;

  // List<AnnualIncomeSpendImgUIModel>? chooseImg,
  AnnualIncomesFirstItemViewUiModel({
    this.month = 0,
    this.income = "0.00",
    this.expend = "0.00",
    this.hasBookkeeping = 0,
    this.isExpanded = false,
    List<AnnualIncomesSecondItemViewUiModel>? list,
    List<AnnualIncomeSpendImgUIModel>? url,
  })  : list = list ?? [],
        url = url ?? [];
}
