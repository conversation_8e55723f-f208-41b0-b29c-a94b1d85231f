import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../ui_model/annual_incomes_pend_ui_model.dart';

/// 分类项展示组件
/// 用于显示单个分类的图标、名称及操作按钮（添加/删除）
class ExpenseIncomeCategoryItemView extends StatelessWidget {
  /// 当前分类数据
  final AnnualIncomeSpendUIModel categoryItem;

  /// 删除回调（当分类已存在时触发）
  final VoidCallback? onDelete;

  /// 添加回调（当分类未存在时触发）
  final VoidCallback? onAdd;
  final bool showDelete;

  // 布局常量
  static const iconRightMargin = 12.0;

  // 操作图标（删除/添加）视觉尺寸
  static const double _actionIconVisualSize = 16.0;

  // 点击区域扩大的尺寸（整体点击区域大小）
  static const double _actionTapAreaSize = 30.0;

  const ExpenseIncomeCategoryItemView({
    super.key,
    required this.categoryItem,
    required this.showDelete,
    this.onDelete,
    this.onAdd,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16.0),
      child: Row(
        children: [
          Stack(
            clipBehavior: Clip.none,
            children: [
              // 主图标
              _buildLeftIcon(),
              // 右上角操作图标（删除/添加）- 合并后的方法
              _buildActionIconWithPosition(
                onTap: showDelete ? onDelete : onAdd,
                iconPath: showDelete
                    ? 'assets/images/common/ic_delete.png'
                    : 'assets/images/common/ic_add.png',
              ),
            ],
            // ),
          ),
          // const SizedBox(width: 12.0),
          // 分类名称
          Text(
            categoryItem.name,
            style: const TextStyle(
              fontSize: 16.0,
              color: Color(0xD9000000),
            ),
          ),
          const Spacer(),
        ],
      ),
    );
  }

  /// 构建带位置的操作图标（合并版）
  /// 包含Positioned定位、点击事件和图标本身
  Widget _buildActionIconWithPosition({
    required VoidCallback? onTap,
    required String iconPath,
  }) {
    return Positioned(
      top: -10.h,
      right: 0,
      width: _actionTapAreaSize,
      height: _actionTapAreaSize,
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          alignment: Alignment.center,
          child: Image.asset(
            iconPath,
            fit: BoxFit.contain,
            width: _actionIconVisualSize,
            height: _actionIconVisualSize,
          ),
        ),
      ),
    );
  }

  /// 左侧图标组件
  Widget _buildLeftIcon() {
    return Container(
      margin: const EdgeInsets.only(right: iconRightMargin),
      child: (categoryItem.chooseImg.isEmpty ||
              categoryItem.chooseImg[0].url.isEmpty)
          ? _buildLocalDefaultImage()
          : _buildNetworkImage(categoryItem.chooseImg[0].url),
    );
  }

  /// 网络图片
  Widget _buildNetworkImage(String url) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(6.r),
      child: Image.network(
        url,
        fit: BoxFit.contain,
        width: 38.w,
        height: 38.w,
        errorBuilder: (context, error, stackTrace) => _buildLocalDefaultImage(),
      ),
    );
  }

  /// 本地默认图片
  Widget _buildLocalDefaultImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12.r),
      child: Image.asset(
        'assets/images/common/waa_ic_holder.webp',
        fit: BoxFit.contain,
        width: 38.w,
        height: 38.w,
      ),
    );
  }
}
