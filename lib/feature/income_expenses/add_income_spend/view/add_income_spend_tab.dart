import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../../../../../init_module/init_route.dart';
import '../../../../../utils/route_util/route_api/yp_route.dart';
import '../../../../../utils/ui_util/button_util.dart';
import '../../../../utils/system_util/yprint.dart';
import '../../dialog/income_spend_select_pro_dialog.dart';
import '../../ui_model/annual_incomes_pend_dept_ui_model.dart';
import '../../vm/add_annual_incomes_pend_vm.dart';
import '../item_view/record_item_bottom_pro_photo_btn_view.dart';
import '../item_view/record_item_select_date_view.dart';
import '../item_view/record_item_select_photo_view.dart';
import '../item_view/record_item_select_type_view.dart';
import '../item_view/record_item_select_project_view.dart';
import '../item_view/record_item_remark_input_view.dart';
import 'amount_input_view.dart';

/// 日常收支页面-新增tab
class AddIncomeSpendTab extends StatefulWidget {
  final TextEditingController amountController;
  final int recordType;
  final AddAnnualIncomesPendVm vm;

  const AddIncomeSpendTab({
    super.key,
    required this.vm,
    required this.amountController,
    required this.recordType,
  });

  @override
  State<AddIncomeSpendTab> createState() => _AddIncomeSpendTabPage();
}

class _AddIncomeSpendTabPage extends State<AddIncomeSpendTab>
    with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 滚动内容区域
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                // 金额输入框
                AmountInputView(controller: widget.amountController),
                Divider(
                  height: 0.5.h,
                  color: const Color(0xFFE5E5E5),
                ),
                // 选择类型
                RecordItemSelectTypeView(
                  onTap: _checkProListInfo,
                  flushTypeCallback: ()=>widget.vm.getTypeList(),
                  us: widget.vm.us,
                  recordType: widget.recordType,
                ),
                // 选择日期
                RecordItemSelectDateView(
                  onTap: () {
                    yprint("打开日期选择");
                  },
                ),
                // 选择项目
                Obx(() {
                  final hasProject = widget.vm.us.selectionProInfo.deptId > 0;
                  return hasProject
                      ? RecordItemSelectProjectView(
                          us: widget.vm.us,
                          onSelectTap: _checkProListInfo,
                          onRemoveTap: () => widget.vm.us
                              .updateSelectionProInfo(
                                  AnnualIncomeSpendDeptUIModel()),
                        )
                      : const SizedBox.shrink(); // 不显示时返回空容器
                }),
                // 选择相册
                Obx(() {
                  return widget.vm.us.photoViewDisplayStatus
                      ? RecordItemSelectPhotoView(
                          us: widget.vm.us,
                          onRemoveTap: () =>
                              _updatePhotoViewDisplayStatus(false))
                      : const SizedBox.shrink(); // 不显示时返回空容器
                }),
                // 备注输入框
                Obx(() {
                  return RecordItemRemarkInputView(
                    remarkText: widget.vm.us.remark,
                    onTap: () => _remarkPageTap(),
                  );
                }),

                // 底部项目、照片按钮
                RecordItemBottomProPhotoBtnView(
                  us: widget.vm.us,
                  onProjectTap: _checkProListInfo,
                  onUploadPhotoTap: _onUploadPhotoTap,
                ),
              ],
            ),
          ),
        ),
        // 底部按钮
        _buildBottomConfirmButton(),
      ],
    );
  }

  /// 构建底部确认按钮
  Widget _buildBottomConfirmButton() {
    return Container(
      width: double.infinity,
      height: 94.h,
      color: Colors.white,
      padding: EdgeInsets.only(top: 10.h, left: 16.w, right: 16.w),
      child: Column(
        children: [
          ButtonUtil.buildCommonButton(
            text: '保存',
            onPressed: _onConfirmTap,
          ),
          const Spacer(),
        ],
      ),
    );
  }

  /// 检查项目列表数据
  void _checkProListInfo() {
    if (widget.vm.us.proList.isEmpty) {
      widget.vm.getProList().then((_) {
        if (widget.vm.us.proList.isNotEmpty) {
          _showSelectProDialog();
        }
      });
      return;
    }
    _showSelectProDialog();
  }

  /// 显示项目选择弹窗
  void _showSelectProDialog() {
    showSelectProDialog(
      widget.vm.us.proList,
      widget.vm.us.selectionProInfo,
      false,
      (AnnualIncomeSpendDeptUIModel result) {
        widget.vm.us.updateSelectionProInfo(result);
        if (result.deptId > 0) {
          widget.vm.getDeptDetail(result.deptId.toInt().toString());
        }
      },
    );
  }

  /// 更新相册入口显示状态
  void _updatePhotoViewDisplayStatus(bool isDisplay) {
    widget.vm.us.updatePhotoViewDisplayStatus(isDisplay);
  }

  /// 上传照片点击事件
  void _onUploadPhotoTap() {
    _updatePhotoViewDisplayStatus(true);
  }

  /// 备注编辑
  void _remarkPageTap() {
    YPRoute.openPage(RouteNameCollection.notes)?.then((res) {
      if (res != null && res is String) {
        widget.vm.us.updateRemarkInfo(res);
      }
    });
  }

  /// 确认按钮点击事件
  void _onConfirmTap() {
    widget.vm.onConfirmTap(widget.amountController.text, widget.recordType);
  }

}
