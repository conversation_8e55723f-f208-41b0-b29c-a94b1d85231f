import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../ui_model/annual_incomes_pend_ui_model.dart';

/// 单个记录项视图（子组件）
/// 包含圆形图标和文本，支持控制标题显示，图标大小支持外部传入
class RecordItemTypeView extends StatelessWidget {
  /// 数据模型
  final AnnualIncomeSpendUIModel uiModel;

  /// 点击回调
  final VoidCallback onTap;

  /// 是否被选中
  final bool isSelected;

  /// 图标宽度（外部传入，默认40）
  final double iconSize;

  /// 行间距（外部传入，默认16）
  final double lineSpacing;

  /// 是否显示标题文本（新增参数，默认显示）
  final bool showTitle;

  const RecordItemTypeView({
    super.key,
    required this.uiModel,
    required this.isSelected,
    required this.onTap,
    this.iconSize = 40,
    this.lineSpacing = 16,
    this.showTitle = true, // 默认显示标题
  });

  @override
  Widget build(BuildContext context) {
    // 计算单个item宽度（与父组件保持一致）
    final itemWidth = MediaQuery.of(context).size.width / 5;

    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min, // 高度仅包含内容，不拉伸
        crossAxisAlignment: CrossAxisAlignment.center, // 内容水平居中
        children: [
          // 圆形图标（限制最大宽度，避免超出item宽度）
          ConstrainedBox(
            constraints: BoxConstraints(
              maxWidth: itemWidth - 4.w, // 留少量空间避免边缘溢出
            ),
            child: _buildIcon(),
          ),

          // 根据showTitle决定是否显示标题相关组件
          if (showTitle) ...[
            SizedBox(height: 4.h), // 图标与文本间距
            // 文本（限制最大宽度，避免超出）
            ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: itemWidth,
              ),
              child: _buildTypeText(),
            ),
          ],

          SizedBox(height: lineSpacing.h),
        ],
      ),
    );
  }

  /// 构建类型文本
  Widget _buildTypeText() {
    return Text(
      uiModel.name,
      style: TextStyle(
        fontSize: 12.sp,
        color: Colors.black87,
        overflow: TextOverflow.ellipsis,
      ),
      maxLines: 1, // 限制单行显示
    );
  }

  /// 构建图标（优先网络图片，无则显示本地默认图）
  Widget _buildIcon() {
    if (uiModel.noChooseImg.isEmpty || uiModel.noChooseImg[0].url.isEmpty) {
      return _buildLocalImage('assets/images/worker/icon_record_type_more.png');
    }
    return _buildNetworkImage(
        isSelected ? uiModel.chooseImg[0].url : uiModel.noChooseImg[0].url);
  }

  /// 网络图片（使用外部传入的图标尺寸）
  Widget _buildNetworkImage(String url) {
    return Image.network(
      url,
      fit: BoxFit.contain,
      width: iconSize.w,
      height: iconSize.h,
      errorBuilder: (context, error, stackTrace) =>
          _buildLocalImage('assets/images/worker/icon_record_type_more.png'),
    );
  }

  /// 本地默认图片（使用外部传入的本地图标尺寸）
  Widget _buildLocalImage(String localPath) {
    return Image.asset(
      localPath,
      fit: BoxFit.contain,
      width: iconSize.w,
      height: iconSize.h,
    );
  }

}

