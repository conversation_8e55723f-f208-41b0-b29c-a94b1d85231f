import 'package:gdjg_pure_flutter/data/modify_data/repo/modify_repo.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/accredit_project/detail/entity/AccreditProjectProps.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/accredit_project/list/vm/my_accredit_project_list_us.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

class MyAccreditProjectListVM {
  final _repo = ModifyRepo();
  final us = MyAccreditProjectListUS();

  /// 获取我的授权项目列表
  Future<dynamic> getAccreditProjectList() async {
    final resp = await _repo.getAccreditProjectList();
    if (resp.isOK()) {
      final result = resp.success?.data?.transform();
      if (result != null) {
        us.setDataList(result);
      }
    }
  }

  /// 授权详情前置接口 检查班组长是否实名
  Future<bool> checkGroupIsRealName(String leaderId) async {
    final resp = await _repo.checkGroupIsRealName(leaderId);
    return resp.isOK() && resp.getSucData()?.leader_verified == 1;
  }

  Future<void> grantVerify(
      String leaderId, int isGranted, String leaderName) async {
    //已经同意，这里是关闭
    if (isGranted == 1) {
      showCommonDialog(CommonDialogConfig(
        title: '确定取消对$leaderName的隐私信息授权吗？',
        content: '隐私信息包括：身份证信息、银行卡信息',
        negative: '取消',
        positive: '确认',
        onPositive: () {
          _repo.fetchGrantClose(leaderId).then((result) {
            if (result.isOK()) {
              ToastUtil.showToast('取消成功');
              getAccreditProjectList();
            }
          });
        },
      ));
      return;
    }

    final result = await _repo.checkGroupIsRealName(leaderId);
    if (result.isOK()) {
      if (result.getSucData()?.leader_verified == 0) {
        showCommonDialog(CommonDialogConfig(
          title: '班组长$leaderName未进行实名，不可对他进行隐私信息授权',
          hiddenNegative: true,
          positive: '我知道了',
        ));
      } else {
        showCommonDialog(CommonDialogConfig(
          title: '确定将隐私信息授权给$leaderName？',
          content: '隐私信息包括：身份证信息、银行卡信息',
          negative: '取消',
          positive: '确认',
          onPositive: () {
            YPRoute.openPage(
              RouteNameCollection.accreditProjectDetailPage,
              params: AccreditProjectProps(
                  leaderId: leaderId,
                  onRefresh: () {
                    getAccreditProjectList();
                  }),
            );
          },
        ));
      }
    }
  }
}
