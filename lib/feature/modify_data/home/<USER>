import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/event_bus/event_modify_data_refresh.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/home/<USER>/modify_data_vm.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/name/update_name_dialog.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/dialog_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/user_kv/UserKvUtil.dart';
import 'package:gdjg_pure_flutter/widget/select_job_type/select_job_type_utils.dart';
import 'package:get/get.dart';

import 'view/data_line.dart';

class ModifyDataHomePage extends BaseFulPage {
  const ModifyDataHomePage({super.key}) : super(appBar: null);

  @override
  State createState() => _BindPhonePageState();
}

class _BindPhonePageState extends BaseFulPageState {
  final ModifyDataVM vm = ModifyDataVM();

  StreamSubscription? _eventRefreshPrivacy;

  @override
  void onPageCreate() {
    super.onPageCreate();
    //收到 event 刷新隐私信息
    _eventRefreshPrivacy ==
        EventBusUtil.collect<EventRefreshPrivacyInfo>((data) {
          vm.getPrivacyInfo();
        });

    //初始化信息
    vm.initInfo();
    //获取隐私信息
    vm.getPrivacyInfo();
    //获取基本资料
    vm.getMemberInfo();
    //获取头像和姓名 修改次数
    vm.getNameBizTimes();
    vm.getAvatarBizTimes();
    //获取待审核信息
    vm.getAuditInfo();
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _eventRefreshPrivacy?.cancel();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
        backgroundColor: ColorsUtil.f5f5f5,
        appBar: AppBarUtil.buildCommonAppBarWithCustom(
          title: '我的名片',
          onBackTap: () {
            YPRoute.closePage();
          },
          actions: [
            GestureDetector(
              onTap: () {
                YPRoute.openPage(RouteNameCollection.myAccreditProjectListPage);
              },
              child: Container(
                margin: const EdgeInsets.only(right: 16.0),
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                decoration: BoxDecoration(
                  color: ColorsUtil.primaryColor15,
                  borderRadius: BorderRadius.circular(14.0),
                ),
                child: Row(
                  children: [
                    IconFont(IconNames.saasSetting, size: 16, color: '#1983FF'),
                    SizedBox(width: 4.w),
                    Text(
                      '我授权的项目',
                      style: TextStyle(
                          color: ColorsUtil.primaryColor,
                          fontSize: 14,
                          fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                width: double.maxFinite,
                child: Text('基本信息'),
              ),
              Obx(
                () => DataLine(
                  title: '头像',
                  content: '',
                  checkTips: vm.us.getAvatarAuditTips(),
                  headerUrl: vm.us.avatarUrl,
                  headerText: vm.getAvatarText(),
                  isHeader: true,
                  checkTipsClick: () {
                    _headerTipsClick();
                  },
                  headerClick: () {
                    _headerViewClick();
                  },
                ),
              ),
              Obx(
                () => DataLine(
                    title: '姓名',
                    content: vm.us.name,
                    checkTips: vm.us.getNameAuditTips(),
                    checkTipsClick: () {
                      _nameTipsClick();
                    },
                    onClick: () {
                      _nameLineClick();
                    }),
              ),
              Obx(
                () => DataLine(
                  title: '换绑手机号',
                  content: vm.us.phone,
                  bottomGrayText: '(将当前手机号更换为其他手机号)',
                  onClick: () {
                    YPRoute.openPage(RouteNameCollection.modifyDataPhone,
                        params: {"old_phone": vm.us.phone});
                  },
                ),
              ),
              Obx(
                () => DataLine(
                  title: '工种',
                  content: vm.us.occInfo.name.isNotEmpty
                      ? vm.us.occInfo.name
                      : '未选择',
                  bottomGrayText: vm.us.occInfo.id.isNotEmpty
                      ? '不满足「${vm.us.occInfo.name}」的记工需求，'
                      : '选择工种，系统将自动推荐最合适的记工模板',
                  bottomBlueText: vm.us.occInfo.id.isNotEmpty ? '点击申请专属模板' : '',
                  bottomTipsClick: () {
                    if (vm.us.occInfo.id.isNotEmpty) {
                      ToastUtil.showToast('todo 打开客服');
                    } else {
                      _showSelectJobType();
                    }
                  },
                  onClick: () {
                    _showSelectJobType();
                  },
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                width: double.maxFinite,
                child: Text('隐私信息'),
              ),
              if (UserKvUtil.getUserRoleIsBoss())
                Obx(
                  () => DataLine(
                    title: '实名',
                    content: vm.us.real,
                    redHas: true,
                    onClick: () {
                      ToastUtil.showToast('todo 跳转实名');
                    },
                  ),
                ),
              Obx(
                () => DataLine(
                  title: '身份证',
                  content: vm.us.cardData.cardNo.isEmpty ? '未上传' : '已上传',
                  redHas: true,
                  onClick: () {
                    YPRoute.openPage(RouteNameCollection.idCardInfoPage);
                  },
                ),
              ),
              Obx(
                () => DataLine(
                    title: '银行卡',
                    content: vm.us.banStr,
                    redHas: true,
                    onClick: () {
                      YPRoute.openPage(RouteNameCollection.bankCardManagerPage);
                    }),
              ),
            ],
          ),
        ));
  }

  ///打开工种选择弹窗
  _showSelectJobType() async {
    final curOcc = vm.us.occInfo.id.isNotEmpty ? vm.us.occInfo : null;
    final selectedOcc = await showSelectJobType(
      context,
      curOccupation: curOcc,
    );
    if (selectedOcc != null) {
      UserKvUtil.saveUserOccupationInfo(selectedOcc);
      vm.us.setOccInfo(selectedOcc);
    }
  }

  /// 头像点击
  _headerViewClick() {
    final status = vm.us.avatarAuditModel.status;
    if (status == '0') {
      ToastUtil.showToast('头像审核中，请稍后再试；');
      return;
    }
    if (vm.us.avatarBizTimes.loseCount < 1) {
      ToastUtil.showToast('本年度修改次数已用完');
      return;
    }

    DialogUtil.showUploadImageBottomSheet(context,
        allowVideoInPicker: false,
        maxPhotoCount: 1,
        showVideoOption: false, onTap: (urls) {
      if (urls.first.isNotEmpty) {
        vm.updateAvatar(urls.first).then((suc) {
          //头像姓名成功后 刷新审核状态和修改次数
          if (suc) {
            vm.getAuditInfo();
            vm.getAvatarBizTimes();
          }
        });
      }
    });
  }

  /// 头像审核失败view点击 如果是审核中或者没有就不会弹窗
  _headerTipsClick() {
    final status = vm.us.avatarAuditModel.status;
    if (status == '2') {
      showCommonDialog(CommonDialogConfig(
          title: '失败原因',
          content: vm.us.avatarAuditModel.msg.isNotEmpty
              ? vm.us.avatarAuditModel.msg
              : '头像涉及违规，请重新上传审核',
          positive: '确定',
          hiddenNegative: true));
      return;
    }
  }

  /// 姓名整行点击
  _nameLineClick() {
    final status = vm.us.nameAuditModel.status;
    if (status == '0') {
      ToastUtil.showToast('姓名审核中，请稍后再试；');
      return;
    }
    if (vm.us.nameBizTimes.loseCount < 1) {
      ToastUtil.showToast('本年度修改次数已用完');
      return;
    }
    YPRoute.openDialog(
      builder: (context) => UpdateNameDialog(
        onConfirm: (name) {
          vm.updateName(name).then((suc) {
            //修改姓名成功后 刷新审核状态和修改次数
            if (suc) {
              vm.getAuditInfo();
              vm.getNameBizTimes();
            }
          });
        },
        defaultName: vm.us.name,
        nameTips:
            '每年仅可修改 ${vm.us.nameBizTimes.canUseTimes} 次，本年度还剩余 ${vm.us.nameBizTimes.loseCount} 次',
      ),
    );
  }

  /// 姓名审核失败view点击 如果是审核中或者没有就不会弹窗
  _nameTipsClick() {
    final status = vm.us.nameAuditModel.status;
    if (status == '2') {
      showCommonDialog(CommonDialogConfig(
          title: '失败原因',
          content: vm.us.nameAuditModel.msg.isNotEmpty
              ? vm.us.nameAuditModel.msg
              : '名字涉及违规，请重新修改审核',
          positive: '确定',
          hiddenNegative: true));
      return;
    }
    if (status == '0') {
      ToastUtil.showToast('姓名审核中，请稍后再试；');
      return;
    }
  }
}
