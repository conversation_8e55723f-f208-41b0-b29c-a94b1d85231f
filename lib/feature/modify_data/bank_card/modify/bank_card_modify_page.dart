import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/modify_data/ds/model/bank_manager/net/bank_net_model_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/bank_card/modify/vm/bank_card_modify_vm.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/event_bus/event_modify_data_refresh.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/dialog_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

class BankCardModifyPage extends BaseFulPage {
  const BankCardModifyPage({super.key}) : super(appBar: null);

  @override
  State createState() => _BankCardModifyPageState();
}

class _BankCardModifyPageState extends BaseFulPageState {
  final vm = BankCardModifyVM();

  final TextEditingController _bankNoController = TextEditingController();
  final TextEditingController _bankNameController = TextEditingController();
  final TextEditingController _bankSubNameController = TextEditingController();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    if (routeParams != null) {
      var item = routeParams as BankCardBizModel?;
      if (item != null) {
        if (item.privacyId != null) {
          vm.setModel(BankCardBizModel(
              privacyId: item.privacyId,
              bankName: item.bankName,
              bankSubName: item.bankSubName,
              bankNo: item.bankNo));
          _bankNameController.text = item.bankName;
          _bankNameController.selection = TextSelection.fromPosition(
            TextPosition(offset: _bankNameController.text.length),
          );
          _bankSubNameController.text = item.bankSubName;
          _bankSubNameController.selection = TextSelection.fromPosition(
            TextPosition(offset: _bankSubNameController.text.length),
          );
          _bankNoController.text = item.bankNo;
        }
      }
    }
  }

  @override
  void dispose() {
    _bankNoController.dispose();
    _bankNameController.dispose();
    _bankSubNameController.dispose();
    super.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Obx(
      () => Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBarUtil.buildCommonAppBarWithCustom(
          title: vm.us.data.privacyId > 0 ? '编辑银行卡' : '添加银行卡',
          onBackTap: () {
            YPRoute.closePage();
          },
          actions: [
            GestureDetector(
              onTap: () {
                FocusManager.instance.primaryFocus?.unfocus();
                if (!vm.isModify()) {
                  YPRoute.closePage();
                  return;
                }
                vm.saveBankCard().then((suc) {
                  if (suc) {
                    ToastUtil.showToast(
                        vm.us.data.privacyId > 0 ? '保存成功' : '添加成功');
                    EventBusUtil.emit(RefreshBankCardListEvent());
                    YPRoute.closePage();
                  }
                });
              },
              child: Container(
                margin: EdgeInsets.only(right: 16.0),
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: ColorsUtil.primaryColor,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  children: [
                    Text(
                      '保存',
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        body: InkWell(
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Column(
            children: [
              Container(
                height: 8.h,
                decoration: BoxDecoration(color: ColorsUtil.f5f6fa),
              ),
              Container(
                height: 50.h,
                padding: EdgeInsets.only(left: 16.w, right: 10.w),
                decoration: BoxDecoration(color: Colors.white),
                child: Row(
                  children: [
                    SizedBox(
                      width: 90.w,
                      child: Text('银行卡号',
                          style: TextStyle(
                            color: ColorsUtil.black85,
                            fontSize: 16.sp,
                          )),
                    ),
                    Expanded(
                      child: TextField(
                        controller: _bankNoController,
                        onChanged: (value) {
                          vm.setBankNo(value);
                        },
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp('[0-9]')),
                          LengthLimitingTextInputFormatter(19),
                        ],
                        style: TextStyle(
                          color: ColorsUtil.black85,
                          fontSize: 16.sp,
                        ),
                        decoration: InputDecoration(
                          hintText: '请填写银行卡号',
                          hintStyle: TextStyle(color: ColorsUtil.black35),
                          border: InputBorder.none,
                          counterText: "",
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        FocusManager.instance.primaryFocus?.unfocus();
                        DialogUtil.showUploadImageBottomSheet(context,
                            allowVideoInPicker: false,
                            returnLocalPaths: true,
                            maxPhotoCount: 1,
                            showVideoOption: false, onTap: (urls) {
                          if (urls.first.isNotEmpty) {
                            vm.ocrBankCardImage(urls.first).then((result) {
                              if (result != null) {
                                vm.setBankNo(result.bankNo);
                                _bankNoController.text = result.bankNo;
                                vm.setBankName(result.bankName);
                                _bankNameController.text = result.bankName;
                              }
                            });
                          }
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            vertical: 6.w, horizontal: 6.w),
                        decoration: BoxDecoration(color: Colors.transparent),
                        child: Image.asset(
                            'assets/images/common/ic_take_phone_small.webp',
                            width: 20,
                            height: 20),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w),
                color: ColorsUtil.lineColor,
                height: 0.5.h,
              ),
              Container(
                height: 50.h,
                padding: EdgeInsets.only(left: 16.w, right: 16.w),
                decoration: BoxDecoration(color: Colors.white),
                child: Row(
                  children: [
                    SizedBox(
                      width: 90.w,
                      child: Text('银行名称',
                          style: TextStyle(
                            color: ColorsUtil.black85,
                            fontSize: 16.sp,
                          )),
                    ),
                    Expanded(
                      child: TextField(
                        controller: _bankNameController,
                        onChanged: (value) {
                          vm.setBankName(value);
                        },
                        keyboardType: TextInputType.text,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(18),
                        ],
                        style: TextStyle(
                          color: ColorsUtil.black85,
                          fontSize: 16.sp,
                        ),
                        decoration: InputDecoration(
                          hintText: '请银行名称',
                          hintStyle: TextStyle(color: ColorsUtil.black35),
                          border: InputBorder.none,
                          counterText: "",
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w),
                color: ColorsUtil.lineColor,
                height: 0.5.h,
              ),
              Container(
                height: 50.h,
                padding: EdgeInsets.only(left: 16.w, right: 16.w),
                decoration: BoxDecoration(color: Colors.white),
                child: Row(
                  children: [
                    SizedBox(
                      width: 90.w,
                      child: Text('开户行',
                          style: TextStyle(
                            color: ColorsUtil.black85,
                            fontSize: 16.sp,
                          )),
                    ),
                    Expanded(
                      child: TextField(
                        controller: _bankSubNameController,
                        onChanged: (value) {
                          vm.setBankSubName(value);
                        },
                        keyboardType: TextInputType.text,
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(30),
                        ],
                        style: TextStyle(
                          color: ColorsUtil.black85,
                          fontSize: 16.sp,
                        ),
                        decoration: InputDecoration(
                          hintText: '请填写银行卡开户行',
                          hintStyle: TextStyle(color: ColorsUtil.black35),
                          border: InputBorder.none,
                          counterText: "",
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(horizontal: 16.w),
                color: ColorsUtil.lineColor,
                height: 0.5.h,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
