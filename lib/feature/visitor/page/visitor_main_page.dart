import 'package:flutter/widgets.dart';
import 'package:gdjg_pure_flutter/feature/visitor/page/visitor_leader_page.dart';
import 'package:gdjg_pure_flutter/feature/visitor/page/visitor_worker_page.dart';
import 'package:gdjg_pure_flutter/feature/visitor/vm/visitor_role_vm.dart';
import 'package:get/get.dart';

import '../../../utils/route_util/base_page/base_ful_page.dart';
import '../../tabbar/us/identity_us.dart';
import '../../tabbar/view/rotation_switch_view.dart';

class VisitorMainPage extends BaseFulPage {
  const VisitorMainPage({super.key}) : super(appBar: null);

  @override
  State createState() => _VisitorPageState();
}

class _VisitorPageState extends BaseFulPageState<VisitorMainPage> {
  @override
  void initState() {
    super.initState();
    Get.lazyPut(() => VisitorRoleVM(), fenix: true);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Obx(() => RotationSwitchView(
          frontWidget: const VisitorWorkerPage(),
          backWidget: const VisitorLeaderPage(),
          showFrontSide: Get.find<VisitorRoleVM>().role == UserIdentity.worker,
        ));
  }
}
