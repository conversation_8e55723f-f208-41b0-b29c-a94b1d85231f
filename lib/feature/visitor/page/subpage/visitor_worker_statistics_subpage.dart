import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/visitor/ui_model/visitor_label_page.dart';
import 'package:gdjg_pure_flutter/feature/visitor/utils/visitor_func_guide_utils.dart';
import 'package:gdjg_pure_flutter/feature/visitor/widget/visitor_date_range_filter.dart';

import '../../../../utils/ui_util/button_util.dart';
import '../../../../utils/ui_util/colors_util.dart';

class VisitorWorkerStatisticsSubpage extends StatefulWidget {
  const VisitorWorkerStatisticsSubpage({super.key});

  @override
  State<StatefulWidget> createState() => _VisitorWorkerStatisticsSubpageState();
}

class _VisitorWorkerStatisticsSubpageState
    extends State<VisitorWorkerStatisticsSubpage>
    with SingleTickerProviderStateMixin {
  final List<VisitorLabelPage> _pages = [
    VisitorLabelPage(label: "项目", page: _createPage()),
    VisitorLabelPage(label: "总计", page: _createPage()),
  ];

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _pages.length, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Divider(height: 8.h, color: Color(0xFFF0F0F0), thickness: 8.h),
        Container(
          decoration: BoxDecoration(color: Colors.white),
          padding: EdgeInsets.symmetric(vertical: 2.h),
          child: VisitorDateRangeFilterWidget(),
        ),
        Divider(
          height: 1,
          color: ColorsUtil.divideLineColor,
        ),
        Container(
          decoration: BoxDecoration(color: Colors.white),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
          child: Row(
            children: [
              ButtonUtil.buildGreyButtonSelect(
                text: '选择项目',
                onPressed: VisitorFuncGuideUtils.showPrivacyTipsDialog,
              ),
              SizedBox(width: 8.w),
              ButtonUtil.buildGreyButtonSelect(
                text: '类型',
                onPressed: VisitorFuncGuideUtils.showPrivacyTipsDialog,
              )
            ],
          ),
        ),
        Divider(height: 8.h, color: Color(0xFFF0F0F0), thickness: 8.h),
        _buildTabs(
          _tabController,
          List.generate(_pages.length, (index) => _pages[index].label),
        ),
        Expanded(
          child: _buildPages(
            _tabController,
            List.generate(_pages.length, (index) => _pages[index].page),
          ),
        ),
      ],
    );
  }

  Widget _buildTabs(TabController? controller, List<String> tabs) => Container(
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        child: TabBar(
          controller: controller,
          dividerColor: Colors.transparent,
          tabs: tabs.map((label) => Tab(text: label)).toList(),
          labelColor: ColorsUtil.primaryColor,
          unselectedLabelColor: const Color(0xFF666666),
          labelStyle: TextStyle(
            fontSize: 18.w,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w400,
          ),
          indicatorColor: ColorsUtil.primaryColor,
          indicatorSize: TabBarIndicatorSize.label,
        ),
      );

  Widget _buildPages(TabController? controller, List<Widget> pages) =>
      TabBarView(
        controller: controller,
        children: pages,
      );

  static Widget _createPage() => Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 60.h,
          ),
          Image.asset(
            "assets/images/common/icon_empty_team_project.png",
            fit: BoxFit.scaleDown,
            width: 100.w,
            height: 100.w,
          ),
          Text(
            '您尚未登录，无法查询到您的统计数据',
            style: TextStyle(
              color: const Color(0xFF9D9DB3),
              fontSize: 14.sp,
            ),
          )
        ],
      );
}
