import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/visitor/ui_model/visitor_icon_func_ui_model.dart';
import 'package:gdjg_pure_flutter/feature/visitor/utils/visitor_func_guide_utils.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

import '../../../../init_module/init_route.dart';
import '../../../../utils/route_util/route_api/yp_route.dart';

class VisitorProfileSubpage extends StatelessWidget {
  static const List<VisitorIconFuncUIModel> _businessFuncGroup = [
    VisitorIconFuncUIModel(
      VisitorIconFuncId.attendanceSheet,
      "考勤表",
      AssetImage("assets/images/visitor/ic_mine_func_attendance_sheet.webp"),
    ),
    VisitorIconFuncUIModel(
      VisitorIconFuncId.workChecked,
      "在线对工",
      AssetImage("assets/images/visitor/ic_mine_func_work_checked.webp"),
    ),
    VisitorIconFuncUIModel(
      VisitorIconFuncId.projectAgreement,
      "项目协议",
      AssetImage("assets/images/visitor/ic_mine_func_project_agreement.webp"),
    ),
    VisitorIconFuncUIModel(
      VisitorIconFuncId.incomeAndExpenditure,
      "全年收支",
      AssetImage(
          "assets/images/visitor/ic_mine_func_income_and_expenditure.webp"),
    ),
  ];

  static const List<VisitorIconFuncUIModel> _systemFuncGroup = [
    VisitorIconFuncUIModel(
      VisitorIconFuncId.share,
      "推荐给好友",
      AssetImage("assets/images/visitor/ic_mine_func_share.png"),
    ),
    VisitorIconFuncUIModel(
      VisitorIconFuncId.news,
      "鱼泡资讯",
      AssetImage("assets/images/visitor/ic_mine_func_news.png"),
    ),
    VisitorIconFuncUIModel(
      VisitorIconFuncId.setting,
      "关于我们",
      AssetImage("assets/images/visitor/ic_mine_func_setting.png"),
    ),
  ];

  const VisitorProfileSubpage({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 40.h),
        _buildUserinfoRaw(),
        SizedBox(height: 32.h),
        _buildBusinessFuncBlock(_businessFuncGroup),
        SizedBox(height: 32.h),
        _buildSystemFuncBlock(_systemFuncGroup),
      ],
    );
  }

  Widget _buildUserinfoRaw() => Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "您还未登录",
                      style: TextStyle(
                        color: ColorsUtil.black85,
                        fontSize: 18.sp,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      "(登录后可查看项目及统计)",
                      style: TextStyle(
                        color: ColorsUtil.black85,
                        fontSize: 14.sp,
                      ),
                    ),
                  ]),
            ),
            TextButton(
                onPressed: VisitorFuncGuideUtils.showPrivacyTipsDialog,
                style: ButtonStyle(
                  overlayColor: WidgetStateProperty.all(Colors.transparent),
                  splashFactory: NoSplash.splashFactory,
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      "登录",
                      style: TextStyle(
                        color: ColorsUtil.ypPrimaryColor,
                        fontSize: 14.sp,
                      ),
                    ),
                    Image.asset(
                        "assets/images/common/ic_nav_arrow_right_primary52.png",
                        width: 14.w,
                        height: 14.w)
                  ],
                ))
          ],
        ),
      );

  Widget _buildBusinessFuncBlock(List<VisitorIconFuncUIModel> funcGroup) =>
      Container(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: GridView.builder(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 4,
            mainAxisExtent: 64.h,
          ),
          itemCount: funcGroup.length,
          itemBuilder: (context, index) =>
              _buildFuncCell(funcGroup[index], 32.w),
        ),
      );

  Widget _buildSystemFuncBlock(List<VisitorIconFuncUIModel> funcGroup) =>
      Container(
        padding: EdgeInsets.symmetric(vertical: 16.h),
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(children: [
          Container(
            padding: EdgeInsets.only(left: 20.w),
            alignment: Alignment.centerLeft,
            child: Text(
              '设置与帮助',
              style: TextStyle(
                fontSize: 16.sp,
                color: ColorsUtil.black85,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          SizedBox(height: 24.h),
          GridView.builder(
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              mainAxisExtent: 62.h,
            ),
            itemCount: funcGroup.length,
            itemBuilder: (context, index) =>
                _buildFuncCell(funcGroup[index], 26.w),
          )
        ]),
      );

  Widget _buildFuncCell(VisitorIconFuncUIModel um, double imageSize) =>
      GestureDetector(
        onTap: () => onFuncClick(um.id),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image(
                image: um.icon,
                height: imageSize,
                width: imageSize,
              ),
              SizedBox(height: 6.h),
              Text(
                um.label,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: ColorsUtil.black85,
                ),
              ),
            ]),
      );

  void onFuncClick(VisitorIconFuncId funcId) {
    switch (funcId) {
      case VisitorIconFuncId.attendanceSheet:
      case VisitorIconFuncId.workChecked:
      case VisitorIconFuncId.projectAgreement:
      case VisitorIconFuncId.incomeAndExpenditure:
        VisitorFuncGuideUtils.showPrivacyTipsDialog();
        break;
      case VisitorIconFuncId.share:
        VisitorFuncGuideUtils.showPrivacyTipsDialog();
        break;
      case VisitorIconFuncId.news:
        YPRoute.openPage(RouteNameCollection.yuPaoNews);
        break;
      case VisitorIconFuncId.setting:
        VisitorFuncGuideUtils.showPrivacyTipsDialog();
        break;
    }
  }
}
