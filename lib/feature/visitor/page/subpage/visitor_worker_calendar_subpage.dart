import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/visitor/utils/visitor_func_guide_utils.dart';
import 'package:gdjg_pure_flutter/feature/visitor/widget/calendar/visitor_calendar_mutli_months_widget.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dynamic_height_calendar.dart';
import 'package:gdjg_pure_flutter/widget/quick_month_adjuster.dart';

import '../../../../utils/ui_util/colors_util.dart';
import '../../../../widget/calendar/calendar_week.dart';
import '../../../../widget/calendar/dayevent/DayEvent.dart';

class VisitorWorkerCalendarSubpage extends StatefulWidget {
  const VisitorWorkerCalendarSubpage({super.key});

  @override
  State<StatefulWidget> createState() => _VisitorWorkerCalendarSubpageState();
}

class _VisitorWorkerCalendarSubpageState
    extends State<VisitorWorkerCalendarSubpage> {
  final GlobalKey<VisitorCalendarMultiMonthsWidgetState> _calendarKey =
      GlobalKey<VisitorCalendarMultiMonthsWidgetState>();

  final QuickMonthController _controller =
      QuickMonthController(initial: DateTime.now());

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: 4.h,
          width: double.infinity,
          child: ColoredBox(color: const Color(0xFFF0F0F0)),
        ),
        QuickMonthAdjuster(
          controller: _controller,
          onMonthChanged: (datetime) => _calendarKey.currentState
              ?.updateMonth(datetime.year, datetime.month),
        ),
        Container(
          padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 16.w),
          alignment: Alignment.center,
          decoration: BoxDecoration(color: Colors.white),
          child: Text(
            '您尚未登录，无法查询到您的记工信息',
            style: TextStyle(color: ColorsUtil.black85, fontSize: 16.sp),
          ),
        ),
        CalendarWeek(),
        Expanded(
          child: Stack(
            children: [
              VisitorCalendarMultiMonthsWidget(
                key: _calendarKey,
                onMonthChange: (date) => _controller.set(date),
                onTap: (date) => VisitorFuncGuideUtils.showPrivacyTipsDialog(),
              ),
              Positioned(
                bottom: 30.h,
                left: 0,
                right: 0,
                child: _buildFloatButtons(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFloatButtons() => Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          OutlinedButton(
            onPressed: VisitorFuncGuideUtils.showPrivacyTipsDialog,
            style: OutlinedButton.styleFrom(
                backgroundColor: Colors.white,
                side: BorderSide(color: ColorsUtil.yellowMedium),
                shape: StadiumBorder()),
            child: Text(
              "记借支/结算",
              style: TextStyle(color: ColorsUtil.yellowMedium, fontSize: 14.sp),
            ),
          ),
          SizedBox(width: 16.w),
          OutlinedButton(
            onPressed: VisitorFuncGuideUtils.showPrivacyTipsDialog,
            style: OutlinedButton.styleFrom(
                backgroundColor: Colors.white,
                side: BorderSide(color: ColorsUtil.ypPrimaryColor),
                shape: StadiumBorder()),
            child: Text(
              "记工",
              style:
                  TextStyle(color: ColorsUtil.ypPrimaryColor, fontSize: 14.sp),
            ),
          )
        ],
      );
}
