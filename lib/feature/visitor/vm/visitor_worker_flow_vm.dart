import 'package:gdjg_pure_flutter/feature/visitor/us/visitor_worker_flow_us.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../ui_model/visitor_year_month.dart';

class VisitorWorkerFlowVm {
  final VisitorWorkFlowUS _us = VisitorWorkFlowUS();

  YearMonth? get yearMonth => _us.yearMonth;

  List<DateTime> get workdays => _us.workdays;

  VisitorWorkerFlowVm() {
    _initializeYearMonth();
  }

  void _initializeYearMonth() {
    final DateTime now = DateTime.now();
    updateYearMonth(YearMonth(now.year, now.month));
  }

  void updateYearMonth(YearMonth ym) {
    _us.updateYearMonth(ym);
    final List<DateTime> days;
    final DateTime now = DateTime.now();
    if (now.year == ym.year && now.month == ym.month) {
      // 本月
      days = List.generate(
          now.day, (index) => DateTime(ym.year, ym.month, now.day - index));
    } else {
      // 非本月
      final int daysCount = DateTime(ym.year, ym.month + 1, 0).day;
      days = List.generate(
        daysCount,
        (index) => DateTime(ym.year, ym.month, daysCount - index),
      );
    }
    _us.updateWorkdays(days);
  }
}
