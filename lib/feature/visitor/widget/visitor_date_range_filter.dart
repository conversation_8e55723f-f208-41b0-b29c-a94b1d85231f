import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/widget/start_end_date_picker_widget.dart';

import '../../../utils/route_util/route_api/yp_route.dart';
import '../../../utils/ui_util/button_util.dart';
import '../../../utils/ui_util/month_select_util.dart';

/// 日期范围过滤组件
class VisitorDateRangeFilterWidget extends StatefulWidget {
  final VisitorDateRangeChangedCallback? onDateChangeCallback;

  const VisitorDateRangeFilterWidget({super.key, this.onDateChangeCallback});

  @override
  State<StatefulWidget> createState() => _VisitorDateRangeFilterWidgetState();
}

class _VisitorDateRangeFilterWidgetState
    extends State<VisitorDateRangeFilterWidget> {
  /// 定义一个起始时间
  final DateTime _optionalStart = DateTime(2020, 1, 1);

  /// 动态获取结束时间
  final DateTime _optionalEnd = DateTime.now();

  late DateTime _selectedStart;

  late DateTime _selectedEnd;

  _VisitorDateRangeFilterWidgetState({
    DateTime? selectedStart,
    DateTime? selectedEnd,
  }) {
    _selectedStart = selectedStart ?? _optionalStart;
    _selectedEnd = selectedEnd ?? _optionalEnd;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildDateButton(
            _selectedStart,
            () => _showDatePicker(_selectedStart, true, (date) {
              if (_isSameDate(_selectedStart, date)) {
                return;
              }
              setState(() {
                _selectedStart = date;
                widget.onDateChangeCallback?.call(_selectedStart, _selectedEnd);
              });
            }),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: Text(
              '至',
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF323233),
              ),
            ),
          ),
          _buildDateButton(
            _selectedEnd,
            () => _showDatePicker(_selectedEnd, false, (date) {
              if (_isSameDate(_selectedEnd, date)) {
                return;
              }
              setState(() {
                _selectedEnd = date;
                widget.onDateChangeCallback?.call(_selectedStart, _selectedEnd);
              });
            }),
          ),
          Spacer(),
          ButtonUtil.buildCommonButtonWithBorder(
            height: 30.h,
            width: 50.w,
            text: _isFullMonthSelected()
                ? '${_selectedStart.month.toString().padLeft(2, '0')}月'
                : '月份',
            onPressed: _showMonthPicker,
            selected: _isFullMonthSelected(),
          ),
          SizedBox(width: 8.w),
          ButtonUtil.buildCommonButtonWithBorder(
            height: 30.h,
            width: 50.w,
            text: '全部',
            onPressed: () {
              if (_isAllRangeSelected()) {
                return;
              }
              setState(() {
                _selectedStart = _optionalStart;
                _selectedEnd = _optionalEnd;
                widget.onDateChangeCallback?.call(_selectedStart, _selectedEnd);
              });
            },
            selected: _isAllRangeSelected(),
          )
        ],
      ),
    );
  }

  Widget _buildDateButton(DateTime date, void Function() onTap) =>
      GestureDetector(
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 8.w),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                _formatDate(date),
                style: TextStyle(
                  fontSize: 15.sp,
                  fontWeight: FontWeight.w800,
                  color: const Color(0xFF323233),
                ),
              ),
              Icon(
                Icons.arrow_drop_down,
                size: 18.w,
              ),
            ],
          ),
        ),
      );

  bool _isSameDate(DateTime date1, DateTime date2) =>
      date1.year == date2.year &&
      date1.month == date2.month &&
      date1.day == date2.day;

  /// 🍊
  bool _isAllRangeSelected() =>
      _selectedStart.year == _optionalStart.year &&
      _selectedStart.month == _optionalStart.month &&
      _selectedStart.day == _optionalStart.day &&
      _selectedEnd.year == _optionalEnd.year &&
      _selectedEnd.month == _optionalEnd.month &&
      _selectedEnd.day == _optionalEnd.day;

  bool _isFullMonthSelected() {
    if (_selectedStart.year != _selectedEnd.year ||
        _selectedStart.month != _selectedEnd.month) {
      return false;
    }
    final int year = _selectedStart.year;
    final int month = _selectedStart.month;

    final int dayStrat;
    if (year == _optionalStart.year && month == _optionalStart.month) {
      dayStrat = _optionalStart.day;
    } else {
      dayStrat = 1;
    }

    final int dayEnd;
    if (year == _optionalEnd.year && month == _optionalEnd.month) {
      dayEnd = _optionalEnd.day;
    } else {
      dayEnd = DateTime(year, month + 1, 0).day;
    }
    return _selectedStart.day == dayStrat && _selectedEnd.day == dayEnd;
  }

  String _formatDate(DateTime date) => '${date.year}-${date.month}-${date.day}';

  void _showDatePicker(
    DateTime selected,
    bool isStartDate,
    void Function(DateTime) onDateSelected,
  ) {
    YPRoute.openDialog(
        builder: (context) => StartEndDatePickerWidget(
              initialDate: selected,
              isStartDate: isStartDate,
              minDate: isStartDate ? null : _optionalStart,
              onConfirm: onDateSelected,
            ),
        alignment: Alignment.bottomCenter,
        maskColor: Colors.black.withValues(alpha: 0.5));
  }

  void _showMonthPicker() {
    MonthSelectUtil.show(
      context: context,
      initialYear: _isFullMonthSelected() ? _selectedStart.year : null,
      initialMonth: _isFullMonthSelected() ? _selectedStart.month : null,
      onSelected: (year, month) {
        final int dayStrat;
        if (year == _optionalStart.year && month == _optionalStart.month) {
          dayStrat = _optionalStart.day;
        } else {
          dayStrat = 1;
        }

        final int dayEnd;
        if (year == _optionalEnd.year && month == _optionalEnd.month) {
          dayEnd = _optionalEnd.day;
        } else {
          dayEnd = DateTime(year, month + 1, 0).day;
        }

        final DateTime start = DateTime(year, month, dayStrat);
        final DateTime end = DateTime(year, month, dayEnd);
        if (_isSameDate(_selectedStart, start) &&
            _isSameDate(_selectedEnd, end)) {
          return;
        }
        setState(() {
          _selectedStart = start;
          _selectedEnd = end;
          widget.onDateChangeCallback?.call(start, end);
        });
      },
    );
  }
}

typedef VisitorDateRangeChangedCallback = void Function(
    DateTime start, DateTime end);
