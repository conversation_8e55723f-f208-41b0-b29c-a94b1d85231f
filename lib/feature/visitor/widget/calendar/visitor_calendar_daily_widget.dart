import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lunar/calendar/Lunar.dart';

import '../../../../widget/calendar/utils.dart';

/// 某一天的视图
/// 游客模式,只需要显示日期
class VisitorCalendarDailyWidget extends StatelessWidget {
  final DateTime date;
  final void Function(DateTime date)? onTap;

  const VisitorCalendarDailyWidget({super.key, required this.date, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap?.call(date),
      child: Container(
        margin: EdgeInsets.all(1.w),
        padding: EdgeInsets.symmetric(vertical: 4.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildMainDateInfo(),
            _buildSubDateInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildMainDateInfo() {
    final Color textColor;
    if (_isToday()) {
      textColor = Color(0xFFF54A45);
    } else if (_isFuture()) {
      textColor = Color(0xFF323232);
    } else {
      textColor = Color(0x73000000);
    }
    return Text(
      date.day.toString(),
      style: TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: 16,
        color: textColor,
      ),
    );
  }

  Widget _buildSubDateInfo() => _isToday()
      ? Container(
          padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 6.w),
          decoration: BoxDecoration(
              color: Color(0xFF5290FD),
              borderRadius: BorderRadius.circular(12)),
          child: Text(
            '今天',
            style: TextStyle(fontSize: 12, color: Colors.white),
          ),
        )
      : Padding(
          padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 6.w),
          child: Text(
            _lunar(),
            style: TextStyle(fontSize: 10, color: Color(0x40000000)),
          ),
        );

  bool _isToday() => isSameDay(date, DateTime.now());

  bool _isFuture() => isFutureDate(date);

  String _lunar() => Lunar.fromDate(date).getDayInChinese();
}
