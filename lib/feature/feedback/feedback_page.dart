import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/feature/feedback/entity/feedback_page_entity.dart';
import 'package:gdjg_pure_flutter/feature/feedback/util/feedback_config_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/photo_picker_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';
import 'package:dio/dio.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

/// 反馈页面
/// 支持反馈提交和历史查看两种模式
/// 使用WebView加载H5页面，并提供native功能支持：
/// - 图片选择和上传
/// - 返回导航处理
/// - 二维码保存
class FeedbackPage extends BaseFulPage {
  const FeedbackPage({super.key}) : super(appBar: null);

  @override
  State createState() => _FeedbackPageState();
}

/// 反馈页面常量
class FeedbackPageConstants {
  /// 图片选择器结果码（对应Android的IMAGE_CHOOSER_RESULT_CODE = 0x12）
  static const int imageChooserResultCode = 0x12;
}

class _FeedbackPageState extends BaseFulPageState<FeedbackPage> {
  /// WebView控制器
  WebViewController? _webViewController;

  /// 反馈分类
  String _classStr = "";

  /// 反馈内容
  String _contentStr = "";

  /// 页面参数
  FeedbackPageEntity? _pageParam;

  /// 初始加载的URL
  String? _initialUrl;

  /// WebView初始化状态
  bool _isWebViewInitialized = false;

 /// JavaScript导航设置状态，避免重复绑定
 bool _isNavigationSetup = false;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);

    if (routeParams != null) {
      _pageParam = routeParams as FeedbackPageEntity;

      // 避免重复初始化
      if (!_isWebViewInitialized && _webViewController == null) {
        _initWebView();
      }
    }
  }

  /// 初始化WebView控制器和配置
  void _initWebView() async {
    // 创建WebView控制器
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller = WebViewController.fromPlatformCreationParams(params);

    // 注册JavaScript通道
    await _registerJavaScriptChannels(controller);

    // 配置WebView
    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: (NavigationRequest request) async {
            // 处理特殊协议跳转
            if (request.url.startsWith('weixin://') || request.url.startsWith('yupao://')) {
              if (await canLaunchUrl(Uri.parse(request.url))) {
                await launchUrl(Uri.parse(request.url), mode: LaunchMode.externalApplication);
              }
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onPageFinished: (String url) {
            // 页面加载完成后设置H5交互功能
            Future.delayed(const Duration(milliseconds: 300), () {
              _setupH5Navigation();
            });
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('WebView资源错误: ${error.description}');
          },
        ),
      );

    // 平台特殊配置
    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);

      // 设置文件选择处理
      (controller.platform as AndroidWebViewController).setOnShowFileSelector(_onFileSelector);
    }

    _webViewController = controller;

    // 加载URL
    _loadFeedbackUrl();

    // 标记WebView已初始化
    setState(() {
      _isWebViewInitialized = true;
    });
  }

  /// 注册JavaScript通道，用于H5与native交互
  Future<void> _registerJavaScriptChannels(WebViewController controller) async {
    try {
      await controller.addJavaScriptChannel(
        'ypNative',
        onMessageReceived: (JavaScriptMessage message) {
          try {
            final data = jsonDecode(message.message);
            final method = data['method'] as String?;

            switch (method) {
              case 'back':
                _handleBack();
                break;
              case 'contentCallback':
                _handleContentCallback(data);
                break;
              default:
                debugPrint('未知的H5调用方法: $method');
            }
          } catch (e) {
            debugPrint('处理H5消息失败: $e');
          }
        },
      );
    } catch (e) {
      debugPrint('注册JavaScript通道失败: $e');
    }
  }

  /// 处理来自H5的反馈成功事件
  void _handleFeedbackSuccess() {
    debugPrint('H5反馈成功，将关闭页面。');
    if (mounted && Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }
  }

  /// 设置H5页面导航功能，拦截返回按钮点击
  void _setupH5Navigation() async {
    if (_webViewController == null) return;

    try {
      final jsCode = '''
        (function() {
          try {
            // 返回处理
            window.handleNativeBack = function() {
              if (window.ypNative && window.ypNative.postMessage) {
                window.ypNative.postMessage(JSON.stringify({method: 'back'}));
              }
            };

            // 桥接垫片：兼容 s.sendMessageNative 和 sendMessageNative
            try {
              if (!window.s) { window.s = {}; }
              if (typeof window.s.sendMessageNative !== 'function') {
                window.s.sendMessageNative = function(payload) {
                  try {
                    var data = typeof payload === 'string' ? payload : JSON.stringify(payload || {});
                    if (window.ypNative && window.ypNative.postMessage) {
                      window.ypNative.postMessage(data);
                    } else if (window.flutter_inappwebview) {
                      try { window.flutter_inappwebview.callHandler('ypNative', data); } catch (e) {}
                    } else if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.ypNative) {
                      try { window.webkit.messageHandlers.ypNative.postMessage(data); } catch (e) {}
                    }
                  } catch (e) {}
                };
              }
              if (typeof window.sendMessageNative !== 'function') {
                window.sendMessageNative = window.s.sendMessageNative;
              }
            } catch (e) {}

            // 仅绑定指定返回元素
            var selectors = [
              'div.header_header-back-icon__mXrkJ',
              'div[class*="header_header-back-icon__"]',
              'img.header_icon__tDgmS',
              'img[src*="header_back"]'
            ];
            selectors.forEach(function(selector) {
              try {
                var buttons = document.querySelectorAll(selector);
                buttons.forEach(function(btn) {
                  if (btn.hasAttribute('data-native-back-bound')) return;
                  btn.setAttribute('data-native-back-bound', 'true');
                  btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    window.handleNativeBack();
                  }, true);
                });
              } catch (e) {}
            });

            // 监听成功提示后延时返回
            try {
              if (!window.__nativeBackTriggeredByToast) { window.__nativeBackTriggeredByToast = false; }
              var hasKeyword = function(text) {
                if (!text) return false;
                return text.indexOf('反馈成功') !== -1 || text.indexOf('提交成功') !== -1;
              };
              var tryTriggerByToast = function(text) {
                try {
                  if (window.__nativeBackTriggeredByToast) return;
                  if (hasKeyword(text)) {
                    window.__nativeBackTriggeredByToast = true;
                    setTimeout(function(){ try { window.handleNativeBack(); } catch(e) {} }, 1200);
                  }
                } catch (e) {}
              };
              var originalAlert = window.alert;
              window.alert = function(msg){ tryTriggerByToast(msg); if (originalAlert) { return originalAlert.apply(this, arguments); } };
              var obs = new MutationObserver(function(mutations){
                try {
                  for (var i=0;i<mutations.length;i++) {
                    var m = mutations[i];
                    if (m.addedNodes) {
                      for (var j=0;j<m.addedNodes.length;j++) {
                        var node = m.addedNodes[j];
                        if (!node) continue;
                        if (node.nodeType === 3) { tryTriggerByToast(node.nodeValue || ''); }
                        else if (node.nodeType === 1) { var txt=''; try { txt=(node.textContent||'').trim(); } catch(e){} tryTriggerByToast(txt); }
                      }
                    }
                  }
                } catch(e){}
              });
              try { obs.observe(document.body || document.documentElement, { childList: true, subtree: true }); } catch(e) {}
            } catch (e) {}
          } catch (e) {}
        })();
      ''';

      await _webViewController!.runJavaScript(jsCode);
      _isNavigationSetup = true;
      debugPrint('H5导航功能设置完成');
    } catch (e) {
      debugPrint('设置H5导航功能失败: $e');
    }
  }


  /// 处理来自H5的返回事件 - 智能返回（优先WebView内部返回）
  void _handleBack() {
    _performSmartBackAction();
  }

  /// 智能返回实现
  void _performSmartBackAction() async {
    try {
      if (_webViewController == null) {
        if (mounted && Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
        return;
      }

      final canGoBack = await _webViewController!.canGoBack();
      if (canGoBack) {
        await _webViewController!.goBack();
      } else {
        if (mounted && Navigator.canPop(context)) {
          Navigator.of(context).pop();
        }
      }
    } catch (e) {
      if (mounted && Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
    }
  }

  /// 处理来自系统（如手势）的返回事件 - 智能返回
  Future<bool> _handleWillPop() async {
    _performSmartBackAction();
    return false;
  }

  /// 处理H5内容回调
  void _handleContentCallback(Map<String, dynamic> data) {
    _classStr = data['classification'] ?? "";
    _contentStr = data['content'] ?? "";
  }

  /// 处理保存二维码
  void _handleSaveQrCode(Map<String, dynamic> data) async {
    final url = data['url'] as String?;
    if (url == null || url.isEmpty) return;

    // 检查存储权限
    bool hasPermission = await _checkStoragePermission();
    if (!hasPermission) return;

    // 下载并保存图片
    await _saveImageFromUrl(url);
  }

  /// 检查存储权限
  Future<bool> _checkStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.photos.request();
      if (!status.isGranted) {
        ToastUtil.showToast('需要存储权限才能保存图片');
        return false;
      }
    } else if (Platform.isIOS) {
      final status = await Permission.photos.request();
      if (!status.isGranted) {
        ToastUtil.showToast('需要相册权限才能保存图片');
        return false;
      }
    }
    return true;
  }

  /// 从URL下载并保存图片到相册
  Future<void> _saveImageFromUrl(String url) async {
    try {
      final dio = Dio();
      final response = await dio.get<Uint8List>(
        url,
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200 && response.data != null) {
        final success = await PhotoPickerUtil.saveImageToGallery(
          response.data!,
          fileName: '_jgjz_kf_${DateTime.now().millisecondsSinceEpoch}.jpg',
        );

        if (success) {
          ToastUtil.showToast('已保存至系统相册');
        } else {
          ToastUtil.showToast('保存失败');
        }
      } else {
        ToastUtil.showToast('下载图片失败');
      }
    } catch (e) {
      debugPrint('保存图片失败: $e');
      ToastUtil.showToast('保存失败');
    }
  }

  /// 文件选择处理（用于H5上传图片）
  Future<List<String>> _onFileSelector(FileSelectorParams params) async {
    try {
      // 检查是否为图片选择
      final acceptTypes = params.acceptTypes;
      if (acceptTypes.isNotEmpty && acceptTypes.first == 'image/*') {
        return await _selectImageWithProperFormat();
      }
    } catch (e) {
      debugPrint('文件选择失败: $e');
      ToastUtil.showToast('选择图片失败');
    }
    return [];
  }

  /// 选择图片并返回正确格式的URI
  Future<List<String>> _selectImageWithProperFormat() async {
    try {
      // 检查权限
      final hasPermission = await PhotoPickerUtil.checkStoragePermission();
      if (!hasPermission) {
        ToastUtil.showToast('需要相册权限才能选择图片');
        return [];
      }

      // 使用图片选择器
      final List<AssetEntity>? result = await AssetPicker.pickAssets(
        context,
        pickerConfig: AssetPickerConfig(
          maxAssets: 1,
          requestType: RequestType.image,
          themeColor: const Color(0xFF3986eb),
          textDelegate: const AssetPickerTextDelegate(),
        ),
      );

      if (result == null || result.isEmpty) {
        return [];
      }

      // 获取文件并转换为正确的URI格式
      final file = await result.first.file;
      if (file != null && await file.exists()) {
        final filePath = file.path;

        // 确保返回正确的文件URI格式
        String fileUri;
        if (Platform.isAndroid) {
          fileUri = filePath.startsWith('file://') ? filePath : 'file://$filePath';
        } else {
          fileUri = filePath;
        }

        return [fileUri];
      } else {
        return [];
      }

    } catch (e) {
      debugPrint('选择图片过程出错: $e');
      ToastUtil.showToast('选择图片失败');
      return [];
    }
  }

  /// 构建并加载反馈URL
  void _loadFeedbackUrl() async {
    if (_pageParam == null || _webViewController == null) return;

    final baseUrl = FeedbackConfigUtil.getMHyWebUrl();
    final typePath = _pageParam!.type == FeedbackPageEntity.typeFeedback ? 'feedback/add' : 'feedback/history';

    // 构建参数
    final params = await _buildUrlParams();
    final finalUrl = '$baseUrl$typePath$params';

    debugPrint('=== 反馈页面加载信息 ===');
    debugPrint('页面类型: ${_pageParam!.type}');
    debugPrint('基础URL: $baseUrl');
    debugPrint('类型路径: $typePath');
    debugPrint('参数: $params');
    debugPrint('最终URL: $finalUrl');
    debugPrint('===================');

    // 只在第一次加载时记录初始URL
    if (_initialUrl == null) {
      _initialUrl = finalUrl;
      debugPrint('设置初始URL: $_initialUrl');
    }

    // 加载URL
    await _webViewController!.loadRequest(Uri.parse(finalUrl));
  }

  /// 构建URL参数
  Future<String> _buildUrlParams() async {
    if (_pageParam == null) return '';

    // 获取动态构建的headers
    final headers = await FeedbackConfigUtil.getHeaders();
    
    String params = '?configId=${_pageParam!.configId}'
        '&appId=${_pageParam!.appId}'
        '&resource=${FeedbackConfigUtil.getSource()}'
        '&tenatId='
        '&session=${FeedbackConfigUtil.getUserToken()}'
        '&source=0'
        '&uid=${FeedbackConfigUtil.getUserId()}'
        '&userrole=null'
        '&headers=$headers';

    // 添加问题类型参数
    if (_pageParam!.questionType != null && _pageParam!.questionType!.isNotEmpty) {
      params += '&classificationId=${_pageParam!.questionType}';
    }

    return params;
  }

  @override
  Widget yBuild(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        // 处理手势返回和系统返回键
        if (_webViewController != null) {
          return await _handleWillPop();
        }
        return true; // WebView未初始化时允许直接返回
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          // 确保页面内容不与状态栏重叠
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: double.infinity,
            color: Colors.white,
            child: Stack(
              children: [
                _buildWebView()
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWebView() {
    // 检查WebView控制器是否已初始化
    if (_webViewController == null) {
      debugPrint('WebView控制器未初始化，显示加载中...');
      return const Center(
        child: CircularProgressIndicator(),
      );
    }
    
    if (WebViewPlatform.instance is AndroidWebViewPlatform) {
      return WebViewWidget.fromPlatformCreationParams(
        params: AndroidWebViewWidgetCreationParams(
          controller: _webViewController!.platform,
          displayWithHybridComposition: false,
        ),
      );
    }
    return WebViewWidget(controller: _webViewController!);
  }

  @override
  void dispose() {
    // 清理WebView控制器
    _webViewController = null;
    _isWebViewInitialized = false;

   // 重置导航设置状态
   _isNavigationSetup = false;

    // 清理反馈内容状态
    _contentStr = "";
    _classStr = "";

    super.dispose();
  }
}
