import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_put_away_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/repo/corp_select_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart';
import 'package:gdjg_pure_flutter/feature/common_page/change_project_name/change_project_name_page.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/WageRulesSettingDialog.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/entity/wage_rules_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/worker_project_unsettled_page.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/show_delete_confirm_dialog.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/repo/worker_flow_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_index_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';

import 'my_created_project_setup_us.dart';

/// 我创建的项目设置ViewModel
class MyCreatedProjectSetupVM {
  final _workerProjectRep = WorkerProjectRepo();
  final _corpSelectRepo = CorpSelectRepo();
  final _workerFlowRepo = WorkerFlowRepo();
  final us = MyCreatedProjectSetupUS();

  String? _projectId;
  double? _deptId;
  double? _corpId;
  double? _workerId;
  bool _isFromCompleted = false;

  /// 获取是否来自已结清项目
  bool get isFromCompleted => _isFromCompleted;

  /// 初始化数据
  void init(String? projectId, {bool isFromCompleted = false}) {
    _projectId = projectId;
    _isFromCompleted = isFromCompleted;
    _loadPrivacySettings();
    fetchData();
  }

  /// 获取项目设置数据
  void fetchData() async {
    if (_projectId == null || _projectId!.isEmpty) {
      return;
    }

    final result = await _workerProjectRep.getProject(_projectId!);

    if (result.isOK() && result.getSucData() != null) {
      final data = result.getSucData()!;

      // 部门ID用于设为已结清
      _deptId = data.deptId;
      // 企业ID用于企业切换
      _corpId = data.corpId;
      // 工人ID用于工价设置
      _workerId = data.workerId;

      // 更新UI状态
      us.setProjectName(data.name);
      us.setPerWorkPrice(_buildWageDisplayText(
          data.feeStandard?.workingHoursStandard,
          data.feeStandard?.workingHoursPrice));
      us.setContractPrice(_buildWageDisplayText(
          data.contractorFeeStandard?.workingHoursStandard,
          data.contractorFeeStandard?.workingHoursPrice));

      // 设置点工加班相关数据
      us.setPerWorkOvertimeType(data.feeStandard?.overtimeType ?? 0.0);
      us.setPerWorkOvertimeHoursStandard(
          data.feeStandard?.overtimeHoursStandard ?? '');
      us.setPerWorkOvertimeHoursPrice(
          data.feeStandard?.overtimeHoursPrice ?? '');

      // 设置包工加班相关数据
      us.setContractOvertimeType(
          data.contractorFeeStandard?.overtimeType ?? 0.0);
      us.setContractOvertimeHoursStandard(
          data.contractorFeeStandard?.overtimeHoursStandard ?? '');
      us.setContractOvertimeHoursPrice(
          data.contractorFeeStandard?.overtimeHoursPrice ?? '');

      // 获取总未结金额
      _fetchTotalUnsettledAmount(data);
    }
  }

  /// 加载隐私设置
  void _loadPrivacySettings() {
    final hideMyWage = _workerProjectRep.getHideMyWageWhenShare();
    us.setHideMyWageWhenShare(hideMyWage);
  }

  /// 保存隐私设置
  void _savePrivacySettings() {
    _workerProjectRep.setHideMyWageWhenShare(us.hideMyWageWhenShare);
  }

  /// 获取总未结金额
  void _fetchTotalUnsettledAmount(dynamic projectData) async {
    if (_projectId == null || _projectId!.isEmpty || projectData == null) {
      us.setTotalUnsettledAmount(0.0);
      return;
    }

    // 构建业务统计请求参数
    final param = BusinessCountParamModel(
      identity: "2", // 个人流水
      start_time: projectData.startTime ?? DateUtil.formatStartDate(DateTime.now()),
      end_time: projectData.endTime ?? DateUtil.formatEndDate(DateTime.now()),
      status: "0", // 未结清项目
      work_notes: _projectId!,
    );

    // 调用业务统计接口
    final result = await _workerFlowRepo.fetchHeaderData(param);

    if (result.isOK() && result.getSucData() != null) {
      final data = result.getSucData()!;
      us.setTotalUnsettledAmount(data.unsettled);
    } else {
      us.setTotalUnsettledAmount(0.0);
    }
  }

  /// 格式化数字
  String _formatNumber(String? numberStr) {
    if (numberStr == null || numberStr.isEmpty) {
      return '';
    }

    try {
      double number = double.parse(numberStr);
      // 如果是整数，直接显示整数
      if (number == number.toInt()) {
        return number.toInt().toString();
      }
      // 如果有小数，保留小数最多两位
      String formatted = number.toStringAsFixed(2);
      formatted = formatted.replaceAll(RegExp(r'\.?0+$'), '');
      return formatted;
    } catch (e) {
      return numberStr;
    }
  }

  /// 工价文本
  String _buildWageDisplayText(String? hoursStandard, String? hoursPrice) {
    if (hoursStandard == null ||
        hoursPrice == null ||
        hoursStandard.isEmpty ||
        hoursPrice.isEmpty ||
        hoursStandard == "0" ||
        hoursStandard == "0.0" ||
        hoursPrice == "0" ||
        hoursPrice == "0.0") {
      return '未设置';
    }

    String formattedStandard = _formatNumber(hoursStandard);
    String formattedPrice = _formatNumber(hoursPrice);

    return '1个工$formattedStandard小时$formattedPrice元';
  }

  /// 点击项目信息
  void onProjectInfoTap() {
    if (_deptId == null) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    YPRoute.openPage(RouteNameCollection.changeProjectName,
        params: ChangeProjectNameProps(
          deptId: _deptId!.toInt().toString(),
          currentName: us.projectName,
        ));
  }

  /// 点击点工工价设置
  void onPointWorkWageTap() {
    if (_projectId == null || _projectId!.isEmpty) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    if (_workerId == null) {
      ToastUtil.showToast('工人信息错误');
      return;
    }

    var props = WageRulesProps(
      title: '点工工价设置',
      workNoteId: _projectId!,
      businessType: RwaRecordType.workDays,
      recordNoteType: RecordNoteType.group,
      confirmRequestApi: true,
      workers: [
        WorkerModel(
          workerId: _workerId,
          workerName: ''
        )
      ],
    );
    WageRulesSettingDialog.show(props: props, onSelected: (feeInfo){
      fetchData();
    });
  }

  /// 点击包工工价设置
  void onContractWorkWageTap() {
    if (_projectId == null || _projectId!.isEmpty) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    if (_workerId == null) {
      ToastUtil.showToast('工人信息错误');
      return;
    }

    var props = WageRulesProps(
      title: '包工工价设置',
      workNoteId: _projectId!,
      businessType: RwaRecordType.packageWork,
      recordNoteType: RecordNoteType.group,
      confirmRequestApi: true,
      workers: [
        WorkerModel(
          workerId: _workerId,
          workerName: '',
        )
      ],
    );
    WageRulesSettingDialog.show(props: props, onSelected: (feeInfo){
      fetchData();
    });
  }

  /// 切换隐私设置
  void onPrivacyToggle(bool value) {
    us.setHideMyWageWhenShare(value);
    _savePrivacySettings();
  }

  /// 点击统计
  void onStatisticsTap() {
    if (_projectId == null || _projectId!.isEmpty) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    if (_deptId == null) {
      ToastUtil.showToast('部门信息错误');
      return;
    }

    final params = GroupProBillProps(
      workNoteId: _projectId!,
      workNoteName: us.projectName,
      deptId: _deptId,
      isJoin: false,
    );

    // 导航到统计页面
    YPRoute.openPage(RouteNameCollection.groupProBill, params: params);
  }

  /// 点击总未结
  void onSettlementTap() {
    if (_projectId == null || _projectId!.isEmpty) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    YPRoute.openPage(
      RouteNameCollection.workerProjectUnsettled,
      params: WorkerProjectUnsettledPageArgs(
        projectId: _projectId!,
        projectName: us.projectName,
      ),
    );
  }

  /// 点击记结算
  void onRecordSettlementTap() async {
    if (_projectId == null) return;

    ToastUtil.showToast('记结算功能开发中');
  }

  /// 点击设为已结清
  void onMarkAsSettledTap() async {
    if (_deptId == null) return;

    showCommonDialog(CommonDialogConfig(
      title: '确认设为已结清吗？',
      content: '设为已结清后，不参与统计和展示',
      negative: '取消',
      positive: '确定',
      onPositive: () => _confirmMarkAsSettled(),
    ));
  }

  /// 确认设为已结清
  void _confirmMarkAsSettled() async {
    if (_deptId == null) return;

    // 企业切换
    if (_corpId != null && _corpId! > 0) {
      final corpSelectParam = CorpSelectParamModel(
        corp_id: _corpId!.toInt().toString(),
      );

      await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
    }

    // 再执行已结清操作
    final param = DeptPutAwayDeptParamModel(
      dept_id: _deptId!.toInt().toString(),
      is_ignored: '1',
    );

    final result = await _workerProjectRep.putAwayDept(param);

    if (result.isOK()) {
      ToastUtil.showToast('设为已结清成功');
      EventBusUtil.emit<String>('project_settle');
      YPRoute.closeDialog();
      YPRoute.closePage({'refresh': true});
    } else {
      ToastUtil.showToast('设为已结清失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }

  /// 点击恢复为在建
  void onRecoverToActiveTap() async {
    if (_deptId == null) return;

    showCommonDialog(CommonDialogConfig(
      title: '确认恢复为在建吗？',
      content: '恢复为在建后，将参与统计和展示',
      negative: '取消',
      positive: '确定',
      onPositive: () => _confirmRecoverToActive(),
    ));
  }

  /// 确认恢复为在建
  void _confirmRecoverToActive() async {
    if (_deptId == null) return;

    // 企业切换
    if (_corpId != null && _corpId! > 0) {
      final corpSelectParam = CorpSelectParamModel(
        corp_id: _corpId!.toInt().toString(),
      );

      await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
    }

    // 恢复为在建操作
    final param = DeptPutAwayDeptParamModel(
      dept_id: _deptId!.toInt().toString(),
      is_ignored: '0', // 0表示恢复为在建
    );

    final result = await _workerProjectRep.putAwayDept(param);

    if (result.isOK()) {
      ToastUtil.showToast('恢复为在建成功');
      EventBusUtil.emit<String>('project_recover');
      YPRoute.closeDialog();

      // 从已结清项目进入的，需要关闭两个页面回到已结清tab
      if (_isFromCompleted) {
        YPRoute.closePage({'refresh': true});
        YPRoute.closePage({'refresh': true});
      } else {
        YPRoute.closePage({'refresh': true});
      }
    } else {
      ToastUtil.showToast('恢复为在建失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }

  /// 点击彻底删除
  void onDeleteProjectTap() async {
    if (_deptId == null) return;

    showDeleteConfirmDialog(
      onConfirm: () => _confirmDeleteProject(),
    );
  }

  /// 确认彻底删除项目
  void _confirmDeleteProject() async {
    if (_deptId == null) return;

    // 企业切换
    if (_corpId != null && _corpId! > 0) {
      final corpSelectParam = CorpSelectParamModel(
        corp_id: _corpId!.toInt().toString(),
      );

      await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
    }

    // 删除项目操作
    final result = await _workerProjectRep.deleteProject(_deptId!.toInt().toString());

    if (result.isOK()) {
      ToastUtil.showToast('删除项目成功');
      EventBusUtil.emit<String>('project_delete');
      YPRoute.closeDialog();

      // 如果是从已结清项目进入的，需要关闭两个页面回到已结清tab
      if (_isFromCompleted) {
        YPRoute.closePage({'refresh': true});
        YPRoute.closePage({'refresh': true});
      } else {
        YPRoute.closePage({'refresh': true});
      }
    } else {
      ToastUtil.showToast('删除项目失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }
}
