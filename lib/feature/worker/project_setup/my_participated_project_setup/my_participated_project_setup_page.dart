import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:get/get.dart';

import 'vm/my_participated_project_setup_vm.dart';

class MyParticipatedProjectSetupProps {
  final String? deptId;
  final bool isFromCompleted;

  MyParticipatedProjectSetupProps({
    this.deptId,
    this.isFromCompleted = false,
  });
}

/// 我参与的项目页面
class MyParticipatedProjectSetupPage extends BaseFulPage {
  const MyParticipatedProjectSetupPage({super.key})
      : super(appBar: const YPAppBar(title: "项目设置"));

  @override
  State createState() => _MyParticipatedProjectSetupPageState();
}

class _MyParticipatedProjectSetupPageState extends BaseFulPageState {
  late MyParticipatedProjectSetupProps? props;
  final MyParticipatedProjectSetupVM viewModel = MyParticipatedProjectSetupVM();
  StreamSubscription? _eventSubscription;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as MyParticipatedProjectSetupProps?;
    viewModel.init(props?.deptId, isFromCompleted: props?.isFromCompleted ?? false);

    // 监听项目状态变更事件
    _eventSubscription = EventBusUtil.collect<String>((eventType) {
      // 项目更新
      if (eventType == 'project_update') {
        viewModel.fetchData();
      }
    });
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _eventSubscription?.cancel();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F6FA),
      body: _buildContent(),
    );
  }

  /// 构建页面内容
  Widget _buildContent() {
    return SingleChildScrollView(
      child: Column(
        children: [
          /// 项目和分享时设置
          _buildMainSettingsSection(),
          // 统计和总未结
          _buildStatisticsAndSettlementSection(),
          // 已结清
          _buildMarkAsSettledSection(),
        ],
      ),
    );
  }

  /// 项目和分享时设置
  Widget _buildMainSettingsSection() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 8.h),
      child: Column(
        children: [
          ListTile( 
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
            title: Text(
              '项目',
              style: TextStyle(
                fontSize: 17.sp,
                color: const Color(0xFF222222),
              ),
            ),
            trailing: Obx(() => Text(
              viewModel.us.projectName,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: const Color(0xFF666666),
              ),
            )),
          ),
          Divider(
            height: 1.h,
            color: const Color(0xFFF5F6FA),
            indent: 16.w,
            endIndent: 16.w,
          ),
          ListTile(
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
            title: Text(
              '分享时，不展示工友工资',
              style: TextStyle(
                fontSize: 17.sp,
                color: const Color(0xFF222222),
              ),
            ),
            trailing: Obx(() => _buildCustomSwitch()),
          ),
        ],
      ),
    );
  }

  /// 统计和总未结
  Widget _buildStatisticsAndSettlementSection() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 8.h),
      child: Column(
        children: [
          ListTile(
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
            title: Text(
              '统计',
              style: TextStyle(
                fontSize: 17.sp,
                color: const Color(0xFF222222),
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '对工',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w400,
                    color: const Color(0xFF666666),
                  ),
                ),
                SizedBox(width: 8.w),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.w,
                  color: const Color(0xFF999999),
                ),
              ],
            ),
            onTap: viewModel.onStatisticsTap,
          ),
          // 只有非已结清项目才显示未结行
          if (!viewModel.isFromCompleted) ...[
            Divider(
              height: 1.h,
              color: const Color(0xFFF5F6FA),
              indent: 16.w,
              endIndent: 16.w,
            ),
            ListTile(
              contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
              title: Text(
                '未结',
                style: TextStyle(
                  fontSize: 17.sp,
                  color: const Color(0xFF222222),
                ),
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Obx(() => Text(
                    viewModel.us.totalUnsettledAmount.toStringAsFixed(2),
                    style: TextStyle(
                      fontFamily: FontUtil.fontCondMedium,
                      fontSize: 22.sp,
                      color: const Color(0xFF5290FD),
                      fontWeight: FontWeight.w400,
                    ),
                  )),
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.w,
                    color: const Color(0xFF999999),
                  ),
                ],
              ),
              onTap: viewModel.onSettlementTap,
            ),
          ],
        ],
      ),
    );
  }

  /// 已结清/恢复为在建
  Widget _buildMarkAsSettledSection() {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 8.h),
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 2.h),
        title: Center(
          child: Text(
            viewModel.isFromCompleted ? '恢复为在建' : '设为已结清',
            style: TextStyle(
              fontSize: 15.sp,
              color: viewModel.isFromCompleted ? Colors.green : Colors.redAccent,
            ),
          ),
        ),
        onTap: viewModel.isFromCompleted
            ? viewModel.onRecoverToActiveTap
            : viewModel.onMarkAsSettledTap,
      ),
    );
  }

  /// Switch
  Widget _buildCustomSwitch() {
    return GestureDetector(
      onTap: () => viewModel.onPrivacyToggle(!viewModel.us.hideWorkerWageWhenShare),
      child: SizedBox(
        width: 50.w,
        height: 30.h,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // 导轨
            Container(
              width: 24.w,
              height: 14.h,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.h),
                color: viewModel.us.hideWorkerWageWhenShare
                    ? const Color(0xFF4CAF50)
                    : Colors.grey.shade400,
              ),
            ),
            // 按钮
            AnimatedAlign(
              duration: const Duration(milliseconds: 200),
              alignment: viewModel.us.hideWorkerWageWhenShare
                  ? Alignment.centerRight
                  : Alignment.centerLeft,
              child: Container(
                width: 22.w,
                height: 22.h,
                margin: EdgeInsets.symmetric(horizontal: 2.w),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.4),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
