import 'package:gdjg_pure_flutter/data/worker_data/invite_data/repo/invite_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/invite_data/ds/model/param/invite_info_param_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/qr_scan/vm/qr_scan_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/invite_join/vm/invite_join_vm.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/regex/regex_utils.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

/// 二维码扫描业务逻辑层
class QRScanVM {
  final QRScanUS us = QRScanUS();
  late final MobileScannerController scannerController;
  final _inviteRepo = InviteRepo();

  /// 初始化
  void init() {
    scannerController = MobileScannerController();
  }

  /// 销毁资源
  void dispose() {
    scannerController.dispose();
  }



  /// 处理扫码结果
  void onScanResult(BarcodeCapture capture) {
    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isNotEmpty) {
      final String? code = barcodes.first.rawValue;
      if (code != null && code.isNotEmpty) {
        scannerController.stop();
        _processScanResult(code);
      }
    }
  }

  /// 处理扫码结果
  void _processScanResult(String code) {
    us.setProcessingResult(true);

    // 模拟处理延迟
    Future.delayed(const Duration(milliseconds: 500), () {
      us.setProcessingResult(false);

      // 判断扫描内容是否为邀请链接
      if (RegexUtils.isUrl(code) && _hasInviteParams(code)) {
        _handleInviteUrl(code);
      } else {
        // 如果不是邀请链接，显示提示并关闭页面
        ToastUtil.showToast('请扫描班组长分享的二维码');
        YPRoute.closePage();
      }
    });
  }

  /// 切换手电筒
  void toggleFlashlight() {
    try {
      scannerController.toggleTorch();
      us.setFlashlight(!us.isFlashlightOn.value);
    } catch (e) {
      ToastUtil.showToast('手电筒操作失败');
    }
  }



  /// 重新开始扫描
  void restartScanning() {
    // 重新开始扫描逻辑
  }

  /// 分析图片中的二维码
  Future<void> analyzeImageFromPath(String imagePath) async {
    try {
      us.setProcessingResult(true);

      // mobile_scanner的analyzeImage方法识别图片
      final result = await scannerController.analyzeImage(imagePath);

      us.setProcessingResult(false);

      if (result != null && result.barcodes.isNotEmpty) {
        final String? code = result.barcodes.first.rawValue;
        if (code != null && code.isNotEmpty) {
          _processScanResult(code);
        } else {
          ToastUtil.showToast('图片中未找到有效的二维码');
        }
      } else {
        ToastUtil.showToast('图片中未找到二维码');
      }
    } catch (e) {
      us.setProcessingResult(false);
      ToastUtil.showToast('图片二维码识别失败');
    }
  }

  /// 显示帮助信息
  void showHelpInfo() {
    YPRoute.openPage(RouteNameCollection.whatScan);
  }

  /// 处理邀请URL
  Future<void> _handleInviteUrl(String url) async {
    try {
      // 解析URL参数
      final token = _extractToken(url);
      final deptId = _extractDeptId(url);

      if (token == null || deptId == null) {
        ToastUtil.showToast('邀请链接参数不完整');
        YPRoute.closePage();
        return;
      }

      final param = InviteInfoParamModel(
        token: token,
        deptId: deptId,
      );

      final result = await _inviteRepo.getInviteInfo(param);

      if (result.isOK()) {
        final inviteInfo = result.getSucData()!;
        final pageParam = InviteJoinPageParam(
          inviteInfo: inviteInfo,
          token: token,
        );
        YPRoute.openPage(RouteNameCollection.inviteJoin, params: pageParam, closeNumBeforeOpen: 1);
      } else {
        ToastUtil.showToast(result.fail?.errorMsg ?? '获取邀请信息失败');
        YPRoute.closePage();
      }
    } catch (e) {
      ToastUtil.showToast('处理邀请链接失败');
      YPRoute.closePage();
    }
  }

  /// 解析URL中的查询参数
  Map<String, String> _parseQueryParameters(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.queryParameters;
    } catch (e) {

      return {};
    }
  }

  /// 从URL中提取token参数
  String? _extractToken(String url) {
    final params = _parseQueryParameters(url);
    return params['token'];
  }

  /// 从URL中提取dept_id参数
  String? _extractDeptId(String url) {
    final params = _parseQueryParameters(url);
    return params['dept_id'];
  }

  /// 检查URL是否包含必要的邀请参数
  bool _hasInviteParams(String url) {
    final params = _parseQueryParameters(url);
    return params.containsKey('token') && params.containsKey('dept_id');
  }
}
