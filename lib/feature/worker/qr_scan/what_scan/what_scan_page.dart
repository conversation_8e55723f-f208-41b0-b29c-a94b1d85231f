import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/qr_scan/what_scan/vm/what_scan_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 扫码教程页面
class WhatScanPage extends BaseFulPage {
  const WhatScanPage({super.key}) : super(
    appBar: const YPAppBar(title: "个人记工")
  );

  @override
  State createState() => _WhatScanPageState();
}

class _WhatScanPageState extends BaseFulPageState<WhatScanPage> {
  late final WhatScanVM viewModel;

  @override
  void onPageCreate() {
    super.onPageCreate();
    viewModel = WhatScanVM();
    viewModel.init();
  }

  @override
  void onPageDestroy() {
    viewModel.dispose();
    super.onPageDestroy();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Column(
        children: [
          Container(
            width: double.infinity,
            height: 8.h,
            color: const Color(0xFFF0F0F0),
          ),
          Expanded(
            child: Container(
              color: Colors.white,
              child: Padding(
                padding: EdgeInsets.all(16.w),
                child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '1、需要扫什么码',
                    style: TextStyle(
                      color: Colors.black,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 17.h),
                  RichText(
                    text: TextSpan(
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 14.sp,
                      ),
                      children: [
                        const TextSpan(text: '该二维码为班组长的项目二维码，让班组长在'),
                        TextSpan(
                          text: '【项目页】',
                          style: TextStyle(color: ColorsUtil.primaryColor),
                        ),
                        const TextSpan(text: '，点击需要加入项目的'),
                        TextSpan(
                          text: '【设置】',
                          style: TextStyle(color: ColorsUtil.primaryColor),
                        ),
                        const TextSpan(text: '按钮，再点击'),
                        TextSpan(
                          text: '【项目二维码】',
                          style: TextStyle(color: ColorsUtil.primaryColor),
                        ),
                        const TextSpan(text: '。\n扫描该二维码，即可加入该项目。'),
                      ],
                    ),
                  ),
                  SizedBox(height: 17.h),
                  Container(
                    width: double.infinity,
                    height: 380.h,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF5F6FA),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Center(
                      child: Image.asset(
                        'assets/images/worker/scan_code.gif',
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            padding: EdgeInsets.all(20.w),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.error_outline,
                                  color: Colors.grey,
                                  size: 48.w,
                                ),
                                SizedBox(height: 8.h),
                                Text(
                                  '图片加载失败',
                                  style: TextStyle(
                                    color: Colors.grey,
                                    fontSize: 14.sp,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  SizedBox(height: 20.h),
                  Container(
                    margin: EdgeInsets.only(top: 30.h),
                    child: Center(
                      child: GestureDetector(
                        onTap: () => YPRoute.closePage(),
                        child: Container(
                          width: 279.w,
                          height: 40.h,
                          decoration: BoxDecoration(
                            color: const Color(0xFF5290FD),
                            borderRadius: BorderRadius.circular(20.r),
                          ),
                          child: Center(
                            child: Text(
                              '返回扫码加入项目',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16.sp,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 30.h),
                ],
                ),
              ),
            ),
          ),
        ],
    );
  }
}
