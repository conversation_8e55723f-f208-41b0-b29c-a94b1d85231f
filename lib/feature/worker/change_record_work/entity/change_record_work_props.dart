import 'package:gdjg_pure_flutter/model/RecordType.dart';

class ChangeRecordWorkProps {
  // 记工本id
  double? noteId;

  // 记工本名称
  String? noteName;

  DateTime? date;

  ///记工类型
  RwaRecordType? businessType;

  ChangeRecordWorkProps({
    this.noteId,
    this.noteName,
    this.date,
    this.businessType,
  });

  getTitle() {
    switch (businessType) {
      case RwaRecordType.workDays:
        return '点工';
      case RwaRecordType.packageWork:
        return '包工';
      case RwaRecordType.dailyWages:
        return '短工';
      case RwaRecordType.workLoad:
        return '工量';
      case RwaRecordType.expense:
        return '其他费用';
      case RwaRecordType.debt:
        return '借支';
      case RwaRecordType.wageLast:
        return '结算';
      default:
        return '记工';
    }
  }
}
