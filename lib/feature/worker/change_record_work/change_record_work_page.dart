import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/add_record_work_param_model.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/change_record_work/entity/change_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/change_record_work/vm/change_record_work_vm.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/daily_wages_view/daily_wages_controller.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/daily_wages_view/daily_wages_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/image_selector_and_remark_view/image_selector_and_remark_controller.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/select_record_date_view.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/expense_view/expense_controller.dart';
import 'package:gdjg_pure_flutter/widget/expense_view/expense_view.dart';
import 'package:gdjg_pure_flutter/widget/point_work/point_work_controller.dart';
import 'package:gdjg_pure_flutter/widget/point_work/point_work_view.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_controller.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_view.dart';
import 'package:get/get.dart';

class ChangeRecordWorkPage extends BaseFulPage {
  static openPage(ChangeRecordWorkProps props) {
    YPRoute.openPage(RouteNameCollection.changePersonalRecordWork,
        params: props);
  }

  const ChangeRecordWorkPage({super.key}) : super(appBar: null);

  @override
  State createState() => _ChangeRecordWorkPage();
}

class _ChangeRecordWorkPage extends BaseFulPageState
    with SingleTickerProviderStateMixin {
  final vm = ChangeRecordWorkVm();

  late TabController _tabController;

  @override
  void initState() {
    super.initState();
  }

  @override
  void onPageCreate() {
    super.onPageCreate();
    _tabController = TabController(length: 5, vsync: this);
    _tabController.addListener(() {
      // setState(() {});
      // ToastUtil.showToast('当前选中的 Tab 索引：${_tabController.index}');
    });
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    var props = routeParams as ChangeRecordWorkProps?;
    vm.initPage(props, (index) {
      _tabController.index = index;
    });
    vm.initProject(props?.noteId, props?.noteName);
    vm.initDate(props?.date);
  }

  @override
  void dispose() {
    _tabController.removeListener(() {});
    _tabController.dispose();
    super.dispose();
  }

  /// 项目选择
  Widget _buildProjectView() {
    return GestureDetector(
      onTap: () {
        yprint('******************');
        vm.changeProject();
      },
      child: Container(
        width: double.infinity,
        height: 45,
        color: Colors.transparent,
        child: Center(
          child: Row(
            children: [
              Text('项目：',
                  style: TextStyle(fontSize: 17, color: Color(0xFF323233))),
              Obx(() => Text(vm.us.project?.name ?? '请选择项目',
                  style: TextStyle(
                      fontSize: 17,
                      color: Color(0xFF323233),
                      fontWeight: FontWeight.w500))),
              Spacer(),
              SizedBox(width: 4),
              Image.asset(Assets.commonIconArrowRightGrey,
                  width: 18, height: 18)
            ],
          ),
        ),
      ),
    );
  }

  /// 项目选择
  Widget _buildDateView() {
    return GestureDetector(
      onTap: () {
        showSelectRecordDate();
      },
      child: Container(
        width: double.infinity,
        color: Colors.transparent,
        height: 45,
        child: Center(
          child: Row(
            children: [
              Text('日期：',
                  style: TextStyle(fontSize: 17, color: Color(0xFF323233))),
              Obx(() => Text(vm.us.getSelectDateString(),
                  style: TextStyle(
                      fontSize: 17,
                      color: Color(0xFF323233),
                      fontWeight: FontWeight.w500))),
              Spacer(),
              Image.asset(Assets.commonIconArrowRightGrey,
                  width: 18, height: 18)
            ],
          ),
        ),
      ),
    );
  }

  /// 选择记工日期
  void showSelectRecordDate() {
    // var dates = <DateTime>[];
    // dates.add(DateTime.now());

    YPRoute.openDialog(
      builder: (context) => SelectRecordDateView(
        note_id: vm.us.project?.id.toString() ?? '',
        isRecordWorkPoints: true,
        dateList: vm.us.selectDates,
        isChangeChoice: false,
        isMultiple: false,
        onSelect: (dateList) {
          vm.setSelectList(dateList);
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  final _pointWorkController = PointWorkController();
  final _packageWorkController = PointWorkController();
  final _dailyWagesController = DailyWagesController();
  final _workLoadController = WorkLoadController();
  final _expenseController = ExpenseController();
  final _pointWorkPhotoRemarkController = ImageSelectorAndRemarkController();
  final _packagePhotoRemarkController = ImageSelectorAndRemarkController();

  /// 点工组件
  Widget _buildTabPageView(RwaRecordType businessType, Widget childView) {
    return Container(
      width: double.infinity,
      child: Column(
        children: [
          Flexible(
              flex: 1,
              child: Container(
                height: double.infinity,
                child: SingleChildScrollView(
                  child: childView,
                ),
              )),
        ],
      ),
    );
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(
        title: vm.us.title.value,
        onBackTap: () => YPRoute.closePage(),
      ),
      resizeToAvoidBottomInset: false,
      backgroundColor: Color(0xFFFFFFFF),
      body: Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              Divider(
                thickness: 8,
                color: Color(0xFFF0F0F0),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 16),
                width: double.infinity,
                child: Column(children: [
                  _buildDateView(),
                  Divider(
                    thickness: 1,
                    color: Color(0xFFF5F5F5),
                  ),
                  _buildProjectView(),
                ]),
              ),
              Divider(
                thickness: 8,
                color: Color(0xFFF0F0F0),
              ),
              Flexible(
                flex: 1,
                child: TabBarView(
                    controller: _tabController,
                    physics: NeverScrollableScrollPhysics(),
                    children: [
                      // 点工
                      _buildTabPageView(
                          RwaRecordType.workDays,
                          Obx(() => PointWorkView(
                                isShowRemind: false,
                                controller: _pointWorkController,
                                imageSelectorAndRemarkController:
                                    _pointWorkPhotoRemarkController,
                                noteId: vm.us.project?.id.toString() ?? '',
                                workNoteName: vm.us.project?.name ?? '',
                                businessType: RwaRecordType.workDays,
                                recordNoteType: RecordNoteType.personal,
                                isModify: false,
                                worker:
                                    WorkerModel(workerId: 3514, workerName: ''),
                                feeStandardModel:
                                    vm.us.projectInfo?.feeStandard,
                              ))),
                      // 包工
                      _buildTabPageView(
                          RwaRecordType.packageWork,
                          Obx(() => PointWorkView(
                                isShowRemind: false,
                                controller: _packageWorkController,
                                imageSelectorAndRemarkController:
                                    _packagePhotoRemarkController,
                                noteId: vm.us.project?.id.toString() ?? '',
                                workNoteName: vm.us.project?.name ?? '',
                                businessType: RwaRecordType.packageWork,
                                recordNoteType: RecordNoteType.personal,
                                isModify: false,
                                worker:
                                    WorkerModel(workerId: 3514, workerName: ''),
                                feeStandardModel:
                                    vm.us.projectInfo?.contractorFeeStandard,
                              ))),
                      // 短工
                      _buildTabPageView(RwaRecordType.dailyWages,
                          DailyWagesView(controller: _dailyWagesController)),
                      // 工量
                      _buildTabPageView(
                          RwaRecordType.workLoad,
                          Obx(() => WorkLoadView(
                                controller: _workLoadController,
                                noteId: vm.us.project?.id.toString() ?? '',
                              ))),
                      // 其他费用
                      _buildTabPageView(
                          RwaRecordType.expense,
                          Obx(() => ExpenseView(
                                controller: _expenseController,
                                noteId: vm.us.project?.id.toString() ?? '',
                              ))),
                    ]),
              ),
            ],
          )),
      bottomNavigationBar: _buildBottomView(),
    );
  }

  ///底部取消按钮和确认按钮
  Widget _buildBottomView() {
    return Container(
      padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Color(0xFFF5F5F5), width: 0.5.w),
        ),
      ),
      child: Row(
        children: [
          // 取消按钮
          GestureDetector(
            onTap: vm.onConfirmTap,
            behavior: HitTestBehavior.opaque,
            child: Container(
              height: 44,
              width: 100.w,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.r),
                border: Border.all(color: Color(0xFFF54A45), width: 1.w),
              ),
              child: Text('删除',
                  style: TextStyle(fontSize: 16.sp, color: Color(0xFFF54A45))),
            ),
          ),
          SizedBox(width: 10.w), // 按钮间距
          Expanded(
            child: GestureDetector(
              // onTap: vm.onSubmitTap,
              behavior: HitTestBehavior.opaque,
              child: Container(
                height: 44,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  border:
                      Border.all(color: ColorsUtil.primaryColor, width: 1.w),
                  color: ColorsUtil.primaryColor,
                ),
                child: Text('保存修改',
                    style: TextStyle(fontSize: 16.sp, color: Colors.white)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
