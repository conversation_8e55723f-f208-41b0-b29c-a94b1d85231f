import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/expenses_add_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/expenses_del_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/expenses_net_model_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/worker_account_record_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/expense_choose/dialog/add_expense_dialog.dart';
import 'package:gdjg_pure_flutter/feature/worker/expense_choose/dialog/del_expense_dialog.dart';
import 'package:gdjg_pure_flutter/feature/worker/expense_choose/us/expense_choose_us.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

class ExpenseChooseVM {
  var uiState = ExpenseChooseUs().obs;

  final recordRep = WorkerAccountRecordRepo();

  initExpenseList({String? bkId}) async {
    recordRep.fetchOtherExpenseOptions().then((result) {
      if (result.isOK()) {
        var data = result.getSucData();
        if (data != null) {
          uiState.value.setExpenseList(data.list);
          initExpenseId(double.tryParse(bkId ?? '')?.toInt() ?? 0);
        }
      }
    });
  }

  initExpenseId(int? expenseId) async {
    final index = uiState.value.expenseList
        .indexWhere((item) => expenseId == item.bkOtherExpensesId);
    if (index != -1) {
      uiState.value.setSelectedIndex(index);
    }
  }

  setChecked(int index) {
    var item = ExpensesNetModelABizModel();
    uiState.value.setSelectedIndex(index);
    var expense = uiState.value.expenseList[index];
    item = expense;
    YPRoute.closePage(item);
  }

  Future<bool> addExpense(String name) async {
    var param = ExpensesAddAParamModel();
    param.name = name;
    bool addSuccess = false;
    await recordRep.addExpenseOption(param).then((result) {
      if (result.isOK()) {
        var data = result.getSucData();
        if (data != null) {
          var item = ExpensesNetModelABizModel(
              bkOtherExpensesId: double.parse(data.bkOtherExpensesId),
              name: data.name,
              isDefault: 0,
              memberId: data.memberId);
          uiState.value.addExpense(item);
          addSuccess = true;
        }
      }
    });
    return addSuccess;
  }

  Future<bool> delExpense(double expenseId) async {
    var param = ExpensesDelAParamModel();
    param.id = expenseId;
    bool delSuccess = false;
    await recordRep.deleteExpenseOption(param).then((result) {
      if (result.isOK()) {
        var data = result.getSucData();
        if (data != null) {
          if (data.scalar) {
            uiState.value.delExpense(expenseId);
            delSuccess = true;
          }
        }
      }
    });
    return delSuccess;
  }

  showAddExpenseDialog() {
    AddExpenseDialog.show(onConfirm: (value) {
      if (value.isEmpty || value == '') {
        ToastUtil.showToast('请输入费用名称，最多20字');
        return;
      }
      addExpense(value).then((result) {
        if (result) {
          YPRoute.closeDialog();
          YPRoute.closePage(value);
        }
      });
    });
  }

  showDelExpenseDialog(String name, double expenseId) {
    DelExpenseDialog.show(
        onConfirm: () {
          delExpense(expenseId).then((result) {
            if (result) {
              YPRoute.closeDialog();
            }
          });
        },
        expenseName: name);
  }
}
