import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/expenses_lastbiz_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/expense_choose/vm/expense_choose_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

import 'entity/expense_choose_props.dart';

class ExpenseChoosePage extends BaseFulPage {
  const ExpenseChoosePage({Key? key})
      : super(appBar: const YPAppBar(title: "费用名称"));

  @override
  State createState() => _ExpenseChoosePageState();
}

class _ExpenseChoosePageState extends BaseFulPageState {
  final vm = ExpenseChooseVM();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    // TODO: implement onPageRoute
    super.onPageRoute(routeParams, fromLaunchTask);
    var props = routeParams as ExpensesLastBizModel?;
    print('--------11111111--------- ${props?.bkOtherExpensesId}');
    if (props?.bkOtherExpensesId != null) {
      vm.initExpenseList(bkId: props?.bkOtherExpensesId);
    } else {
      vm.initExpenseList();
    }
  }

  @override
  Widget yBuild(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            _TopHintBar(
              text: '点击选择费用名称，支持新增自定义费用名称',
            ),
            const SizedBox(height: 8),
            Obx(() => Expanded(
                  child: SingleChildScrollView(
                    child: vm.uiState.value.expenseList.isNotEmpty == true
                        ? ListView.builder(
                            physics: NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return _buildSelectionItem(context, index);
                            },
                            itemCount: vm.uiState.value.expenseList.length,
                          )
                        : Container(),
                  ),
                )),
            SizedBox(height: 74)
          ],
        ),
        Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: SafeArea(
              child: Container(
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(color: Color(0xFFF5F5F5), width: 1),
                  ),
                  color: Colors.white,
                ),
                child: GestureDetector(
                  onTap: () {
                    vm.showAddExpenseDialog();
                  },
                  child: Container(
                    margin: EdgeInsets.only(
                        left: 16, right: 16, top: 8, bottom: 24),
                    height: 44.h,
                    decoration: BoxDecoration(
                        color: ColorsUtil.primaryColor,
                        borderRadius: BorderRadius.all(Radius.circular(4.w))),
                    child: Center(
                      child: Text('新增自定义费用名称',
                          style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white)),
                    ),
                  ),
                ),
              ),
            ))
      ],
    );
  }

  /// 构建选择项
  Widget _buildSelectionItem(BuildContext context, int index) {
    final itemData = vm.uiState.value.expenseList[index];
    return InkWell(
      onTap: () => vm.setChecked(index),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1),
          ),
        ),
        child: Row(
          children: [
            if (index == vm.uiState.value.selectedIndex)
              IconFont(
                IconNames.saasCheck,
                size: 17.5,
                color: "#1983FF",
              )
            else
              IconFont(
                IconNames.saasRadio,
                size: 17.5,
                color: "#323233",
              ),
            SizedBox(width: 8),
            Text(
              itemData.name ?? '',
              style: TextStyle(fontSize: 17.sp, color: Color(0xFF323232)),
            ),
            Spacer(),
            if (itemData.isDefault == 0)
              GestureDetector(
                onTap: () {
                  vm.showDelExpenseDialog(
                      itemData.name, itemData.bkOtherExpensesId);
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                      border: Border.all(width: 1, color: Color(0xFF5290FD)),
                      borderRadius: BorderRadius.circular(4)),
                  child: Text(
                    '删除',
                    style: TextStyle(
                      fontSize: 17.sp,
                      color: Color(0xFF5290FD),
                    ),
                  ),
                ),
              )
          ],
        ),
      ),
    );
  }
}

class _TopHintBar extends StatelessWidget {
  final String text;

  const _TopHintBar({
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      color: const Color(0x1A5290FD),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Text(
        text,
        style: TextStyle(
          color: Color(0xFF2F6BFF),
          fontSize: 13.sp,
        ),
      ),
    );
  }
}
