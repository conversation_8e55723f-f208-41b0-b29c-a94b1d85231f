import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class AddExpenseDialog {
  static show({
    required Function(String) onConfirm,
  }) {
    YPRoute.openDialog(
      builder: (context) => AddExpenseView(
        onConfirm: onConfirm,
      ),
      clickMaskDismiss: true,
    );
  }
}

class AddExpenseView extends StatefulWidget {
  final Function(String) onConfirm;

  const AddExpenseView({Key? key, required this.onConfirm}) : super(key: key);

  @override
  _AddExpenseViewState createState() => _AddExpenseViewState();
}

class _AddExpenseViewState extends State<AddExpenseView> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();

    // 延迟获取焦点
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_focusNode.hasFocus) {
        _focusNode.unfocus();
      }
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  String get value => _controller.text;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24) ,
      padding: const EdgeInsets.only(top: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '新增自定义费用名称',
            style: TextStyle(
              fontSize: 16,
              color: ColorsUtil.black85,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              maxLength: 20,
              decoration: InputDecoration(
                hintText: '请输入费用名称，最多20字',
                hintStyle: TextStyle(
                  fontSize: 14,
                  color: ColorsUtil.black65,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(4),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: const Color(0xFFF0F0F0),
                isCollapsed: true,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                counterText: '',
              ),
              style: TextStyle(fontSize: 14, color: ColorsUtil.black65),
            ),
          ),
          const SizedBox(height: 24),
          Divider(
            height: 1,
            color: ColorsUtil.inputBgColor,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Expanded(
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () => YPRoute.closeDialog(),
                  child: Container(
                    height: 44,
                    alignment: Alignment.center,
                    child: Text(
                      '取消',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
              ),
              Container(
                width: 1,
                height: 44,
                color: ColorsUtil.inputBgColor,
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () => widget.onConfirm(value),
                  child: Container(
                      height: 44,
                      alignment: Alignment.center,
                      child: Text(
                        '确定',
                        style: TextStyle(
                            color: ColorsUtil.primaryColor,
                            fontWeight: FontWeight.w500),
                      )),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
