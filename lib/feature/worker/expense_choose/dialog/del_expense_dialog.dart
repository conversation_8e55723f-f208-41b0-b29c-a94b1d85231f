import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class DelExpenseDialog {
  static show({
    required Function() onConfirm,
    required String expenseName,
  }) {
    YPRoute.openDialog(
        builder: (context) => DelExpenseView(
              onConfirm: onConfirm,
              expenseName: expenseName,
            ),
        alignment: Alignment.center,
        clickMaskDismiss: false,
        maskColor: Colors.black.withValues(alpha: 0.5));
  }
}

class DelExpenseView extends StatelessWidget {
  final Function() onConfirm;
  final String expenseName;

  const DelExpenseView({
    Key? key,
    required this.onConfirm,
    required this.expenseName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: IntrinsicHeight(
        child: Column(
          children: [
            const SizedBox(height: 24),
            Text(
              '温馨提示',
              style: TextStyle(
                fontSize: 18.sp,
                color: ColorsUtil.black85,
                fontWeight: FontWeight.w500,
                height: 1.39,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '确定删除$expenseName？',
              style: TextStyle(
                  fontSize: 16.sp, height: 1.375, color: Color(0xFF8A8A99)),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () => YPRoute.closeDialog(),
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: Text(
                        '取消',
                        style: TextStyle(
                            color: ColorsUtil.black85,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      onConfirm();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: const Text(
                        '确定',
                        style: TextStyle(
                            color: Color(0xFF5290FD),
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
    return AlertDialog(
      title: const Text(
        '温馨提示',
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Text(
        '确定删除$expenseName？',
        style: const TextStyle(
          fontSize: 16,
          height: 1.5,
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => YPRoute.closeDialog(),
          child: const Text(
            '取消',
            style: TextStyle(color: Colors.grey),
          ),
        ),
        TextButton(
          onPressed: () {
            onConfirm();
          },
          child: const Text(
            '确定',
            style: TextStyle(color: Colors.red),
          ),
        ),
      ],
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      backgroundColor: Colors.white,
    );
  }
}
