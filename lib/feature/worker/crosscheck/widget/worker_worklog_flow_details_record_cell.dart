import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/crosscheck/ui_model/worker_worklog_flow_details_item_ui_model.dart';
import 'package:gdjg_pure_flutter/utils/format/num_format.dart';

import '../../../../iconfont/iconfont.dart';
import '../../../../utils/system_util/font_util.dart';
import '../../../../utils/ui_util/colors_util.dart';
import '../ui_model/worker_worklog_flow_details_type_enum.dart';

class WorkerWorklogFlowDetailsRecordCell extends StatelessWidget {
  final WorkerWorklogFlowDetailsItemUiModel model;

  const WorkerWorklogFlowDetailsRecordCell({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    final Color baseColor;
    final Widget left;
    final Widget right;
    switch (model.type) {
      case WorkerWorklogFlowDetailsType.gig:
        baseColor = ColorsUtil.primaryColor;
        left = _buildLeft('点工', model.projectName);
        right = _buildRight(
          model.amount,
          baseColor,
          secondary: _formatWorkTime(
            worktimeDay: model.worktimeDay,
            worktimeHour: model.worktimeHour,
            morningWorkDay: model.morningWorkDay,
            morningWorkHour: model.morningWorkHour,
            afternoonWorkDay: model.afternoonWorkDay,
            afternoonWorkHour: model.afternoonWorkHour,
            overtimeDay: model.overtimeDay,
            overtimeHour: model.overtimeHour,
          ),
        );
      case WorkerWorklogFlowDetailsType.contract:
        baseColor = ColorsUtil.primaryColor;
        left = _buildLeft('包工', model.projectName);
        right = _buildRight(
          model.amount,
          baseColor,
          secondary: _formatWorkTime(
            worktimeDay: model.worktimeDay,
            worktimeHour: model.worktimeHour,
            morningWorkDay: model.morningWorkDay,
            morningWorkHour: model.morningWorkHour,
            afternoonWorkDay: model.afternoonWorkDay,
            afternoonWorkHour: model.afternoonWorkHour,
            overtimeDay: model.overtimeDay,
            overtimeHour: model.overtimeHour,
          ),
        );
      case WorkerWorklogFlowDetailsType.outputBased:
        baseColor = ColorsUtil.primaryColor;
        left = _buildLeft(
          '工量 ${model.subname ?? ''}',
          model.projectName,
        );
        right = _buildRight(
          model.amount,
          baseColor,
          secondary: model.total?.isNotEmpty ?? false
              ? '总计:${model.total}${model.unit ?? ''}'
              : null,
        );
      case WorkerWorklogFlowDetailsType.otherCost:
        baseColor = ColorsUtil.primaryColor;
        left = _buildLeft(
          '其它费用 ${model.subname ?? ''}',
          model.projectName,
        );
        right = _buildRight(model.amount, baseColor);
      case WorkerWorklogFlowDetailsType.temporary:
        baseColor = ColorsUtil.primaryColor;
        left = _buildLeft('短工', model.projectName);
        right = _buildRight(model.amount, baseColor);
      case WorkerWorklogFlowDetailsType.advance:
        baseColor = const Color(0xFFFFA011);
        left = _buildLeft('借支', model.projectName);
        right = _buildRight(model.amount ?? '', baseColor);
      case WorkerWorklogFlowDetailsType.unsettled:
        baseColor = const Color(0xFFFFA011);
        left = _buildLeft('未结', model.projectName);
        right = _buildRight(model.amount ?? '', baseColor);
    }

    // 记录主要信息
    final Widget info = Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        left,
        Spacer(),
        right,
        SizedBox(width: 2.w),
        IconFont(
          IconNames.saasArrowRight,
          size: 16.w,
          color: '#8A8A99',
        ),
        SizedBox(width: 4.w),
      ],
    );

    // 内容, 包含主要信息和可能存在的memo信息
    final Widget content;

    final String? memo = model.memo;
    if (memo != null && memo.trim().isNotEmpty) {
      content = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          info,
          Text(
            memo,
            style: TextStyle(
              color: ColorsUtil.black32,
              fontSize: 13.sp,
            ),
          ),
        ],
      );
    } else {
      content = info;
    }

    return DecoratedBox(
      decoration: BoxDecoration(color: Colors.white),
      child: Padding(
        padding:
            EdgeInsets.only(left: 16.w, top: 8.h, right: 16.w, bottom: 10.h),
        child: content,
      ),
    );
  }

  Widget _buildLeft(final String title, final String? projectName) => Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              color: ColorsUtil.black85,
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8.h),
          DecoratedBox(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.w),
              color: const Color(0x266EA3FF),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
              child: Text(
                projectName ?? '-',
                style: TextStyle(
                  color: const Color(0xFF606066),
                  fontSize: 13.sp,
                ),
              ),
            ),
          ),
        ],
      );

  Widget _buildRight(
    final String? primary,
    final Color color, {
    final String? secondary,
  }) {
    final Widget primaryWidget = Text(primary ?? '-',
        style: TextStyle(
          color: color,
          fontSize: 22.sp,
          fontFamily: FontUtil.fontCondMedium,
        ));
    if (secondary == null) {
      return primaryWidget;
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          primaryWidget,
          Text(
            secondary,
            style: TextStyle(
              color: ColorsUtil.black85,
              fontSize: 14.sp,
            ),
          ),
        ],
      );
    }
  }

  String _formatWorkTime({
    final double? worktimeDay,
    final double? worktimeHour,
    final double? morningWorkDay,
    final double? morningWorkHour,
    final double? afternoonWorkDay,
    final double? afternoonWorkHour,
    final double? overtimeDay,
    final double? overtimeHour,
  }) {
    final double wd = worktimeDay ?? 0;
    final double wh = worktimeHour ?? 0;
    final double md = morningWorkDay ?? 0;
    final double mh = morningWorkHour ?? 0;
    final double ad = afternoonWorkDay ?? 0;
    final double ah = afternoonWorkHour ?? 0;
    final double od = overtimeDay ?? 0;
    final double oh = overtimeHour ?? 0;

    final String worktime;
    if (wd != 0 || wh != 0) {
      if (wd != 0) {
        worktime = '上班: ${NumFormat.trim(wd)}个工';
      } else {
        worktime = '上班: ${NumFormat.trim(wh)}小时';
      }
    } else if (md != 0 || mh != 0 || ad != 0 || ah != 0) {
      final String morning;
      if (md != 0) {
        morning = '上午: ${NumFormat.trim(md)}个工';
      } else if (mh != 0) {
        morning = '上午: ${NumFormat.trim(mh)}小时';
      } else {
        morning = '上午: 休息';
      }
      final String afternoon;
      if (ad != 0) {
        afternoon = '下午: ${NumFormat.trim(ad)}个工';
      } else if (ah != 0) {
        afternoon = '下午: ${NumFormat.trim(ah)}小时';
      } else {
        afternoon = '下午: 休息';
      }
      worktime = '$morning\n$afternoon';
    } else {
      worktime = '上班: 休息';
    }
    final String? overtime;
    if (od != 0) {
      overtime = '加班: ${NumFormat.trim(od)}个工';
    } else if (oh != 0) {
      overtime = '加班: ${NumFormat.trim(oh)}小时';
    } else {
      overtime = null;
    }

    if (overtime == null) {
      return worktime;
    } else {
      return '$worktime\n$overtime';
    }
  }
}
