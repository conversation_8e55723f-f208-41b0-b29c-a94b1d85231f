import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/crosscheck/ui_model/worker_worklog_flow_details_summary_ui_model.dart';
import 'package:gdjg_pure_flutter/utils/format/num_format.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

import '../../../../utils/system_util/font_util.dart';
import '../ui_model/worker_worklog_flow_details_type_enum.dart';

/// 流水明细 - 生成图片弹窗的 记录的cell
/// 显示模型[WorkerWorklogFlowDetailsSummaryUiModel]
/// [hindAmount] 是否隐藏金额
class WorkerWorklogFlowPreviewSummaryCell extends StatelessWidget {
  final WorkerWorklogFlowDetailsSummaryUiModel model;
  final bool hideAmount;

  const WorkerWorklogFlowPreviewSummaryCell(
    this.model, {
    super.key,
    this.hideAmount = true,
  });

  @override
  Widget build(BuildContext context) {
    final Color background;
    final Color foreground;
    final Widget left;
    final Widget body;
    final Widget? right;
    switch (model.type) {
      case WorkerWorklogFlowDetailsType.gig:
        background = const Color(0xFFF2F9FF);
        foreground = ColorsUtil.primaryColor;
        left = _buildLeft('点工');
        final String? worktime = _formatWorktime(
          model.worktimeDay,
          model.worktimeHour,
          model.overtimeDay,
          model.overtimeHour,
        );
        body = worktime == null ? Spacer() : _buildBody(worktime);
        right =
            hideAmount ? null : _buildRight(foreground, primary: model.amount);
      case WorkerWorklogFlowDetailsType.contract:
        background = const Color(0xFFF2F9FF);
        foreground = ColorsUtil.primaryColor;
        left = _buildLeft('包工');
        final String? worktime = _formatWorktime(
          model.worktimeDay,
          model.worktimeHour,
          model.overtimeDay,
          model.overtimeHour,
        );
        body = worktime == null ? Spacer() : _buildBody(worktime);
        right =
            hideAmount ? null : _buildRight(foreground, primary: model.amount);
      case WorkerWorklogFlowDetailsType.outputBased:
        background = const Color(0xFFF2F9FF);
        foreground = ColorsUtil.primaryColor;
        left = _buildLeft(
          '工量 ${model.subname ?? ''}',
          subtitle: model.recordCount == null ? null : '${model.recordCount}笔',
        );
        body = Spacer();
        right = _buildRight(
          foreground,
          primary: hideAmount ? null : model.amount,
          secondary: model.total?.isNotEmpty ?? false
              ? '总计:${model.total}${model.unit ?? ''}'
              : null,
        );
      case WorkerWorklogFlowDetailsType.otherCost:
        background = const Color(0xFFF2F9FF);
        foreground = ColorsUtil.primaryColor;
        final String? recordCount = NumFormat.ignoreZero(model.recordCount);
        left = _buildLeft(
          '其它费用 ${model.subname ?? ''}',
          subtitle: recordCount == null ? null : '$recordCount笔',
        );
        body = Spacer();
        right =
            hideAmount ? null : _buildRight(foreground, primary: model.amount);
      case WorkerWorklogFlowDetailsType.temporary:
        background = const Color(0xFFF2F9FF);
        foreground = ColorsUtil.primaryColor;
        left = _buildLeft('短工');
        final String? recordCount = NumFormat.ignoreZero(model.recordCount);
        body = recordCount == null ? Spacer() : _buildBody('$recordCount笔');
        right =
            hideAmount ? null : _buildRight(foreground, primary: model.amount);
      case WorkerWorklogFlowDetailsType.advance:
        background = const Color(0xFFFFF4E5);
        foreground = const Color(0xFFFF9800);
        left = _buildLeft('借支');
        final String? recordCount = NumFormat.ignoreZero(model.recordCount);
        body = recordCount == null ? Spacer() : _buildBody('$recordCount笔');
        right =
            hideAmount ? null : _buildRight(foreground, primary: model.total);
      default:
        // 针对传入类型是 WorkerWorklogFlowDetailsType.unsettled 的项目不显示
        // 传入 WorkerWorklogFlowDetailsType.unsettled 应该数个错误
        return Container();
    }

    return Container(
      decoration: BoxDecoration(
        color: background,
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          left,
          SizedBox(width: 8.w),
          body,
          if (right != null) right,
        ],
      ),
    );
  }

  /// 构建左侧的信息
  /// [title] 显示为标题; ex: 记录类型 和 分项/其它费用明细
  /// [subtitle] 显示在标题下的副标题; ex: 按量记工/其它费用 的笔数
  Widget _buildLeft(String title, {String? subtitle}) {
    final Widget titleWidget = Text(
      title,
      style: TextStyle(color: const Color(0xFF323233), fontSize: 16.sp),
    );
    if (subtitle == null) {
      return titleWidget;
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          titleWidget,
          Text(
            subtitle,
            style: TextStyle(color: const Color(0xFF9D9DB3), fontSize: 15.sp),
          ),
        ],
      );
    }
  }

  /// 构建body信息; 这是一个 Expanded; 将会占用出了 left 和 right 的剩余空间
  /// [content] 内容; 显示 点工/包工的工作/加班时长, 短工的记录数量
  Widget _buildBody(String content) => Expanded(
        child: Text(
          content,
          style: TextStyle(
            color: const Color(0xFF323233),
            fontSize: 15.sp,
          ),
        ),
      );

  /// 构建右侧信息
  /// [color] 主要文本的颜色
  /// [primary] 主要文本; 在显示金额时, 用来显示金额; 隐藏金额时不应该传递这个值
  /// [secondary] 次要文本; 按量记工的总量
  /// 如果主文本和副文本都是null; 这个方法将返回 null
  Widget? _buildRight(Color color, {String? primary, String? secondary}) {
    final Widget? primaryWidget = primary == null
        ? null
        : Text(
            primary,
            style: TextStyle(
              color: color,
              fontSize: 22.sp,
              fontFamily: FontUtil.fontCondMedium,
            ),
          );
    final Widget? secondaryWidget = secondary == null
        ? null
        : Text(
            secondary,
            style: TextStyle(
              color: const Color(0xFF323233),
              fontSize: 15.sp,
            ),
          );
    if (primaryWidget != null && secondaryWidget != null) {
      return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [primaryWidget, secondaryWidget]);
    } else if (primaryWidget != null) {
      return primaryWidget;
    } else if (secondaryWidget != null) {
      return secondaryWidget;
    } else {
      return null;
    }
  }

  /// 格式化上班/加班的时常
  /// 上班 - 加班 使用换行符分割, 在一个Text中显示两行
  /// 排除 0 值; 如果没有任何一个非 0 值, 函数返回 `null`
  String? _formatWorktime(
    String? worktimeDay,
    String? worktimeHour,
    String? overtimeDay,
    String? overtimeHour,
  ) {
    final String? wd = NumFormat.ignoreZero(worktimeDay);
    final String? wh = NumFormat.ignoreZero(worktimeHour);
    final String? od = NumFormat.ignoreZero(overtimeDay);
    final String? oh = NumFormat.ignoreZero(overtimeHour);

    final String? worktime;
    if (wd != null && wh != null) {
      worktime = '上班: $wd个工$wh小时';
    } else if (wh != null) {
      worktime = '上班: $wh小时';
    } else if (wd != null) {
      worktime = '上班: $wd个工';
    } else {
      worktime = null;
    }

    final String? overtime;
    if (od != null && oh != null) {
      overtime = '加班: $od个工$oh小时';
    } else if (oh != null) {
      overtime = '加班: $oh小时';
    } else if (od != null) {
      overtime = '加班: $od个工';
    } else {
      overtime = null;
    }

    if (worktime != null && overtime != null) {
      return '$worktime\n$overtime';
    } else if (worktime != null) {
      return worktime;
    } else if (overtime != null) {
      return overtime;
    } else {
      return null;
    }
  }
}
