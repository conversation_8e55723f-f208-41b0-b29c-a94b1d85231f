import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/ad/native_ad_and_vip_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_statistics_list_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/guide/worker_guide_provider.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/entity/personal_record_workpoints_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/daily_flow_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/vm/work_calendar_vm.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/calendar_action_button.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/machine_list_item.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/quick_link/quick_link_content.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/recruit_list_item.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/common/pigeon_flutter_api.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_week.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dynamic_height_calendar.dart';
import 'package:gdjg_pure_flutter/widget/calendar/utils.dart';
import 'package:gdjg_pure_flutter/widget/select_month/select_month.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../../../../../utils/ui_util/widget_util.dart';

class WorkerCalendarPage extends StatelessWidget {
  const WorkerCalendarPage({
    super.key,
    required this.calendarKey,
  });

  final GlobalKey<DynamicPageViewInScrollViewState> calendarKey;

  @override
  Widget build(BuildContext context) {
    return DynamicPageViewInScrollView(key: calendarKey);
  }
}

class DynamicPageViewInScrollView extends StatefulWidget {
  const DynamicPageViewInScrollView({super.key});

  @override
  State<DynamicPageViewInScrollView> createState() =>
      DynamicPageViewInScrollViewState();
}

class DynamicPageViewInScrollViewState
    extends State<DynamicPageViewInScrollView> {
  final WorkCalendarVm vm = WorkCalendarVm();

  final GlobalKey<DynamicHeightCalendarState> _calendarKey =
      GlobalKey<DynamicHeightCalendarState>();

  final GlobalKey _weekKey = GlobalKey();
  final GlobalKey _calendarRenderBoxKey = GlobalKey();

  TutorialCoachMark? _tutorialCoachMarkStep1;

  @override
  void initState() {
    super.initState();
  }

  Future<void> checkGuideStep1() async {
    if (await vm.checkGuideStep1()) {
      _showTutorialStep1();
    }
  }

  void finishGuideStep1() {
    _tutorialCoachMarkStep1?.finish();
  }

  void _showTutorialStep1() {
    finishGuideStep1();
    final Rect? rect =
        WidgetUtils.calculateBounding([_weekKey, _calendarRenderBoxKey]);
    if (rect == null) {
      return;
    }
    final Rect? todayRect = _calendarKey.currentState?.findTodayRect();
    _tutorialCoachMarkStep1 = WorkerGuideProvider.showStep1(
      context,
      rect,
      todayRect,
      () {
        finishGuideStep1();
        YPRoute.openPage(RouteNameCollection.personalRecordWork);
      },
    );
    vm.completeGuide();
  }

  @override
  void dispose() {
    super.dispose();
    vm.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(() {
        return SmartRefresher(
          controller: vm.refreshController,
          onRefresh: vm.onRefresh,
          onLoading: vm.onLoading,
          enablePullUp: vm.us.showRecruitList
              ? vm.recruitRefresh.hasMore
              : vm.machineRefresh.hasMore,
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            cacheExtent: 50,
            slivers: [
              SliverToBoxAdapter(
                child: Obx(
                  () => SelectMonth(
                    value: vm.us.currentDate,
                    onValueChange: (v) {
                      setState(() {
                        vm.onCalendarDateChange(v);
                        _calendarKey.currentState?.changeMonth(v);
                      });
                    },
                  ),
                ),
              ),
              SliverToBoxAdapter(
                child: Obx(
                  () => GroupStatisticsView(
                    items: vm.us.statisticsList,
                    initialVisibleCount: 1,
                    onItemTap: (index) {
                      // 处理点击事件
                      ToastUtil.showToast("暂无工人流水");
                    },
                  ),
                ),
              ),
              // 动态高度部分
              SliverToBoxAdapter(
                child: CalendarWeek(
                  key: _weekKey,
                ),
              ),
              Obx(() {
                return DynamicHeightCalendar(
                  key: _calendarKey,
                  renderBoxKey: _calendarRenderBoxKey,
                  onValueChange: (date) {
                    vm.onCalendarDateChange(date);
                  },
                  events: vm.us.events,
                  onDayTap: (v) async {
                    // 1、判断当前是否可以记工
                    if (isAfterToday(v, DateTime.now())) {
                      ToastUtil.showToast('未到${v.month}月${v.day}日，不可记工');
                      return;
                    }
                    // 如果当前有数据，则进单日流水
                    final currentEvent = vm.us.events[v] ?? [];
                    if (currentEvent.isNotEmpty) {
                      DailyFlowPage.openPage(v);
                      return;
                    }
                    // 如果当日没数据，则跳转记工
                    await YPRoute.openPage(
                        RouteNameCollection.personalRecordWorkPoints,
                        params: PersonalRecordWorkpointsProps(
                          date: v,
                        ));
                    // 刷新当前日历事件数据
                    vm.onRefresh();
                  },
                );
              }),
              // 记工、记借支按钮
              SliverToBoxAdapter(
                child: CalendarActionButton(
                  reloadPage: vm.onRefresh,
                ),
              ),
              SliverToBoxAdapter(
                child: SizedBox(
                  height: 8.h,
                ),
              ),
              SliverPersistentHeader(
                  pinned: true, // 吸顶效果
                  delegate: _StickyHeaderDelegate(
                      maxHeight: 128.h,
                      minHeight: 128.h,
                      child: Obx(() {
                        return QuickLinkContent(
                          quickLinkList: vm.us.quickLinkList,
                          curQuickLink: vm.us.curQuickLink,
                          onSelected: (i) {
                            vm.onQuickLinkSelected(i);
                          },
                          occupation: vm.us.recruitListUS.currentOcc,
                          onOccupationSelected: (i) =>
                              vm.onOccupationSelected(i),
                        );
                      }))),

              // 鱼泡招工列表
              Obx(() {
                return vm.us.showRecruitList
                    ? Obx(() {
                        return SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final RecruitItemUiState itemData =
                                  vm.recruitRefresh.list[index];
                              var params = <String, Object>{}..["adLocation"] =
                                  AdConst.tabCalendar;
                              return Container(
                                margin: EdgeInsets.only(bottom: 8.h),
                                child: itemData.isAd
                                    ? NativeAdAndVipView(params)
                                    : RecruitListItem(value: itemData),
                              );
                            },
                            childCount: vm.recruitRefresh.list.length,
                          ),
                        );
                      })
                    : SliverToBoxAdapter();
              }),
              // 机械列表
              Obx(() {
                return vm.us.showMachineList
                    ? Obx(() {
                        return SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) {
                              final MachineItemUiState itemData =
                                  vm.machineRefresh.list[index];
                              var params = <String, Object>{}..["adLocation"] =
                                  AdConst.tabCalendar;
                              return Container(
                                margin: EdgeInsets.only(bottom: 8.h),
                                child: itemData.isAd
                                    ? NativeAdAndVipView(params)
                                    : MachineListItem(
                                        value: vm.machineRefresh.list[index]),
                              );
                            },
                            childCount: vm.machineRefresh.list.length,
                          ),
                        );
                      })
                    : SliverToBoxAdapter();
              }),
            ],
          ),
        );
      }),
    );
  }
}

// 自定义Delegate
class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double? minHeight;
  final double? maxHeight;
  final GlobalKey _childKey = GlobalKey();

  _StickyHeaderDelegate({
    required this.child,
    this.minHeight,
    this.maxHeight,
  });

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(
      child: KeyedSubtree(
        key: _childKey,
        child: child,
      ),
    );
  }

  @override
  double get maxExtent {
    if (maxHeight != null) return maxHeight!;

    // 通过上下文获取渲染对象
    final RenderBox? renderBox =
        _childKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null && renderBox.hasSize) {
      return renderBox.size.height;
    }

    // 默认高度（当无法计算时）
    return 56.0; // 或者抛出一个错误/使用assert
  }

  @override
  double get minExtent {
    if (minHeight != null) return minHeight!;
    return maxExtent * 0.5; // 默认最小高度为最大高度的一半
  }

  @override
  bool shouldRebuild(covariant _StickyHeaderDelegate oldDelegate) {
    return child != oldDelegate.child ||
        minHeight != oldDelegate.minHeight ||
        maxHeight != oldDelegate.maxHeight;
  }
}
