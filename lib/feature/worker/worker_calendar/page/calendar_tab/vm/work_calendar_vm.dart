import 'package:gdjg_pure_flutter/data/calendar/ds/model/param/business_get_project_calendar_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/job/net_model_job_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/model/machine/net_model_machine_param_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/repo/calendar_repo.dart';
import 'package:gdjg_pure_flutter/data/quick_link_data/repo/model/quick_link_entity.dart';
import 'package:gdjg_pure_flutter/data/quick_link_data/repo/quick_link_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/vm/work_calendar_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/machine_list_item.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/quick_link/quick_link_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/recruit_list_item.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/utils/refresh_loadmore/refresh_loadmore_vvm.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/select_job_type/occupation_entity.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../../../data/worker_data/worker_guide_lds.dart';

class WorkCalendarVm {
  final us = WorkCalendarUS();
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);
  late RefreshLoadMoreVvm<RecruitItemUiState> recruitRefresh;

  late RefreshLoadMoreVvm<MachineItemUiState> machineRefresh;

  final calendarRepo = CalendarRepo();

  final quickLinkRepo = QuickLinkRepo();

  final WorkerGuideLds _lds = WorkerGuideLds();

  WorkCalendarVm() {
    // 招工列表刷新
    recruitRefresh = RefreshLoadMoreVvm(
        refreshController: this.refreshController,
        dataFetcher: (page, pageSize) {
          return queryRecruitList(page: page);
        });
    // 机器机械列表刷新
    machineRefresh = RefreshLoadMoreVvm(
        refreshController: this.refreshController,
        dataFetcher: (page, pageSize) {
          return queryMechanicalList(page: page);
        });
    sync();
  }

  sync() async {
    ToastUtil.showLoading();
    queryCalendarCount();
    // 获取金刚区数据
    queryQuickLinkList();
    // 初始列表数据
    recruitRefresh.refresh();
    machineRefresh.refresh();

    ToastUtil.hideLoading();
  }

  void onRefresh() async {
    // 判断当前是哪个tab，执行刷新时仅刷新此tab的列表数据
    if (us.curQuickLink == QuickLinkDest.show_job_list) {
      recruitRefresh.refresh();
    } else if (us.curQuickLink == QuickLinkDest.show_machine_list) {
      machineRefresh.refresh();
    }
    queryCalendarCount();
  }

  void onLoading() async {
    // 判断当前是哪个tab，执行刷新时仅刷新此tab的列表数据
    if (us.curQuickLink == QuickLinkDest.show_job_list) {
      recruitRefresh.loadMore();
    } else if (us.curQuickLink == QuickLinkDest.show_machine_list) {
      machineRefresh.loadMore();
    }
  }

  /// 获取金刚区数据
  queryQuickLinkList() async {
    final resp = await quickLinkRepo.getQuickLinkData(RecordNoteType.personal);
    if (resp.isOK()) {
      us.setQuickLinkList(resp.getSucData() ?? []);
    }
  }

  /// 获取招工列表数据
  Future<List<RecruitItemUiState>> queryRecruitList({required int page}) async {
    final isRefresh = page == 1;
    final resp = await calendarRepo.queryRecruitList(NetModelJobParamModel(
        page: page.toString(), occupation_id: us.recruitListUS.currentOcc?.id));
    if (resp.isOK()) {
      final data = resp.getSucData()?.list ?? [];
      final list = data.map((i) {
        return RecruitItemUiState(
            id: i.id.toString(),
            title: i.title,
            tag: i.showLabelV2.map((t) {
              return RecruitTag(
                name: t.name,
                type: t.type.toString(),
              );
            }).toList(),
            location: i.showAddress,
            time: i.timeStr);
      }).toList();
      if (isRefresh) {
        insertAdItemToRecruit(list);
      }
      return list;
    }
    return List.empty();
  }

  /// 获取日历事件数据
  Future<void> queryCalendarCount() async {
    var param = BusinessGetProjectCalendarCountParamModel(
      start_time: DateUtil.formatStartDate(us.currentDate),
      end_time: DateUtil.formatEndDate(us.currentDate),
    );
    final result = await calendarRepo.getProjectCalendarCount(param);
    if (result.isOK() && result.getSucData() != null) {
      us.setMonthData(result.getSucData());
    }
  }

  /// 获取鱼泡机械列表数据
  Future<List<MachineItemUiState>> queryMechanicalList(
      {required int page}) async {
    final isRefresh = page == 1;
    final resp = await calendarRepo
        .queryMechanicalList(NetModelMachineParamModel(page: page.toString()));
    if (resp.isOK()) {
      final data = resp.getSucData()?.list ?? [];
      final list = data.map((i) {
        return MachineItemUiState(
            id: i.uu.toString(),
            title: i.title,
            tag: i.tag.map((t) {
              return MachineTag(
                name: t,
                type: t,
              );
            }).toList(),
            mode: i.mode,
            location: i.area,
            time: i.time);
      }).toList();
      if (isRefresh) {
        insertAdItemToMachine(list);
      }
      return list;
    }
    return List.empty();
  }

  Future<bool> checkGuideStep1() async =>
      !await _lds.fetchGuided(WorkerGuideLds.maskStep1);

  Future<bool> completeGuide() async =>
      await _lds.updateGuided(WorkerGuideLds.maskStep1, true);

  /// 处理日历日期改变
  onCalendarDateChange(DateTime date) async {
    // 如果当前日期未改变，不做逻辑处理
    if (us.currentDate == date) {
      return;
    }
    us.setCurrentDate(date);

    try {
      ToastUtil.showLoading();
      queryCalendarCount();
    } finally {
      ToastUtil.hideLoading();
    }
  }

  onQuickLinkSelected(QuickLinkUiState item) {
    final dest = QuickLinkDest.values
        .where((i) => i.name == item.original.dest)
        .firstOrNull;
    us.setCurQuickLink(dest);
  }

  onOccupationSelected(OccupationEntity? item) {
    us.recruitListUS.setCurOccupation(item);
    // 更新一次招工列表数据
    recruitRefresh.refresh();
  }

  dispose() {
    refreshController.dispose();
  }

  /// 招工列表插入一个广告
  void insertAdItemToRecruit(List<RecruitItemUiState> list) {
    if (list.isEmpty) return;
    final tempItem = list[0];
    final adItem = RecruitItemUiState(
      id: tempItem.id,
      title: tempItem.title,
      tag: tempItem.tag,
      location: tempItem.location,
      time: tempItem.time,
      isAd: true,
    );
    list.insert(1, adItem);
  }

  /// 机械列表插入一个广告
  void insertAdItemToMachine(List<MachineItemUiState> list) {
    if (list.isEmpty) return;
    final tempItem = list[0];
    final adItem = MachineItemUiState(
      id: tempItem.id,
      title: tempItem.title,
      tag: tempItem.tag,
      mode: tempItem.mode,
      location: tempItem.location,
      time: tempItem.time,
      isAd: true,
    );
    list.insert(1, adItem);
  }
}
