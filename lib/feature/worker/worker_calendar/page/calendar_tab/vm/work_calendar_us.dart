import 'package:gdjg_pure_flutter/data/calendar/model/machine/net_model_machine_biz_model.dart';
import 'package:gdjg_pure_flutter/data/calendar/repo/model/business_get_project_calendar_count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/quick_link_data/repo/model/quick_link_entity.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistice_helper.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/vm/recruit_list_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/machine_list_item.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/page/calendar_tab/widget/quick_link/quick_link_ui_state.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/calendar_uistate_helper.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dayevent/DayEvent.dart';
import 'package:get/get.dart';

class WorkCalendarUS {
  final recruitListUS = RecruitListUS();

  final _currentDate = DateTime.now().obs;
  final _startDate = DateTime.now().copyWith(year: 2000).obs;
  final _endDate = DateTime.now().obs;
  final RxMap<DateTime, List<DayEvent>> _events =
      <DateTime, List<DayEvent>>{}.obs;

  final _statisticsList = List<StatisticsItemUIState>.empty().obs;

  /// 当前选中的
  final Rx<QuickLinkDest?> _curQuickLink = QuickLinkDest.show_job_list.obs;

  final _quickLinkList = List<QuickLinkEntity>.empty().obs;

  final _machineList = List<MachineItemUiState>.empty().obs;

  var machineListPage = 1;

  final _machineHasMore = true.obs;

  DateTime get currentDate => _currentDate.value;

  DateTime get startDate => _startDate.value;

  DateTime get endDate => _endDate.value;

  Map<DateTime, List<DayEvent>> get events => _events.value;

  List<StatisticsItemUIState> get statisticsList => _statisticsList.value;

  List<QuickLinkUiState> get quickLinkList => _quickLinkList.value.map((i) {
        return QuickLinkUiState(
            id: i.code, title: i.title, icon: i.icon, original: i);
      }).toList();

  List<MachineItemUiState> get machineList => _machineList.value;

  QuickLinkDest? get curQuickLink => _curQuickLink.value;

  bool get showRecruitList =>
      _curQuickLink.value == QuickLinkDest.show_job_list;

  bool get showMachineList =>
      _curQuickLink.value == QuickLinkDest.show_machine_list;

  bool get hasMore => _curQuickLink.value == QuickLinkDest.show_job_list
      ? recruitListUS.recruitHasMore.value
      : _machineHasMore.value;

  void setCurrentDate(DateTime date) {
    _currentDate.value = date;
  }

  void setStartDate(DateTime date) {
    _startDate.value = date;
  }

  void setMonthData(BusinessGetProjectCalendarCountBizModel? value) {
    // 统计数据
    List<StatisticsItemUIState> list =
        StatisticsUIStateHelper.buildStatisticsItem(value?.count);
    _statisticsList.value = list;
    // 事件数据
    var events = CalendarUIStateHelper.convertEntityToCalendarUIState(
        value?.calendar ?? []);
    _events.value = events;
  }

  void setQuickLinkList(List<QuickLinkEntity> list) {
    _quickLinkList.value = list;
  }

  void setMachineList(int page, List<NetModelMachineABizModel> data) {
    this.machineListPage = page;
    if (page > 1 && data.isEmpty) {
      _machineHasMore.value = false;
      return;
    }
    final list = data.map((i) {
      return MachineItemUiState(
          id: i.uu.toString(),
          title: i.title,
          tag: i.tag.map((t) {
            return MachineTag(
              name: t,
              type: t,
            );
          }).toList(),
          mode: i.mode,
          location: i.area,
          time: i.time);
    }).toList();
    if (page == 1) {
      _machineList.value = list;
      return;
    }
    _machineList.value = [..._machineList.value, ...list];
  }

  void setCurQuickLink(QuickLinkDest? dest) {
    _curQuickLink.value = dest;
  }
}
