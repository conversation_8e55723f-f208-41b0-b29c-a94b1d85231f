import 'dart:ui';

import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/net/business_get_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class BillItemUiState {
  final BusinessGetBusinessListBBizModel originalData;

  BillItemUiState({
    required this.originalData,
  });

  // Helper methods similar to the Android adapter
  String get workTypeName {
    final code = originalData.businessType.toInt();
    if (code == RecordType.workLoad.value) {
      return "${RwaRecordType.workLoad.label} ${originalData.unitWorkTypeName}";
    }
    return RwaRecordType.fromCode(code).label;
  }

  String get loadTypeName => originalData.unitWorkTypeName;

  /// 工资展示
  String get moneyDisplay {
    final businessType = originalData.businessType.toInt();
    // 点工 包工
    if (businessType == RecordType.workDays.value ||
        businessType == RecordType.packageWork.value) {
      final feeMoney = originalData.feeMoney;
      final feeStandardId = originalData.feeStandardId;
      if (feeStandardId == null) {
        return "-";
      }
      if (feeMoney == null) {
        return "0.00";
      }
      if (feeStandardId == 0) {
        return "--";
      }
      return feeMoney.formatDoubleToMoney();
    }
    // 工量
    if (businessType == RecordType.workLoad.value) {
      return originalData.money.toString();
    }
    if (businessType == RecordType.dailyWages.value ||
        businessType == RecordType.debt.value ||
        businessType == RecordType.wageLast.value ||
        businessType == RecordType.incomeLast.value ||
        businessType == RecordType.expenditure.value) {
      return originalData.money.formatStringToMoney();
    }
    return originalData.feeMoney.formatDoubleToMoney();
  }

  Color get moneyTextColor {
    final businessType = originalData.businessType.toInt();
    if (businessType == RecordType.wageLast.value ||
        businessType == RecordType.debt.value) {
      return ColorsUtil.yellowMedium;
    }
    return ColorsUtil.blueMedium;
  }

  bool get showWorkNote => originalData.workNoteName.isNotEmpty;

  String get workNoteName {
    if (originalData.workNoteName.isNotEmpty) {
      return originalData.workNoteName;
    }
    return "";
  }

  String get contentText {
    // Handle workload type (type 2)
    if (originalData.businessType == RecordType.workLoad.value) {
      // WORK_AND_ACCOUNT_TYPE_LOAD
      if (originalData.unitNum.isNotEmpty &&
          originalData.unitWorkTypeUnit.isNotEmpty) {
        return '工程量: $originalData.unitNum$originalData.unitWorkTypeUnit';
      }
      return '';
    }

    // For other types (mainly type 1 and 6)
    final workTimeContent = originalData.workTimeFormat;
    return workTimeContent;
  }

  String get overtimeText {
    if (originalData.businessType == RecordType.workLoad.value) {
      return '';
    }
    return originalData.overTimeInfo ?? "";
  }

  bool isHasImg() => originalData.hasImg == 1;

  bool isHasVideo() => false; // Assuming no video field in the model

  bool get showBottomContent =>
      originalData.note.isNotEmpty || isHasImg() || isHasVideo();

  bool needShare() => originalData.isShareSingle == 0;

  String get confirmStatus => ""; // Add your confirm status logic here
}
