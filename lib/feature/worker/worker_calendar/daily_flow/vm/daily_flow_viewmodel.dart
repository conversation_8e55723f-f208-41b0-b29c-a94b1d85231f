import "package:gdjg_pure_flutter/data/worker_flow/ds/model/param/delete_single_record_param_model.dart";
import "package:gdjg_pure_flutter/data/worker_flow/repo/worker_flow_repo.dart";
import "package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/vm/bill_item_ui_state.dart";
import "package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/vm/daily_flow_us.dart";
import "package:gdjg_pure_flutter/utils/refresh_loadmore/refresh_loadmore_vvm.dart";
import "package:pull_to_refresh/pull_to_refresh.dart";

class DailyFlowViewModel {
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);

  final WorkerFlowRepo repo = WorkerFlowRepo();

  late RefreshLoadMoreVvm<BillItemUiState> refreshVvm;

  final DailyFlowUS us = DailyFlowUS();

  DailyFlowViewModel() {
    refreshVvm = RefreshLoadMoreVvm(
        refreshController: this.refreshController,
        dataFetcher: (page, pageSize) {
          return fetchList(page);
        });
  }

  sync() {
    refreshVvm.refresh();
  }

  Future<List<BillItemUiState>> fetchList(int page) async {
    final list = await repo.fetchWorkerDailyFlowList(date: us.curDate);
    final uiList = list.map((i) => BillItemUiState(originalData: i)).toList();
    return uiList;
  }

  void onRefresh() async {
    refreshVvm.refresh();
  }

  void updateCurDate(DateTime date) {
    us.setCurDate(date);
    sync();
  }

  void onDeleteBill(BillItemUiState item) async {
    var params = DeleteSingleRecordAParamModel();
    params.note_id = item.originalData.workNote;
    var id = item.originalData.id.toInt();
    await repo.deleteRecordWork(params, id);
    sync();
  }
}
