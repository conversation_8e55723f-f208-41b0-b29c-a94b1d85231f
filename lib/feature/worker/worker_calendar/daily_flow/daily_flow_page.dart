import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/ad/native_ad_and_vip_view.dart';
import 'package:gdjg_pure_flutter/ad/native_ad_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/change_record_work/change_record_work_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/change_record_work/entity/change_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/daily_flow_route_query.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/vm/bill_item_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/vm/daily_flow_viewmodel.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/widget/worker_flow_bill_item.dart';
import 'package:gdjg_pure_flutter/generated/pigeons/native_ad_api.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/common/pigeon_flutter_api.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/conditional_builder.dart';
import 'package:gdjg_pure_flutter/widget/select_day/select_day.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class DailyFlowPage extends BaseFulPage {
  static openPage(DateTime date) {
    YPRoute.openPage(RouteNameCollection.dailyFlow,
        params: DailyFlowRouteQuery(initDate: date));
  }

  const DailyFlowPage({super.key})
      : super(appBar: const YPAppBar(title: "单日流水"));

  @override
  State createState() => _DailyFlowPageState();
}

class _DailyFlowPageState<DailyFlowPage> extends BaseFulPageState {
  late DailyFlowViewModel vm;
  NativeAdApi? _nativeApi;
  final adParams = <String, Object>{}..["adLocation"] = AdConst.dayFlowPersonal;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    // 获取当前传入时间
    final query = routeParams as DailyFlowRouteQuery?;
    vm = DailyFlowViewModel();
    vm.updateCurDate(query?.initDate ?? DateTime.now());
    _nativeApi = NativeAdApi();
  }

  @override
  Widget yBuild(BuildContext context) {
    final TextTheme textTheme = Theme.of(context).textTheme;
    return Container(
      color: Colors.white,
      child: Stack(
        children: [
          Column(
            children: [
              Obx(() => SelectDay(
                    value: vm.us.curDate,
                    onValueChange: (v) {
                      _nativeApi?.loadNativeAd(adParams);
                      vm.updateCurDate(v);
                    },
                  )),
              Expanded(
                child: SmartRefresher(
                  controller: vm.refreshController,
                  onRefresh: vm.onRefresh,
                  child: Obx(() {
                    return ConditionalBuilder(
                        condition: vm.refreshVvm.list.isNotEmpty,
                        trueBuilder: (v) => ListView.builder(
                              physics: NeverScrollableScrollPhysics(), // 解决滑动冲突
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                final item = vm.refreshVvm.list[index];
                                if (index == vm.refreshVvm.list.length - 1) {
                                  return Column(
                                    children: [
                                      _buildWorkerFlowBillItem(item, index),
                                      Container(height: 160)
                                    ],
                                  );
                                }
                                return _buildWorkerFlowBillItem(item, index);
                              },
                              itemCount: vm.refreshVvm.list.length,
                            ),
                        falseBuilder: (c) {
                          return Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              EmptyView(subtitle: "暂无数据", height: 320),
                              NativeAdAndVipView(adParams),
                            ],
                          );
                        });
                  }),
                ),
              ) // 可根据需要调整
            ],
          ),
          // 悬浮按钮
          Positioned(
            left: 0,
            right: 0,
            bottom: 120.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildBtn(
                    text: "记借支/结算",
                    onPressed: () {},
                    color: ColorsUtil.yellowMedium,
                    textStyle: textTheme.titleMedium!),
                SizedBox(width: 10),
                _buildBtn(
                    text: "记工",
                    onPressed: () {
                      YPRoute.openPage(
                          RouteNameCollection.personalRecordWorkPoints);
                    },
                    color: ColorsUtil.ypPrimaryColor,
                    textStyle: textTheme.titleMedium!),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkerFlowBillItem(BillItemUiState item, int index) {
    return WorkerFlowBillItem(
      item: item,
      onCheckPressed: () {
        ToastUtil.showToast("more");
      },
      onChangePressed: () {
        var businessType =
            RwaRecordType.fromCode(item.originalData.businessType.toInt());
        var props = ChangeRecordWorkProps(
          businessType: businessType,
        );
        ChangeRecordWorkPage.openPage(props);
      },
      onDeletePressed: () {
        vm.onDeleteBill(item);
      },
    );
  }

  Widget _buildBtn(
      {required String text,
      required VoidCallback onPressed,
      required Color color,
      required TextStyle textStyle}) {
    return OutlinedButton(
        onPressed: onPressed,
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: color),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          backgroundColor: Colors.white,
        ),
        child: Text(text, style: textStyle.copyWith(color: color)));
  }
}
