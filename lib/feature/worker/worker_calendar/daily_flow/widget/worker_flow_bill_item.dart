import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/vm/bill_item_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/widget/delete_single_record_view.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/label/text_label.dart';

class WorkerFlowBillItem extends StatelessWidget {
  final BillItemUiState item;
  final VoidCallback? onCheckPressed;
  final VoidCallback? onChangePressed;
  final ValueChanged<bool>? onCheckChanged;
  final Function onDeletePressed;

  const WorkerFlowBillItem({
    super.key,
    required this.item,
    this.onCheckPressed,
    this.onChangePressed,
    this.onCheckChanged,
    required this.onDeletePressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onChangePressed?.call();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Color(0xFFF5F5F5),
              width: 1.0, // 分隔线宽度
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 类型名+费用
            Row(
              children: [
                Text(
                  item.workTypeName,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                    color: ColorsUtil.black32,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Spacer(),
                Row(
                  children: [
                    Text(
                      item.moneyDisplay,
                      style: TextStyle(
                        color: item.moneyTextColor,
                        fontSize: 22,
                        fontFamily: FontUtil.fontCondMedium,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(
                      width: 4,
                    ),
                    IconFont(
                      IconNames.saasArrowRight,
                      size: 18.w,
                      color: "#8A8A99",
                    )
                  ],
                ),
              ],
            ),
            // 项目+上班时间+加班时间+按钮
            Container(
              padding: const EdgeInsets.only(top: 8, bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Visibility(
                          visible: item.showWorkNote,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 4,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0x1A5290FD),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              item.workNoteName,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: TextStyle(
                                color: ColorsUtil.black32,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                        Visibility(
                          visible: item.contentText.isNotEmpty,
                          child: Padding(
                            padding: const EdgeInsets.only(top: 2.0),
                            child: Text(
                              item.contentText,
                              style: TextStyle(
                                color: ColorsUtil.black32,
                                fontSize: 14,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                        Visibility(
                          visible: item.overtimeText.isNotEmpty,
                          child: Padding(
                            padding: const EdgeInsets.only(top: 2.0),
                            child: Text(
                              item.overtimeText,
                              style: TextStyle(
                                color: ColorsUtil.black32,
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      TextLabel(
                        label: "对工",
                        bgColor: ColorsUtil.blueLight,
                        labelColor: ColorsUtil.primaryColor,
                        isBold: true,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        onLabelTap: (id) {},
                      ),
                      SizedBox(
                        width: 8,
                      ),
                      TextLabel(
                        label: "修改",
                        bgColor: ColorsUtil.blueLight,
                        labelColor: ColorsUtil.primaryColor,
                        isBold: true,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        onLabelTap: (id) {
                          onChangePressed?.call();
                        },
                      ),
                      SizedBox(
                        width: 8,
                      ),
                      TextLabel(
                        label: "删除",
                        bgColor: ColorsUtil.blueLight,
                        labelColor: ColorsUtil.primaryColor,
                        isBold: true,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        onLabelTap: (id) {
                          _onTapDelete(this);
                        },
                      ),
                    ],
                  )
                ],
              ),
            ),
            // 备注及图片、视频标识
            Visibility(
              visible: item.showBottomContent,
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      item.originalData.note,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      style: const TextStyle(
                        color: Color(0xFF8A8A99),
                        fontSize: 13,
                      ),
                    ),
                  ),
                  Visibility(
                    visible: item.isHasImg(),
                    child: Padding(
                      padding: EdgeInsets.only(left: 2),
                      child: Image.network(
                        "https://cdn.yupaowang.com/yupao_app/icon_rwa_image.png",
                        width: 18.w,
                        height: 18.h,
                      ),
                    ),
                  ),
                  Visibility(
                    visible: item.isHasVideo(),
                    child: Padding(
                      padding: EdgeInsets.only(left: 2),
                      child: Image.network(
                        "https://cdn.yupaowang.com/jgjz/icon_video_act.png",
                        width: 18.w,
                        height: 18.h,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  _onTapDelete(WorkerFlowBillItem item) {
    YPRoute.openDialog(
      builder: (context) => DeleteSingleRecordView(onConfirm: () {
        onDeletePressed();
        YPRoute.closeDialog();
      }),
      alignment: Alignment.center,
    );
  }
}
