import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class DeleteSingleRecordView extends StatefulWidget {
  final Function() onConfirm;

  const DeleteSingleRecordView({Key? key, required this.onConfirm}) : super(key: key);

  @override
  _DeleteSingleRecordViewState createState() => _DeleteSingleRecordViewState();
}

class _DeleteSingleRecordViewState extends State<DeleteSingleRecordView> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();

    // 延迟获取焦点
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_focusNode.hasFocus) {
        _focusNode.unfocus();
      }
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  String get value => _controller.text;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.symmetric(horizontal: 32),
      child: Container(
        padding: const EdgeInsets.only(top: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '确定删除这笔记工？',
              style: TextStyle(
                fontSize: 16,
                color: ColorsUtil.black85,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '60天内删除数据可前往我的-回收站中找回',
              style: TextStyle(fontSize: 14, color: ColorsUtil.black65),
            ),
            const SizedBox(height: 24),
            Divider(
              height: 1,
              color: ColorsUtil.inputBgColor,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Expanded(
                  child: GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () => YPRoute.closeDialog(),
                    child: Container(
                      height: 44,
                      alignment: Alignment.center,
                      child: Text(
                        '取消',
                        style: TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 1,
                  height: 44,
                  color: ColorsUtil.inputBgColor,
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () => widget.onConfirm(),
                    child: Container(
                        height: 44,
                        alignment: Alignment.center,
                        child: Text(
                          '确定',
                          style: TextStyle(
                              color: ColorsUtil.primaryColor,
                              fontWeight: FontWeight.w500),
                        )),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
