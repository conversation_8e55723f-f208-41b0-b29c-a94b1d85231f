import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/completed_projects/completed_project_item_card.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project/completed_projects/vm/completed_projects_vm.dart';
import 'package:gdjg_pure_flutter/widget/SearchInput.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';

/// 已结清项目Tab页面
class CompletedProjectsTab extends StatefulWidget {
  final Function(VoidCallback)? onRefreshCallback;

  const CompletedProjectsTab({super.key, this.onRefreshCallback});

  @override
  State<CompletedProjectsTab> createState() => CompletedProjectsTabState();
}

class CompletedProjectsTabState extends State<CompletedProjectsTab> with AutomaticKeepAliveClientMixin {
  late CompletedProjectsVM _viewModel;
  late RefreshController _refreshController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _viewModel = CompletedProjectsVM();
    _refreshController = RefreshController(initialRefresh: false);

    // 注册刷新回调
    widget.onRefreshCallback?.call(_refreshData);
  }

  /// 获取ViewModel引用，给page使用检查是否为空
  CompletedProjectsVM getViewModel() {
    return _viewModel;
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  /// 刷新数据回调
  Future<void> _refreshData() async {
    await _viewModel.onRefresh();
  }

  /// 刷新回调
  Future<void> _onRefresh() async {
    await _viewModel.onRefresh();
    _refreshController.refreshCompleted();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: true,
      enablePullUp: false,
      onRefresh: _onRefresh,
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          // 搜索框区域
          SliverToBoxAdapter(
            child: _buildSearchSection(),
          ),

          // 项目列表
          _buildProjectListSliver(),
        ],
      ),
    );
  }

  /// 搜索栏
  Widget _buildSearchSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: SearchInput(
        hintText: '请输入项目名称',
        backgroundColor: Colors.white,
        prefixIcon: Image.asset(
          'assets/images/common/icon_search.png',
          color: Colors.black,
        ),
        onChanged: _viewModel.onSearchChanged,
      ),
    );
  }

  /// 项目列表
  Widget _buildProjectListSliver() {
    return Obx(() {
      // 空列表
      if (_viewModel.completedProjectsUS.showProList.isEmpty) {
        return SliverToBoxAdapter(
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.only(top: 70.h, bottom: 50.h),
            child: Column(
              children: [
                Image.asset(
                  Assets.commonIconEmptyTeamProject,
                  width: 120.w,
                  height: 120.w,
                  fit: BoxFit.contain,
                ),
                Text(
                  '暂无已结清项目',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF8A8A99),
                  ),
                ),
              ],
            ),
          ),
        );
      }

      // 有数据时显示项目列表
      return SliverPadding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        sliver: SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final project = _viewModel.completedProjectsUS.showProList[index];
              return CompletedProjectItemCard(
                item: project,
                isAmountVisible: _viewModel.completedProjectsUS.isAmountVisible.value,
                onDeleteProject: (projectId, corpId) => _viewModel.deleteProject(projectId, corpId),
                onRecoverProject: (projectId, corpId) => _viewModel.recoverProject(projectId, corpId),
              );
            },
            childCount: _viewModel.completedProjectsUS.showProList.length,
          ),
        ),
      );
    });
  }
}
