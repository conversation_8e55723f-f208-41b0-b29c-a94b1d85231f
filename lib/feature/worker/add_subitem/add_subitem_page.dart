import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';

class AddSubitemPage extends BaseFulPage {
  const AddSubitemPage({Key? key})
      : super(appBar: const YPAppBar(title: "添加分项"));

  @override
  State createState() => _AddSubitemPageState();
}

class _AddSubitemPageState extends BaseFulPageState {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _unitController = TextEditingController();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    // 初始化页面数据
  }

  @override
  void dispose() {
    _nameController.dispose();
    _unitController.dispose();
    super.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Column(
      children: [
        // 输入字段区域
        Container(
          color: Colors.white,
          child: Column(
            children: [
              _buildInputRow(
                label: '分项名称',
                placeholder: '请输入分项名称',
                controller: _nameController,
                showArrow: false,
              ),
              _buildInputRow(
                label: '分项单位',
                placeholder: '请选择分项单位',
                controller: _unitController,
                showArrow: true,
                onTap: () {
                  YPRoute.openPage(RouteNameCollection.addSubitemUnit)
                      ?.then((res) {
                    if (res != null) {}
                  });
                },
              ),
            ],
          ),
        ),
        // 内容区域
        Expanded(
          child: Container(
            color: const Color(0xFFF5F5F5),
            width: double.infinity,
          ),
        ),
        // 底部保存按钮
        SafeArea(
          top: false,
          child: Container(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
            color: Colors.white,
            child: SizedBox(
              width: double.infinity,
              height: 44,
              child: ElevatedButton(
                onPressed: () {
                  _saveSubitem();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2F6BFF),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('保存', style: TextStyle(fontSize: 16)),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建输入行
  Widget _buildInputRow({
    required String label,
    required String placeholder,
    required TextEditingController controller,
    required bool showArrow,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1),
          ),
        ),
        child: Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF323232),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextField(
                controller: controller,
                enabled: !showArrow, // 如果是选择类型，禁用输入
                decoration: InputDecoration(
                  hintText: placeholder,
                  hintStyle: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF999999),
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF323232),
                ),
              ),
            ),
            if (showArrow)
              const Icon(
                Icons.chevron_right,
                color: Color(0xFF999999),
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  /// 保存分项
  void _saveSubitem() {
    // if (_nameController.text.trim().isEmpty) {
    //   Get.snackbar('提示', '请输入分项名称');
    //   return;
    // }
    // if (_unitController.text.trim().isEmpty) {
    //   Get.snackbar('提示', '请选择分项单位');
    //   return;
    // }
    //
    // // 这里处理保存逻辑
    // print('保存分项: ${_nameController.text}, 单位: ${_unitController.text}');
    //
    // // 保存成功后返回上一页
    // Get.back(result: {
    //   'name': _nameController.text.trim(),
    //   'unit': _unitController.text.trim(),
    // });
  }
}