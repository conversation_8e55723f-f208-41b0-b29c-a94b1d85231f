import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_statistics_list_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/worker_project_detail/vm/worker_project_detail_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dynamic_height_calendar.dart';
import 'package:gdjg_pure_flutter/widget/select_month/select_month.dart';
import 'package:gdjg_pure_flutter/widget/network_carousel_widget.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_week.dart';
import 'package:get/get.dart';

/// 我创建项目详情页面参数
class MyCreatedProjectDetailProps {
  final ProjectGetProjectListABizModel projectModel;
  final bool isFromCompleted;

  MyCreatedProjectDetailProps({
    required this.projectModel,
    this.isFromCompleted = false,
  });
}

/// 工人项目详情页面
class WorkerProjectDetailPage extends BaseFulPage {
  const WorkerProjectDetailPage({super.key}) : super(appBar: null);

  @override
  State createState() => _WorkerProjectDetailPageState();
}

class _WorkerProjectDetailPageState extends BaseFulPageState {
  late WorkerProjectDetailVM _viewModel;
  final GlobalKey<DynamicHeightCalendarState> _calendarKey =
      GlobalKey<DynamicHeightCalendarState>();

  @override
  void onPageCreate() {
    super.onPageCreate();
    _viewModel = WorkerProjectDetailVM();
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    ProjectGetProjectListABizModel? projectModel;
    bool isFromCompleted = false;

    if (routeParams is MyCreatedProjectDetailProps) {
      projectModel = routeParams.projectModel;
      isFromCompleted = routeParams.isFromCompleted;
    } else if (routeParams is ProjectGetProjectListABizModel) {
      projectModel = routeParams;
      isFromCompleted = false;
    }

    if (projectModel != null) {
      _viewModel.initProjectInfo(projectModel, isFromCompleted: isFromCompleted);
      // 动态设置AppBar标题
      dynamicTitle = projectModel.name;
    }
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildWithResourceWidget(
        title: dynamicTitle ?? '项目详情',
        resourceText: '拍证据工资有保障',
        resourceIcon: Assets.commonIcTakePhoneSmall,
        onBackTap: () => YPRoute.closePage(),
      ),
      body: Stack(
        children: [
          Container(
            color: Colors.white,
            child: Column(
              children: [
                // 轮播图
                _buildCarouselSection(),

                // 分割线
                Divider(height: 6.h, color: Color(0xFFF0F0F0), thickness: 6.h),

                Expanded(
                  child: CustomScrollView(
                    slivers: [
                      // 月份切换器
                      SliverToBoxAdapter(
                        child: _buildMonthSwitchView(),
                      ),

                      // 统计数据
                      SliverToBoxAdapter(
                        child: _buildStatisticsView(),
                      ),

                      // 周行
                      SliverToBoxAdapter(
                        child: _buildWeekView(),
                      ),

                      // 大日历
                      _buildCalendarView(),
                    ],
                  ),
                ),

                // 底部按钮区域
                _buildBottomButtons(),
              ],
            ),
          ),
          // 悬浮按钮
          Positioned(
            bottom: 80,
            child: SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildStackButton(1),
                  SizedBox(width: 16),
                  _buildStackButton(2),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建轮播图区域
  Widget _buildCarouselSection() {
    return Container(
      color: Color(0xFFF0F0F0),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
      child: NetworkCarouselWidget(
        height: 48.h,
        code: 'JGJZ_PROJECT_CALENDAR_PERSONAL_B',
      ),
    );
  }

  /// 月份切换控件
  Widget _buildMonthSwitchView() {
    return Obx(
      () => SelectMonth(
        value: _viewModel.us.dataTime.value,
        onValueChange: (selectTime) {
          _viewModel.updateDateRange(selectTime);
          _calendarKey.currentState?.changeMonth(selectTime);
        },
      ),
    );
  }

  /// 统计布局
  Widget _buildStatisticsView() {
    return Obx(() {
      if (_viewModel.us.isStatisticsEmpty) {
        return SizedBox(
          width: double.infinity,
          height: 56.h,
          child: Center(
            child: Text(
              "当月无记工",
              style: TextStyle(color: Color(0xFF323232), fontSize: 16.sp),
            ),
          ),
        );
      }
      return GroupStatisticsView(
        items: _viewModel.us.statisticsList,
        initialVisibleCount: 1,
        onItemTap: (index) {
          // TODO: 实现统计点击功能
        },
      );
    });
  }

  /// 周行
  Widget _buildWeekView() {
    return Container(
        color: Color(0xFFF5F6FA),
        height: 28.h,
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        child: CalendarWeek());
  }

  /// 大日历
  Widget _buildCalendarView() {
    return Obx(
      () => DynamicHeightCalendar(
        key: _calendarKey,
        onValueChange: (date) {
          _viewModel.updateDateRange(date);
        },
        events: _viewModel.us.events.value,
        onDayTap: (DateTime value) {
          // 项目详情页面暂不处理日期点击事件
        },
      ),
    );
  }

  /// 构建底部按钮区域
  Widget _buildBottomButtons() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Divider(height: 1.h, color: Color(0xFFF0F0F0), thickness: 1.h),
          SizedBox(
            height: 6.h,
          ),
          // 按钮区域
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: [
                _buildBottomButton(
                  iconPath: Assets.tabbarIconTabbarStatisticNormal,
                  text: '统计',
                  onTap: _viewModel.onStatisticsTap,
                ),
                _buildBottomButton(
                  iconPath: Assets.workerIconAttendanceSheetTab,
                  text: '考勤表',
                  onTap: _viewModel.onAttendanceClick,
                ),
                _buildBottomButton(
                  iconPath: Assets.tabbarIconTabbarNotSettledNormal,
                  text: '未结',
                  onTap: _viewModel.onUnsettledClick,
                ),
                _buildBottomButton(
                  iconPath: Assets.visitorIcMineFuncSetting,
                  text: '设置',
                  onTap: _viewModel.onSettingsClick,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单个底部按钮
  Widget _buildBottomButton({
    required String iconPath,
    required String text,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              color: const Color(0xFF666666),
              iconPath,
              width: 24.w,
              height: 24.w,
            ),
            Text(
              text,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建悬浮按钮
  Widget _buildStackButton(int type) {
    var buttonText = "";
    var mWidth = 0.0;
    Color? mColor;
    Function()? mClick;
    if (type == 1) {
      buttonText = "记借支/结算";
      mColor = Color(0xFFFF9500);
      mWidth = 116;
      mClick = () {
        // TODO: 实现记借支/结算功能
      };
    } else if (type == 2) {
      buttonText = "记工";
      mColor = ColorsUtil.primaryColor;
      mWidth = 85;
      mClick = () {
        // TODO: 实现记工功能
      };
    }
    if (mColor == null || mWidth == 0 || buttonText.isEmpty || mClick == null) return SizedBox();
    return InkWell(
      onTap: mClick,
      child: Container(
        width: mWidth,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: mColor, width: 1),
        ),
        alignment: Alignment.center,
        child: Text(
          buttonText,
          style: TextStyle(color: mColor, fontSize: 16),
        ),
      ),
    );
  }
}
