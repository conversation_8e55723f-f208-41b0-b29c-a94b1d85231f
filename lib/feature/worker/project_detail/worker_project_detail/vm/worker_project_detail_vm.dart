import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistice_helper.dart';
import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/worker_project_detail/vm/worker_project_detail_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_setup/my_created_project_setup/my_created_project_setup_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/worker_project_unsettled_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/repo/corp_select_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/group_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/param/group_calendar_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/calendar_uistate_helper.dart';

/// 工人项目详情页面ViewModel
class WorkerProjectDetailVM {
  final WorkerProjectDetailUS us = WorkerProjectDetailUS();
  final _workerProjectRepo = WorkerProjectRepo();
  final _corpSelectRepo = CorpSelectRepo();
  final _groupRepo = GroupRepo();

  var _dateTime = DateTime.now();
  bool _isFromCompleted = false;

  /// 初始化项目信息
  void initProjectInfo(ProjectGetProjectListABizModel projectInfo, {bool isFromCompleted = false}) {
    us.projectInfo.value = projectInfo;
    _isFromCompleted = isFromCompleted;
    // 初始化网络请求
    _startApiCallFlow(projectInfo);
  }
  
  void _startApiCallFlow(ProjectGetProjectListABizModel projectInfo) async {
    final deptId = projectInfo.deptId.toString();
    us.setDeptId(deptId);
    
    _fetchProjectDetail(deptId);
  }

  /// 刷新数据
  void refresh() {
    final projectInfo = us.projectInfo.value;
    if (projectInfo != null) {
      _startApiCallFlow(projectInfo);
    }
  }

  /// 更新日期范围
  void updateDateRange(DateTime dateTime) {
    _dateTime = dateTime;
    us.setCurrentDate(dateTime);
    _fetchCalendarData(dateTime);
  }
  
  /// 获取项目详情
  void _fetchProjectDetail(String deptId) async {
    if (deptId.isEmpty) {
      ToastUtil.showToast('项目参数错误');
      return;
    }

    final result = await _workerProjectRepo.getDeptDetail(deptId);

    if (result.isOK()) {
      final deptData = result.getSucData();
      if (deptData != null) {
        // 设置项目基本信息
        us.setProjectName(deptData.name);
        us.setWorkNoteId(deptData.workNoteId.toString());
        us.setCorpId(deptData.corpId);

        // 切换企业
        _fetchCorpSelect(deptData.corpId);

        // 获取日历数据
        _fetchCalendarData(_dateTime);
      }
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '获取项目详情失败');
    }
  }

  /// 切换企业
  void _fetchCorpSelect(double corpId) async {
    final param = CorpSelectParamModel(corp_id: corpId.toString());
    await _corpSelectRepo.fetchCorpSelect(param);
  }

  /// 获取日历数据
  void _fetchCalendarData(DateTime dateTime) async {
    if (us.workNoteId.value.isEmpty) return;

    final params = GroupCalendarParamModel();
    params.work_note = us.workNoteId.value;
    params.start_time = DateUtil.formatStartDate(dateTime);
    params.end_time = DateUtil.formatEndDate(dateTime);

    final result = await _groupRepo.getGroupCalendar(params);
    if (result.isOK()) {
      _convertEntityToUIState(result.getSucData());
      _convertEntityToCalendarUIState(result.getSucData());
    }
  }

  /// 转换统计数据
  void _convertEntityToUIState(GroupCalendarBizModel? data) {
    List<StatisticsItemUIState> list =
        StatisticsUIStateHelper.buildStatisticsItem(data?.count);
    us.setStatisticsList(list);
  }

  /// 转换日历事件数据
  void _convertEntityToCalendarUIState(GroupCalendarBizModel? data) {
    var events = CalendarUIStateHelper.convertEntityToCalendarUIState(
        data?.calendar ?? []);
    us.setEvents(events);
  }

  /// 点击统计
  void onStatisticsTap() {
    if (us.workNoteId.value.isEmpty) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    if (us.deptId.value.isEmpty) {
      ToastUtil.showToast('部门信息错误');
      return;
    }

    final params = GroupProBillProps(
      workNoteId: us.workNoteId.value,
      workNoteName: us.projectName.value,
      deptId: double.tryParse(us.deptId.value),
      isJoin: false,
    );

    // 导航到统计页面
    YPRoute.openPage(RouteNameCollection.groupProBill, params: params);
  }

  /// 考勤表按钮点击事件
  void onAttendanceClick() {
    // TODO: 实现考勤表功能
  }

  /// 未结按钮点击事件
  void onUnsettledClick() {
    final projectInfo = us.projectInfo.value;
    if (projectInfo == null) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    // 跳转到未结算页面
    YPRoute.openPage(
      RouteNameCollection.workerProjectUnsettled,
      params: WorkerProjectUnsettledPageArgs(
        projectId: projectInfo.id.toString(),
        projectName: projectInfo.name,
      ),
    );
  }
  
  /// 设置按钮点击事件
  void onSettingsClick() {
    final projectInfo = us.projectInfo.value;
    if (projectInfo == null) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    // 跳转到在建项目页面
    YPRoute.openPage(
      RouteNameCollection.projectSetup,
      params: MyCreatedProjectSetupProps(
        projectId: projectInfo.id.toString(),
        isFromCompleted: _isFromCompleted,
      ),
    );
  }
}
