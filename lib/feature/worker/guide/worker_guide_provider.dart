import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/guide_utils.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../../../widget/bubble.dart';
import '../../../widget/guide_bubble.dart';

class WorkerGuideProvider {
  static TutorialCoachMark showStep1(
    final BuildContext context,
    final Rect rect,
    final Rect? todayRect,
    final void Function() onFinish,
  ) =>
      GuideUtils.createSingleFocusTutorialCoachMark(
        rect,
        onFinish,
        [
          TargetContent(
            align: ContentAlign.custom,
            customPosition: CustomTargetContentPosition(
              top: rect.bottom,
              left: rect.left,
            ),
            padding: EdgeInsets.only(top: 4.h, left: 16.w, right: 32.w),
            builder: (context, controller) => GuideBubble(
              voice: "sounds/waa_guide_calendar_personal.MP3",
              step: "1/3",
              content: "点击日历上的日期，开始第一笔记工",
              nextAction: onFinish,
            ),
          ),
          if (todayRect != null)
            TargetContent(
              align: ContentAlign.custom,
              customPosition: CustomTargetContentPosition(
                top: todayRect.top + todayRect.height / 2.0,
                left: todayRect.left + 8.w,
              ),
              padding: EdgeInsets.zero,
              builder: (context, controller) => Align(
                alignment: Alignment.topLeft,
                child: Image.asset(
                  'assets/images/worker/finger_run.gif',
                  width: 50,
                  height: 50,
                  fit: BoxFit.contain,
                ),
              ),
            ),
        ],
      )..show(context: context);

  static TutorialCoachMark showStep2(
    final BuildContext context,
    final Rect rect,
    final void Function() onFinish,
  ) =>
      GuideUtils.createSingleFocusTutorialCoachMark(
        rect,
        onFinish,
        [
          TargetContent(
            align: ContentAlign.custom,
            customPosition: CustomTargetContentPosition(
              top: rect.bottom,
              left: rect.left,
            ),
            padding: EdgeInsets.only(top: 4.h, left: 16.w, right: 32.w),
            builder: (context, controller) => GuideBubble(
              trianglePosition: TrianglePosition.topLeft,
              triangleOffset: 32.w,
              voice: "sounds/waa_guide_select_type.MP3",
              step: "2/3",
              content: "选择合适的记工方式",
              nextAction: onFinish,
            ),
          )
        ],
      )..show(context: context);

  static TutorialCoachMark showStep3(
    final BuildContext context,
    final Rect rect,
    final void Function() onFinish,
  ) =>
      GuideUtils.createSingleFocusTutorialCoachMark(
        rect,
        onFinish,
        [
          TargetContent(
            align: ContentAlign.custom,
            customPosition: CustomTargetContentPosition(
              top: rect.bottom,
              left: rect.left,
            ),
            padding: EdgeInsets.only(top: 4.h, left: 16.w, right: 32.w),
            builder: (context, controller) => GuideBubble(
                trianglePosition: TrianglePosition.topLeft,
                triangleOffset: 32.w,
                voice: "sounds/waa_guide_select_record_time.MP3",
                step: "3/3",
                content: "选择工作时长，默认选择一个工",
                next: "开始记工",
                nextAction: onFinish),
          ),
        ],
      )..show(context: context);
}
