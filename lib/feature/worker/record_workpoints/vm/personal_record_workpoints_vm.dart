import 'package:flutter/painting.dart';
import 'package:gdjg_pure_flutter/data/change_project/repo/model/biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/add_record_work_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/check_business_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/project_get_last_business_project_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/project_get_last_business_project_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/worker_account_record_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/vm/personal_record_workpoints_us.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/repeat_record_dialog.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:intl/intl.dart';

class PersonalRecordWorkPointsVM {
  final us = PersonalRecordWorkPointsUS();

  final recordRep = WorkerAccountRecordRepo();
  final _workerProjectRep = WorkerProjectRepo();

  PersonalRecordWorkPointsVM() {
    ever(us.projectObs, (value) {
      // 项目id改变时，重新获取项目信息
      if (value != null) {
        _getProjectInfo(value.id.trimTrailingZeros());
      }
    });
  }

  /// 获取项目信息
  _getProjectInfo(String noteId) {
    _workerProjectRep.getProject(noteId).then((result) {
      if (result.isOK()) {
        var data = result.getSucData();
        if (data != null) {
          us.setProjectInfo(data);
        }
      }
    });
  }

  changeProject() {
    // us.projectName.value = '项目名';
    // us.projectName.refresh();
    YPRoute.openPage(RouteNameCollection.changeProjectPage, params: us.project?.id)?.then((result) {
      if (result != null) {
        var model = result as ProjectGetAllProjectNameABizModel;
        us.setProject(ProjectGetLastBusinessProjectBizModel(
          id: model.id,
          name: model.name,
          deptId: model.deptId,
          corpId: model.corpId,
        ));
        // us.projectName.value = model.name;
      }
    });
  }

  // 设置选择日期
  setSelectList(List<DateTime> list) {
    us.setSelectList(list);
  }

  // 获取上次记工的记工本
  getLastBusinessProject() {
    recordRep
        .getProjectLastBusinessProject(ProjectGetLastBusinessProjectParamModel(
      identity: '2',
      status: '0',
      data_type: '1',
    ))
        .then((result) {
      if (result.isOK()) {
        var data = result.getSucData();
        if (data != null) {
          us.setProject(data);
        }
      }
    });
  }

  // 设置项目
  initProject(double? projectId, String? projectName) {
    if (projectId != null && projectName != null) {
      us.setProject(ProjectGetLastBusinessProjectBizModel(id: projectId, name: projectName));
    } else {
      getLastBusinessProject();
    }
  }

  // 设置时间
  initDate(DateTime? date) {
    if (date != null) {
      us.setSelectList([date]);
    } else {
      us.setSelectList([DateTime.now()]);
    }
  }

  recordPointWork(RwaRecordType businessType, BusinessAddMyselfAParamModel param) {
    _checkBusiness(businessType, () {
      param.business_type = businessType.code.value.toString();
      param.identity = RecordNoteType.personal.value;
      param.business_time = _datesToFormattedString();
      param.work_note = us.project?.id.trimTrailingZeros();
      recordRep.addRecordWork(param).then((result) {
        if (result.isOK()) {
          ToastUtil.showToast('记工成功');
          YPRoute.closePage(true);
        }
      });
    });
  }

  /// 将 DateTime 列表转换为格式化字符串
  String _datesToFormattedString() {
    // 使用 DateFormat 格式化日期
    final DateFormat formatter = DateFormat('yyyy-MM-dd');

    // 遍历列表，格式化每个 DateTime，并用 join(',') 连接
    return us.selectDates.map((date) => formatter.format(date)).join(',');
  }

  /// 检查是否记过工
  _checkBusiness(RwaRecordType businessType, Function() onConfirmRepeat) {
    recordRep
        .checkBusiness(BusinessCheckBusinessParamModel(
      workerId: us.projectInfo?.workerId.trimTrailingZeros(),
      workNoteId: us.project?.id.trimTrailingZeros(),
      businessType: businessType.code.value.toString(),
      businessTimes: _datesToFormattedString(),
    ))
        .then((result) {
      if (result.isOK()) {
        var data = result.getSucData();
        if (data != null) {
          if (data.isRecord()) {
            // 重复记工弹框
            YPRoute.openDialog(
              builder: (context) => RepeatRecordDialog(
                onConfirm: () {
                  onConfirmRepeat();
                },
              ),
              alignment: Alignment.center,
            );
          } else {
            onConfirmRepeat();
          }
        }
      }
    });
  }
}
