
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/project_get_last_business_project_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_biz_model.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class PersonalRecordWorkPointsUS {

  // 最后记工项目
  final Rx<ProjectGetLastBusinessProjectBizModel?> _project = Rx<ProjectGetLastBusinessProjectBizModel?>(null);

  ProjectGetLastBusinessProjectBizModel? get project => _project.value;
  Rx<ProjectGetLastBusinessProjectBizModel?> get projectObs => _project;
  setProject(ProjectGetLastBusinessProjectBizModel value) {
    _project.value = value;
  }


  final Rx<ProjectGetProjectBizModel?> _projectInfo = Rx<ProjectGetProjectBizModel?>(null);
  ProjectGetProjectBizModel? get projectInfo => _projectInfo.value;
  setProjectInfo(ProjectGetProjectBizModel value) {
    _projectInfo.value = value;
  }


  // 选择记工单的日期集合
  final _selectDates = <DateTime>[].obs;
  List<DateTime> get selectDates => _selectDates.value;
  setSelectList(List<DateTime> list) {
    _selectDates.value = list;
  }

  String getSelectDateString() {
    var str = '';

    switch (selectDates.length) {
      case 0:
        str = '请选择日期';
        break;
      case 1:
      var df = DateFormat('yyyy年MM月dd日');
      str = df.format(selectDates.first);
        // str = '${selectDates.first.year}年${selectDates.first.month}月${selectDates.first.day}日';
        break;
      default:
        str = '共选择${selectDates.length}天';
      }

   return str;

  }



}