import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/image_selector_and_remark_view/image_selector_and_remark_view.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/widget/dashed_vertical_divider.dart';
import 'package:gdjg_pure_flutter/widget/point_work/entity/record_workpoints_model.dart';
import 'package:gdjg_pure_flutter/widget/point_work/point_work_controller.dart';
import 'package:gdjg_pure_flutter/widget/point_work/vm/point_work_vm.dart';

class OtherExpenseView extends StatefulWidget {
  const OtherExpenseView({
    super.key,
  });

  @override
  _OtherExpenseViewState createState() => _OtherExpenseViewState();
}

class _OtherExpenseViewState extends State<OtherExpenseView> {
  late PointWorkVM vm;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    // widget.controller?.removeListener();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      child: _buildExpenseSection(),
    );
  }

  /// 费用选择区域
  Widget _buildExpenseSection() {
    return GestureDetector(
      onTap: () {
        YPRoute.openPage(RouteNameCollection.expenseChoose);
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '费用：',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF000000),
                  ),
                ),
                Expanded(
                  child: Row(
                    spacing: 5.w,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text('xx费用'),
                      Image.asset(
                        Assets.commonIconArrowRightGrey,
                        height: 16,
                        width: 16,
                      )
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 选择照片和备注
  // Widget _buildSelectPhotoAndRemark() {
  //   return ImageSelectorAndRemarkView(
  //     remark: '11111',
  //     imageUrls: [
  //       'https://static-test-public.cdqlkj.cn/r/4614/103/pb/p/20250912/d8bd83ec04024cb592000835acd6363a.png'
  //     ],
  //   );
  // }
}
