import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/add_record_work_param_model.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

import 'entity/daily_wages_model.dart';

class DailyWagesController extends ValueNotifier<DailyWagesModel> {
  DailyWagesController() : super(DailyWagesModel(money: '', note: ''));

  void setMoney(String money) {
    value.money = money;
    notifyListeners();
  }


  DailyWagesModel get recordInfo => value;


  BusinessAddMyselfAParamModel getRecordParam(){
    final param =  BusinessAddMyselfAParamModel();
    var moneyText = value.money.endsWith('.')? value.money.substring(0,value.money.length-1) : value.money;
    if(moneyText.isEmpty){
      moneyText = '0';
    }
    param.money = double.parse(moneyText).trimTrailingZeros();
    // 图片
    param.img_url = '';
    param.resource_ext = [];
    return param;
  }

}
