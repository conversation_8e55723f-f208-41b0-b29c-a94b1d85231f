import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/image_selector_and_remark_view/image_selector_and_remark_controller.dart';

/// 点工ViewModel
class ImageSelectorAndRemarkVM {
  final ImageSelectorAndRemarkController controller;

  ImageSelectorAndRemarkVM(this.controller);

  void addPhoto(String photoUrl) {
    controller.addPhoto(photoUrl);
  }

  void addPhotos(List<String> photoUrls) {
    controller.addPhotos(photoUrls);
  }

  void updateRemark(String remark) {
    controller.updateRemark(remark);
  }

  /// 删除照片
  void removePhoto(int index) {
    controller.removePhoto(index);
  }
}
