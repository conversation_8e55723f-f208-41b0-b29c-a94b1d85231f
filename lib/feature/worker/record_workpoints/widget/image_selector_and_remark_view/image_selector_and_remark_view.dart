import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/common_page/notes/entity/notes_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/image_selector_and_remark_view/image_selector_and_remark_controller.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/image_selector_and_remark_view/vm/image_selector_and_remark_vm.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

import '../../../../bill_flow/page/view/cash_advance_view.dart';

/// 选择照片和备注组件
class ImageSelectorAndRemarkView extends StatefulWidget {
  String defRemark = '';
  List<String> defImageUrls = [];
  bool isShowRemind = true;
  final ImageSelectorAndRemarkController controller;

  ImageSelectorAndRemarkView(
      {super.key,
      this.defRemark = '',
      this.defImageUrls = const [],
      this.isShowRemind = true,
      required this.controller});

  @override
  State<ImageSelectorAndRemarkView> createState() =>
      _ImageSelectorAndRemarkViewState();
}

class _ImageSelectorAndRemarkViewState extends State<ImageSelectorAndRemarkView>
    with AutomaticKeepAliveClientMixin {
  late ImageSelectorAndRemarkVM vm;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    vm = ImageSelectorAndRemarkVM(widget.controller);
    vm.updateRemark(widget.defRemark);
    vm.addPhotos(widget.defImageUrls);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以保持页面状态
    return ValueListenableBuilder(
        valueListenable: vm.controller,
        builder: (context, model, child) {
          return Column(
            children: [
              // 照片上传区域
              _buildPhotoSection(),
              // 备注区域
              _buildRemarkSection(),
            ],
          );
        });
  }

  /// 照片上传区域
  Widget _buildPhotoSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '照片：',
                style: TextStyle(fontSize: 17),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (widget.isShowRemind) ...[
                    GestureDetector(
                      onTap: () {
                        // TODO: 律师提醒弹窗
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.w, vertical: 4.h),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(2.r),
                          border: Border.all(
                            color: const Color(0xFF5290FD),
                            width: 1.w,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.play_circle_outline,
                              size: 14.sp,
                              color: const Color(0xFF5290FD),
                            ),
                            SizedBox(width: 4.w),
                            Text(
                              '律师提醒',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: const Color(0xFF5290FD),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Text(
                      '上传工作照片，留证据，工资有保障',
                      style: TextStyle(fontSize: 12, color: Colors.blue),
                    ),
                    SizedBox(
                      height: 8,
                    ),
                  ],
                  ImagePicker(
                    maxImages: 9,
                    onImagesUploaded: (urls) {
                      // 存储已上传图片的URL列表
                      // _uploadedImageUrls.value = urls;
                      vm.addPhotos(urls);
                    },
                    imageUrls: vm.controller.value.photoUrls,
                  )
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建备注区域
  Widget _buildRemarkSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.w),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '备注：',
            style: TextStyle(fontSize: 17),
          ),
          Expanded(
            child: GestureDetector(
              onTap: () {
                YPRoute.openPage(RouteNameCollection.notes,
                        params: NotesProps(
                            pageSource: NotesPageSource.groupEditRecordWork,
                            value: vm.controller.value.remark))
                    ?.then((res) {
                  if (res != null) {
                    vm.updateRemark(res as String);
                  }
                });
              },
              child: Container(
                height: 200,
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: ColorsUtil.inputBgColor,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  (vm.controller.value.remark) == ''
                      ? '请输入备注内容...'
                      : vm.controller.value.remark,
                  style: TextStyle(
                    color: vm.controller.value.remark == ''
                        ? ColorsUtil.hintFontColor
                        : ColorsUtil.black85,
                  ),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
