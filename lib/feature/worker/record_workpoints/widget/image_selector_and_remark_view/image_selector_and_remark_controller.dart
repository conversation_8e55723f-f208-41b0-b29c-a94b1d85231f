import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/image_selector_and_remark_view/vm/image_selector_and_remark_model.dart';

class ImageSelectorAndRemarkController
    extends ValueNotifier<ImageSelectorAndRemarkModel> {
  ImageSelectorAndRemarkController()
      : super(ImageSelectorAndRemarkModel(
          remark: '',
          photoUrls: [],
        ));

  // ImageSelectorAndRemarkModel get model => value;

  updateRemark(String remark) {
    value.remark = remark;
    notifyListeners();
  }

  addPhoto(String photoUrl) {
    value.photoUrls.add(photoUrl);
    notifyListeners();
  }

  addPhotos(List<String> photoUrl) {
    value.photoUrls.addAll(photoUrl);
    notifyListeners();
  }

  removePhoto(int index) {
    if (index >= 0 && index < value.photoUrls.length) {
      value.photoUrls.removeAt(index);
    }
    notifyListeners();
  }
}
