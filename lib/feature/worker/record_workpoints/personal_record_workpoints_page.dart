
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/add_record_work_param_model.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/entity/personal_record_workpoints_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/vm/personal_record_workpoints_vm.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/daily_wages_view/daily_wages_controller.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/daily_wages_view/daily_wages_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/image_selector_and_remark_view/image_selector_and_remark_controller.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/other_expense_view/other_expense_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/select_record_date_view.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/custom_tab_indicator.dart';
import 'package:gdjg_pure_flutter/widget/expense_view/expense_controller.dart';
import 'package:gdjg_pure_flutter/widget/expense_view/expense_view.dart';
import 'package:gdjg_pure_flutter/widget/point_work/point_work_controller.dart';
import 'package:gdjg_pure_flutter/widget/point_work/point_work_view.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_controller.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_view.dart';
import 'package:get/get.dart';

class PersonalRecordWorkPoints extends BaseFulPage {
  const PersonalRecordWorkPoints({super.key}): super(appBar: const YPAppBar(title: "个人记工"));

  @override
  State createState() => _PersonalRecordWorkPoints();
}
class _PersonalRecordWorkPoints extends BaseFulPageState  with SingleTickerProviderStateMixin {

  final vm = PersonalRecordWorkPointsVM();

  late TabController _tabController;
  @override
  void initState() {
    super.initState();
  }

  @override
  void onPageCreate() {
    super.onPageCreate();
    _tabController = TabController(length: 5, vsync: this);
    _tabController.addListener(() {
      // setState(() {});
      // ToastUtil.showToast('当前选中的 Tab 索引：${_tabController.index}');
    });

  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    var  props = routeParams as PersonalRecordWorkpointsProps?;
    vm.initProject(props?.noteId, props?.noteName);
    vm.initDate(props?.date);
  }

  @override
  void dispose() {
    _tabController.removeListener(() {});
    _tabController.dispose();
    super.dispose();
  }

  /// 项目选择
  Widget _buildProjectView(){
    return GestureDetector(
      onTap: () {
        yprint('******************');
        vm.changeProject();
      },
      child: Container(
        width: double.infinity,
        height: 45,
        color: Colors.transparent,
        child:
        Center(
          child: Row(
            children: [
              Text('项目：',style: TextStyle(fontSize: 17,color: Color(0xFF323233))),
              Obx(()=>
                  Text(vm.us.project?.name ?? '请选择项目',style: TextStyle(fontSize: 17,color: Color(0xFF323233), fontWeight: FontWeight.w500))
              ),
              Spacer(),
              SizedBox(width: 4),
              Image.asset(Assets.commonIconArrowRightGrey,width: 18,height: 18)
            ],
          ),
        ),
      ),
    );
  }


  /// 项目选择
  Widget _buildDateView(){
    return GestureDetector(
      onTap: () {
        showSelectRecordDate();
      },
      child: Container(
        width: double.infinity,
        color: Colors.transparent,
        height: 45,
        child:
        Center(
          child: Row(
            children: [
              Text('日期：',style: TextStyle(fontSize: 17,color: Color(0xFF323233))),
              Obx(()=>
                  Text(vm.us.getSelectDateString(),style: TextStyle(fontSize: 17,color: Color(0xFF323233), fontWeight: FontWeight.w500))
              ),
              Spacer(),
              Text('可多选',style: TextStyle(fontSize: 14,color: Color(0xFF323233))),
              SizedBox(width: 4),
              Image.asset(Assets.commonIconArrowRightGrey,width: 18,height: 18)
            ],
          ),
        ),
      ),
    );
  }


  /// 选择记工日期
  void showSelectRecordDate() {

    // var dates = <DateTime>[];
    // dates.add(DateTime.now());

    YPRoute.openDialog(
      builder: (context) => SelectRecordDateView(
        note_id: vm.us.project?.id.toString() ?? '',
        isRecordWorkPoints: true,
        dateList: vm.us.selectDates,
        isChangeChoice: false,
        isMultiple: true,
        onSelect: (dateList) {
          vm.setSelectList(dateList);
        },),
      alignment: Alignment.bottomCenter,
    );
  }


  final _pointWorkController = PointWorkController();
  final _packageWorkController = PointWorkController();
  final _dailyWagesController = DailyWagesController();
  final _workLoadController = WorkLoadController();
  final _expenseController = ExpenseController();
  final  _pointWorkPhotoRemarkController = ImageSelectorAndRemarkController();
  final  _packagePhotoRemarkController = ImageSelectorAndRemarkController();

  /// 点工组件
  Widget _buildTabPageView(RwaRecordType businessType,Widget childView) {

    return Container(
      width: double.infinity,
      child: Column(
        children: [
          Flexible(
            flex: 1,
              child: Container(
                height: double.infinity,
                child:SingleChildScrollView(
                  child:  childView,
                ),
              )
          ),
          Divider(thickness: 1,color: Color(0xFFF5F5F5)),

          GestureDetector(
            onTap: () {
              _recordWork(businessType);
            },
            child: Container(
              margin: EdgeInsets.only(left: 16,right: 16,top: 8,bottom: 24),
              height: 44.h,
              decoration: BoxDecoration(
                  color: ColorsUtil.primaryColor,
                  borderRadius: BorderRadius.all( Radius.circular(4.w))
              ),
              child: Center(
                child: Text('确认',style: TextStyle(fontSize: 16.sp,fontWeight: FontWeight.w500, color: Colors.white)),
              ),
            ),
          )

        ],
      ),
    );
  }

  _recordWork(RwaRecordType businessType){
    var param = BusinessAddMyselfAParamModel();
    if(businessType == RwaRecordType.workDays){
      param = _pointWorkController.getRecordParam();
    }else if(businessType == RwaRecordType.packageWork){
      param = _packageWorkController.getRecordParam();
    }else if(businessType == RwaRecordType.dailyWages){
      param = _dailyWagesController.getRecordParam();
      if ((param.money ?? '').isEmpty || double.parse(param.money ?? '0') == 0) {
        showCommonDialog(CommonDialogConfig(
          title: '提示?',
          content: '当前金额为0元，确定要记为短工吗？',
          negative: '取消',
          positive: '确认',
          onPositive: () {
            param.money = '0';
            vm.recordPointWork(businessType, param);
          },
        ));
        return;
      }
    }else if(businessType == RwaRecordType.workLoad) {
      param = _workLoadController.getRecordParam();
      if(param.unit_work_type == null || (param.unit_work_type?.isEmpty ?? true)){
        ToastUtil.showToast('请添加分项');
        return;
      }
      if(param.unit_num == null || (param.unit_num?.isEmpty ?? true)){
        ToastUtil.showToast('请填写工程量');
        return;
      }
      if(param.unit_price == null || (param.unit_price?.isEmpty ?? true)){
        ToastUtil.showToast('请填写单价');
        return;
      }
      if(param.money == null || (param.money?.isEmpty ?? true)){
        ToastUtil.showToast('请填写工钱');
        return;
      }
      if (double.parse(param.unit_num ?? '0') == 0) {
        showCommonDialog(CommonDialogConfig(
          title: '提示?',
          content: '当前工程量为0，确定要记为工量吗？',
          negative: '取消',
          positive: '确认',
          onPositive: () {
            param.unit_num = '0';
            vm.recordPointWork(businessType, param);
          },
        ));
        return;
      }
    }else if(businessType == RwaRecordType.expense) {
      param = _expenseController.getRecordParam();
      if(param.other_expenses_id == null || param.other_expenses_id == ''){
        ToastUtil.showToast('请填写费用名称');
        return;
      }
      if(param.money == null || param.money == '' || double.parse(param.money ?? '0') == 0 ){
        ToastUtil.showToast('请填写金额');
        return;
      }
    }
      vm.recordPointWork(businessType,param);
  }

  @override
  Widget yBuild(BuildContext context) {

    return Container(
      width: double.infinity,
      height: double.infinity,
        color: Colors.white,
      child:Column(
        children: [
          Divider(thickness: 8,color: Color(0xFFF0F0F0),),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16),
            width: double.infinity,
            child: Column(
                children: [
                  _buildDateView(),
                  Divider(thickness: 1,color: Color(0xFFF5F5F5),),
                  _buildProjectView(),

                ]
            ),
          ),
          Divider(thickness: 8,color: Color(0xFFF0F0F0),),

          SizedBox(
            height: 32,
            child: TabBar(
                isScrollable: true,
                labelColor: Color.fromRGBO(0, 0, 0, 0.85),
                unselectedLabelColor: Color.fromRGBO(0, 0, 0, 0.65),
                labelStyle: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
                labelPadding: EdgeInsets.symmetric(horizontal: 14),
                unselectedLabelStyle: const TextStyle(fontSize: 18),
                tabAlignment: TabAlignment.start,
                // indicator: BoxDecoration(
                //   color: Colors.blue,
                //   borderRadius: BorderRadius.circular(16),
                // ),
                indicatorColor: Colors.blue,
                indicatorWeight: 3,
                // indicatorSize: TabBarIndicatorSize.tab,
                indicator: CustomTabIndicator(
                    borderSide: BorderSide(color: Colors.blue, width: 3)),
                dividerColor: Colors.transparent,
                controller: _tabController,
                tabs: [
                  Tab(text: '点工'),
                  Tab(text: '包工'),
                  Tab(text: '短工'),
                  Tab(text: '工量'),
                  Tab(text: '其他费用'),
                ]),
          ),
          Flexible(
            flex: 1,
            child: Container(
              // color: Colors.red,
              child: TabBarView(
                  controller: _tabController,
                  // physics: NeverScrollableScrollPhysics(),
                  children: [
                    // 点工
                  _buildTabPageView(
                      RwaRecordType.workDays,
                      Obx(() => PointWorkView(
                            controller: _pointWorkController,
                            imageSelectorAndRemarkController: _pointWorkPhotoRemarkController,
                            noteId: vm.us.project?.id.toString() ?? '',
                            workNoteName: vm.us.project?.name ?? '',
                            businessType: RwaRecordType.workDays,
                            recordNoteType: RecordNoteType.personal,
                            isModify: false,
                            worker: WorkerModel(workerId: 3514, workerName: ''),
                          feeStandardModel:vm.us.projectInfo?.feeStandard,
                          ))),
                    // 包工
                    _buildTabPageView(
                        RwaRecordType.packageWork,
                        Obx(() => PointWorkView(
                          controller: _packageWorkController,
                          imageSelectorAndRemarkController: _packagePhotoRemarkController,
                          noteId: vm.us.project?.id.toString() ?? '',
                          workNoteName: vm.us.project?.name ?? '',
                          businessType: RwaRecordType.packageWork,
                          recordNoteType: RecordNoteType.personal,
                          isModify: false,
                          worker: WorkerModel(workerId: 3514, workerName: ''),
                          feeStandardModel:vm.us.projectInfo?.contractorFeeStandard,
                        ))),
                    // 短工
                    _buildTabPageView(
                        RwaRecordType.dailyWages,
                        DailyWagesView(controller: _dailyWagesController)
                        ),
                    // 工量
                    _buildTabPageView(
                          RwaRecordType.workLoad,
                          Obx(()=>
                              WorkLoadView(
                                controller: _workLoadController,
                                noteId: vm.us.project?.id.toString() ?? '',
                              ))),
                    // 其他费用
                    _buildTabPageView(
                          RwaRecordType.expense,
                          Obx(()=>
                              ExpenseView(
                                controller: _expenseController,
                                noteId: vm.us.project?.id.toString() ?? '',
                              ))),
                  ]
              ),
            ),
          ),
        ],
      )


    );
  }





}