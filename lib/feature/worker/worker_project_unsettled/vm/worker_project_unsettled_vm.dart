import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_index_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/repo/business_get_index_business_count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/repo/worker_flow_repo.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';

class WorkerProjectUnsettledUS {
  final Rx<ProjectGetProjectBizModel?> project = Rx<ProjectGetProjectBizModel?>(null);
  final Rx<BusinessCountBizModel?> summary = Rx<BusinessCountBizModel?>(null);

  void setProject(ProjectGetProjectBizModel data) => project.value = data;

  void setSummary(BusinessCountBizModel data) => summary.value = data;
}

class WorkerProjectUnsettledVM extends GetxController {
  final WorkerProjectUnsettledUS us = WorkerProjectUnsettledUS();
  final WorkerProjectRepo _projectRepo = WorkerProjectRepo();
  final WorkerFlowRepo _flowRepo = WorkerFlowRepo();

  String? _projectId;

  void init(String projectId) {
    _projectId = projectId;
    fetchData();
  }

  void fetchData() async {
    if (_projectId == null || _projectId!.isEmpty) {
      return;
    }

    // 先获取项目详情
    final projectResult = await _fetchProjectDetail();
    if (projectResult != null) {
      us.setProject(projectResult);

      // 使用项目的时间范围获取业务统计
      final summaryResult = await _fetchBusinessCount(
        startTime: projectResult.startTime,
        endTime: projectResult.endTime,
      );

      if (summaryResult != null) {
        us.setSummary(summaryResult);
      }
    } else {
      // 如果项目详情获取失败，使用默认时间获取业务统计
      final summaryResult = await _fetchBusinessCount();
      if (summaryResult != null) {
        us.setSummary(summaryResult);
      }
    }
  }

  Future<ProjectGetProjectBizModel?> _fetchProjectDetail() async {
    final result = await _projectRepo.getProject(_projectId!);
    if (result.isOK()) {
      return result.getSucData();
    }
    return null;
  }

  Future<BusinessCountBizModel?> _fetchBusinessCount({
    String? startTime,
    String? endTime,
  }) async {
    String startTimeStr;
    String endTimeStr;

    if (startTime != null && startTime.isNotEmpty &&
        endTime != null && endTime.isNotEmpty) {
      final projectStartTime = DateUtil.parseDate(startTime);
      final projectEndTime = DateUtil.parseDate(endTime);

      if (projectStartTime != null && projectEndTime != null) {
        startTimeStr = DateUtil.formatDate(projectStartTime);
        endTimeStr = DateUtil.formatDate(projectEndTime);
      } else {
        final now = DateTime.now();
        final startOfMonth = DateTime(now.year, now.month, 1);
        final endOfMonth = DateTime(now.year, now.month + 1, 0);
        startTimeStr = DateUtil.formatDate(startOfMonth);
        endTimeStr = DateUtil.formatDate(endOfMonth);
      }
    } else {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);
      startTimeStr = DateUtil.formatDate(startOfMonth);
      endTimeStr = DateUtil.formatDate(endOfMonth);
    }

    final param = BusinessCountParamModel(
      identity: "2",
      start_time: startTimeStr,
      end_time: endTimeStr,
      status: "0",
    );

    final result = await _flowRepo.fetchHeaderData(param);
    if (result.isOK()) {
      return result.getSucData();
    }
    return null;
  }
}
