import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/view/base_section_view.dart';

/// 其他费用显示组件
class OtherExpenseSectionView extends StatelessWidget {
  /// 费用名称
  final String name;
  
  /// 费用金额
  final String money;
  
  /// 记录笔数
  final int num;
  
  /// 点击事件
  final VoidCallback? onTap;

  const OtherExpenseSectionView({
    super.key,
    required this.name,
    required this.money,
    required this.num,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return BaseSectionView(
      leftContent: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '其他费用 $name',
            style: TextStyle(fontSize: 16.sp, color: Colors.black),
          ),
          if (num > 0) ...[
            SizedBox(height: 2.h),
            Text(
              '$num笔',
              style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
            ),
          ],
        ],
      ),
      amount: money,
      onTap: onTap,
    );
  }


}
