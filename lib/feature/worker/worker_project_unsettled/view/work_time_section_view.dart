import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/view/base_section_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

/// 工时显示组件（点工/包工通用）
class WorkTimeSectionView extends StatelessWidget {
  /// 工作类型（"点工" 或 "包工"）
  final String workType;
  
  /// 工时数量
  final double workTime;
  
  /// 小时数量
  final double workTimeHour;
  
  /// 金额
  final double money;
  
  /// 点击事件
  final VoidCallback? onTap;

  const WorkTimeSectionView({
    super.key,
    required this.workType,
    required this.workTime,
    required this.workTimeHour,
    required this.money,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return BaseSectionView(
      leftContent: Text(
        _buildWorkTimeText(),
        style: TextStyle(fontSize: 16.sp, color: Colors.black),
      ),
      amount: money,
      onTap: onTap,
    );
  }

  /// 构建工时显示文本
  String _buildWorkTimeText() {
    String workTimeText = '$workType 上班:';
    if (workTime > 0) {
      workTimeText += '${workTime.trimTrailingZeros()}个工';
      if (workTimeHour > 0) {
        workTimeText += '+${workTimeHour.trimTrailingZeros()}小时';
      }
    } else if (workTimeHour > 0) {
      workTimeText += '${workTimeHour.trimTrailingZeros()}小时';
    } else {
      workTimeText += '0工';
    }
    return workTimeText;
  }
}
