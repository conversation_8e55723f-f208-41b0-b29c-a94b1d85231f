import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/view/base_section_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';

/// 工量显示组件
class UnitWorkSectionView extends StatelessWidget {
  /// 分项名称
  final String unitWorkTypeName;

  /// 工程量
  final String count;

  /// 工钱
  final String unitMoney;

  /// 记工笔数
  final double num;

  /// 分项单位
  final String unitWorkTypeUnit;

  /// 点击事件
  final VoidCallback? onTap;

  const UnitWorkSectionView({
    super.key,
    required this.unitWorkTypeName,
    required this.count,
    required this.unitMoney,
    required this.num,
    required this.unitWorkTypeUnit,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return BaseSectionView(
      leftContent: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _buildUnitWorkText(),
            style: TextStyle(fontSize: 16.sp, color: Colors.black),
          ),
          if (num > 0) ...[
            SizedBox(height: 2.h),
            Text(
              '${num.toInt()}笔',
              style: TextStyle(fontSize: 14.sp, color: Colors.grey[600]),
            ),
          ],
        ],
      ),
      amount: unitMoney,
      rightContent: _buildRightContent(),
      onTap: onTap,
    );
  }

  /// 工量
  String _buildUnitWorkText() {
    String workText = '工量';
    if (unitWorkTypeName.isNotEmpty) {
      workText += ' $unitWorkTypeName';
    }
    return workText;
  }

  /// 总计
  String _buildTotalText() {
    String totalText = '总计：$count';
    if (unitWorkTypeUnit.isNotEmpty) {
      totalText += unitWorkTypeUnit;
    }
    return totalText;
  }

  /// 构建右侧内容（金额和总计）
  Widget _buildRightContent() {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                unitMoney.formatStringToMoney(),
                style: TextStyle(
                  fontSize: 24.sp,
                  color: ColorsUtil.primaryColor,
                  fontFamily: FontUtil.fontCondMedium,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                _buildTotalText(),
                style: TextStyle(fontSize: 14.sp, color: Colors.black),
              ),
            ],
          ),
          SizedBox(width: 4.w),
          Image.asset(
            Assets.commonIconArrowRightGrey,
            width: 18.w,
            height: 18.h,
          ),
        ],
      ),
    );
  }


}
