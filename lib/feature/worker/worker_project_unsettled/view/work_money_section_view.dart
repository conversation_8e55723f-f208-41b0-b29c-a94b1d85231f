import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/view/base_section_view.dart';

/// 短工显示组件
class WorkMoneySectionView extends StatelessWidget {
  /// 短工金额
  final double workMoney;

  /// 记录笔数
  final double num;

  /// 点击事件
  final VoidCallback? onTap;

  const WorkMoneySectionView({
    super.key,
    required this.workMoney,
    required this.num,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return BaseSectionView(
      leftContent: Text(
        _buildWorkMoneyText(),
        style: TextStyle(fontSize: 16.sp, color: Colors.black),
      ),
      amount: workMoney,
      onTap: onTap,
    );
  }

  /// 短工文本
  String _buildWorkMoneyText() {
    String workText = '短工';
    if (num > 0) {
      workText += ' ${num.toInt()}笔';
    }
    return workText;
  }


}
