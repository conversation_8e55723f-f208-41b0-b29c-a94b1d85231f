import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';

/// 基础section显示组件
/// 提供统一的容器样式和右侧金额显示
class BaseSectionView extends StatelessWidget {
  /// 左侧内容widget
  final Widget leftContent;

  /// 金额
  final dynamic amount;

  /// 右侧自定义内容widget
  final Widget? rightContent;

  /// 点击事件
  final VoidCallback? onTap;

  const BaseSectionView({
    super.key,
    required this.leftContent,
    required this.amount,
    this.rightContent,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 6.w),
      decoration: const BoxDecoration(
        color: Color(0xFFF3F9FF),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          leftContent,
          rightContent ?? _buildRightContent(),
        ],
      ),
    );
  }

  /// 金额和箭头
  Widget _buildRightContent() {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _formatAmount(),
            style: TextStyle(
              fontSize: 24.sp,
              color: ColorsUtil.primaryColor,
              fontFamily: FontUtil.fontCondMedium,
            ),
          ),
          SizedBox(width: 4.w),
          Image.asset(
            Assets.commonIconArrowRightGrey,
            width: 18.w,
            height: 18.h,
          ),
        ],
      ),
    );
  }

  /// 格式化金额
  String _formatAmount() {
    if (amount == null) return "0.00";
    
    if (amount is String) {
      return (amount as String?).formatStringToMoney();
    } else if (amount is double) {
      return (amount as double?).formatDoubleToMoney();
    } else if (amount is int) {
      return (amount as int).toDouble().formatDoubleToMoney();
    }
    
    return "0.00";
  }
}
