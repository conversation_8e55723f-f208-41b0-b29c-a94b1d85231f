import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/WageRulesSettingDialog.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/entity/wage_rules_props.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 工价提示组件（点工/包工通用）
class WorkPriceHintView extends StatelessWidget {
  /// 工作类型（"点工" 或 "包工"）
  final String workType;

  /// 项目ID
  final String? projectId;

  /// 工人ID
  final double? workerId;

  /// 设置完成后的回调
  final VoidCallback? onSettingComplete;

  const WorkPriceHintView({
    super.key,
    required this.workType,
    this.projectId,
    this.workerId,
    this.onSettingComplete,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              Assets.workerIconDeleteWarning,
              width: 16.w,
              height: 16.h,
              color: ColorsUtil.primaryColor,
            ),
            SizedBox(width: 4.w),
            Text(
              '未设$workType工价',
              style: TextStyle(fontSize: 14.sp, color: Colors.black),
            ),
          ],
        ),
        const Spacer(),
        TextButton(
          onPressed: _onSettingTap,
          style: TextButton.styleFrom(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
          ),
          child: Text(
            '去设置',
            style: TextStyle(color: ColorsUtil.primaryColor, fontSize: 14.sp),
          ),
        ),
      ],
    );
  }

  /// 点击设置按钮
  void _onSettingTap() {
    if (workType == '点工') {
      _onPointWorkWageTap();
    } else if (workType == '包工') {
      _onContractWorkWageTap();
    } else {
      ToastUtil.showToast('未知工作类型');
    }
  }

  /// 点击点工工价设置
  void _onPointWorkWageTap() {
    if (projectId == null || projectId!.isEmpty) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    if (workerId == null) {
      ToastUtil.showToast('工人信息错误');
      return;
    }

    var props = WageRulesProps(
      title: '点工工价设置',
      workNoteId: projectId!,
      businessType: RwaRecordType.workDays,
      recordNoteType: RecordNoteType.group,
      confirmRequestApi: true,
      workers: [
        WorkerModel(
          workerId: workerId,
          workerName: ''
        )
      ],
    );
    WageRulesSettingDialog.show(props: props, onSelected: (feeInfo){
      onSettingComplete?.call();
    });
  }

  /// 点击包工工价设置
  void _onContractWorkWageTap() {
    if (projectId == null || projectId!.isEmpty) {
      ToastUtil.showToast('项目信息错误');
      return;
    }

    if (workerId == null) {
      ToastUtil.showToast('工人信息错误');
      return;
    }

    var props = WageRulesProps(
      title: '包工工价设置',
      workNoteId: projectId!,
      businessType: RwaRecordType.packageWork,
      recordNoteType: RecordNoteType.group,
      confirmRequestApi: true,
      workers: [
        WorkerModel(
          workerId: workerId,
          workerName: '',
        )
      ],
    );
    WageRulesSettingDialog.show(props: props, onSelected: (feeInfo){
      onSettingComplete?.call();
    });
  }
}
