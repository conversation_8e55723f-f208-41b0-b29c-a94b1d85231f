import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/repo/business_get_index_business_count_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/data/account/repo/auth_repo.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/choose_project_filter/entity/choose_project_filter_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/crosscheck/page_args/worker_worklog_flow_details_page_args.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/vm/worker_project_unsettled_vm.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/view/work_time_section_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/view/work_price_hint_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/view/unit_work_section_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/view/other_expense_section_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/view/work_money_section_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/view/list_item_un_liquidated_wages_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/vm/protocol/group_liquidated_group_business_count_ui_state.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

class WorkerProjectUnsettledPageArgs {
  final String projectId;
  final String? projectName;

  const WorkerProjectUnsettledPageArgs({required this.projectId, this.projectName});
}

class WorkerProjectUnsettledPage extends BaseFulPage {
  const WorkerProjectUnsettledPage({super.key}) : super(appBar: const YPAppBar(title: "项目详情"));

  @override
  State<StatefulWidget> createState() => _WorkerProjectUnsettledPageState();
}

class _WorkerProjectUnsettledPageState
    extends BaseFulPageState<WorkerProjectUnsettledPage> {
  final WorkerProjectUnsettledVM _vm = Get.put(WorkerProjectUnsettledVM());
  final AuthRepo _authRepo = AuthRepo();
  WorkerProjectUnsettledPageArgs? _args;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    _args = _parseArgs(routeParams);
    final projectId = _args?.projectId;
    if (projectId != null && projectId.isNotEmpty) {
      _vm.init(projectId);
      // 动态设置标题
      if (_args?.projectName != null && _args!.projectName!.isNotEmpty) {
        dynamicTitle = _args!.projectName;
      }
      // 监听项目数据变化，动态更新标题
      _vm.us.project.listen((project) {
        if (project != null && project.name.isNotEmpty) {
          setState(() {
            dynamicTitle = project.name;
          });
        }
      });
    }
  }

  @override
  void onPageDestroy() {
    Get.delete<WorkerProjectUnsettledVM>();
    super.onPageDestroy();
  }

  WorkerProjectUnsettledPageArgs? _parseArgs(Object? routeParams) {
    if (routeParams is WorkerProjectUnsettledPageArgs) {
      return routeParams;
    }
    return null;
  }

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 8.h),
      child: Obx(() {
        final project = _vm.us.project.value;
        if (project == null) {
          return const SizedBox.shrink();
        }
        final summary = _vm.us.summary.value;
        return Column(
          children: [
            Expanded(child: _buildContent(project, summary)),
            _buildBottomBar(project),
          ],
        );
      }),
    );
  }

  Widget _buildContent(ProjectGetProjectBizModel project, BusinessCountBizModel? summary) {
    final spotWork = summary?.spotWork;
    final contractor = summary?.contractor;
    final unit = summary?.unit;
    final workMoney = summary?.workMoney;

    return ListView(
      padding: EdgeInsets.fromLTRB(10.w, 0, 10.w, 0),
      children: [
        _buildDateSection(project),
        SizedBox(height: 8.h),
        //点工section
        if ((spotWork?.num ?? 0) > 0) ...[
          WorkTimeSectionView(
            workType: "点工",
            workTime: spotWork?.num ?? 0,
            workTimeHour: double.tryParse(spotWork?.workTimeHour ?? "0") ?? 0,
            money: spotWork?.spotWorkFeeMoney ?? 0,
            onTap: () => ToastUtil.showToast('未实现'),
          ),
          if (project.feeStandard?.hasFeeStandard() != true)
            WorkPriceHintView(
              workType: "点工",
              projectId: _args?.projectId,
              workerId: double.tryParse(_authRepo.getAccount().uid),
              onSettingComplete: () => _vm.fetchData(),
            ),
          SizedBox(height: 4.h),
        ],
        // 包工section
        if ((contractor?.contractorWorkTime ?? 0) > 0) ...[
          WorkTimeSectionView(
            workType: "包工",
            workTime: contractor?.contractorWorkTime ?? 0,
            workTimeHour: contractor?.contractorWorkTimeHour ?? 0,
            money: double.tryParse(contractor?.contractorMoney ?? "0") ?? 0,
            onTap: () => ToastUtil.showToast('未实现'),
          ),
          if (project.contractorFeeStandard?.hasFeeStandard() != true)
            WorkPriceHintView(
              workType: "包工",
              projectId: _args?.projectId,
              workerId: double.tryParse(_authRepo.getAccount().uid),
              onSettingComplete: () => _vm.fetchData(),
            ),
          SizedBox(height: 4.h),
        ],
        // 工量section
        if ((unit?.num ?? 0) > 0) ...[
          ...unit!.countUnit.map((unitItem) => Padding(
            padding: EdgeInsets.only(bottom: 4.h),
            child: UnitWorkSectionView(
              unitWorkTypeName: unitItem.unitWorkTypeName,
              count: unitItem.count,
              unitMoney: unitItem.unitMoney,
              num: unitItem.num,
              unitWorkTypeUnit: unitItem.unitWorkTypeUnit,
              onTap: () => ToastUtil.showToast('未实现'),
            ),
          )),
        ],
        // 其他费用section
        if ((summary?.otherExpenses ?? []).isNotEmpty) ...[
          ...summary!.otherExpenses.map((expenseItem) => Padding(
            padding: EdgeInsets.only(bottom: 4.h),
            child: OtherExpenseSectionView(
              name: expenseItem.name,
              money: expenseItem.money,
              num: expenseItem.num,
              onTap: () => ToastUtil.showToast('未实现'),
            ),
          )),
        ],
        // 短工section
        if ((workMoney?.num ?? 0) > 0) ...[
          WorkMoneySectionView(
            workMoney: workMoney?.workMoney ?? 0,
            num: workMoney?.num ?? 0,
            onTap: () => ToastUtil.showToast('未实现'),
          ),
        ],
        _buildUnsettledSection(summary),
      ],
    );
  }

  Widget _buildDateSection(ProjectGetProjectBizModel project) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 8.h),
          child: Text(
            _formatDateRange(project),
            style: TextStyle(fontSize: 18.sp, color: Colors.black, fontWeight: FontWeight.w600),
          ),
        ),
        Container(
          height: 0.5.h,
          color: const Color(0xFFE5E5E5),
        ),
      ],
    );
  }

  Widget _buildUnsettledSection(BusinessCountBizModel? summary) {
    final unsettled = summary?.unsettled ?? 0;
    final uiState = GroupLiquidatedGroupBusinessCountListUiState(
      money: _formatAmount(unsettled),
    );

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 24.h),
      child: ListItemUnLiquidatedDetailWages(
        haveBillList: false,
        uiState: uiState,
      ),
    );
  }

  Widget _buildBottomBar(ProjectGetProjectBizModel project) {
    return SafeArea(
      top: false,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 4.h),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            top: BorderSide(color: const Color(0xFFE5E5E5), width: 0.4.h),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: OutlinedButton(
                onPressed: () => _openFlowDetails(project),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: ColorsUtil.primaryColor, width: 1.w),
                  foregroundColor: ColorsUtil.primaryColor,
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                child: Text('对工', style: TextStyle(fontSize: 18.sp)),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: () {
                  ToastUtil.showToast('记结算功能开发中');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorsUtil.primaryColor,
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
                child: Text(
                  '记结算',
                  style: TextStyle(color: Colors.white, fontSize: 18.sp),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openFlowDetails(ProjectGetProjectBizModel project) {
    final start = DateUtil.parseDate(project.startTime);
    final end = DateUtil.parseDate(project.endTime);
    YPRoute.openPage(
      RouteNameCollection.workerWorklogFlowDetails,
      params: WorkerWorklogFlowDetailsPageArgs(
        startTime: start,
        endTime: end,
        projects: [
          ChooseProjectFilterItem(
            workNoteName: project.name,
            workNoteId: project.id.trimTrailingZeros(),
            isChecked: true,
            isGroup: false,
          ),
        ],
      ),
    );
  }

  String _formatDateRange(ProjectGetProjectBizModel project) {
    final start = DateUtil.parseDate(project.startTime);
    final end = DateUtil.parseDate(project.endTime);
    final startStr = _formatDateToChineseStyle(start);
    final endStr = _formatDateToChineseStyle(end);
    return '$startStr-$endStr';
  }

  String _formatDateToChineseStyle(DateTime? date) {
    if (date == null) return '';
    return '${date.year}年${date.month.toString().padLeft(2, '0')}月${date.day.toString().padLeft(2, '0')}日';
  }

  String _formatAmount(double amount) {
    return amount.toStringAsFixed(2);
  }
}
