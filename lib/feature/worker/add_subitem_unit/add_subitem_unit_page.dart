import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/add_subitem_unit/vm/add_subitem_unit_vm.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';

class AddSubitemUnitPage extends BaseFulPage {
  const AddSubitemUnitPage({Key? key})
      : super(appBar: const YPAppBar(title: "添加分项单位"));

  @override
  State createState() => _AddSubitemUnitPageState();
}

class _AddSubitemUnitPageState extends BaseFulPageState {
  final TextEditingController _unitController = TextEditingController();
  String _selectedUnit = '';
  final vm = AddSubitemUnitVM();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    // 设置右侧确定按钮
    dynamicRightResText = '确定';
    dynamicRightResTap = () {
      _confirmUnit();
    };

    vm.initUnitList();
  }

  @override
  void dispose() {
    _unitController.dispose();
    super.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      color: const Color(0xFFF5F5F5),
      child: Column(
        children: [
          // 单位输入区域
          Container(
            color: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            child: Row(
              children: [
                const Text(
                  '单位',
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF323232),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    controller: _unitController,
                    maxLength: 4,
                    // 限制最多4个字符
                    decoration: const InputDecoration(
                      hintText: '请输入单位名称(不超过四个汉字)',
                      hintStyle: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF999999),
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                      counterText: '', // 隐藏字符计数器
                    ),
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF323232),
                    ),
                    onChanged: (value) {
                      _selectedUnit = value;
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // 快速选择单位区域
          Expanded(
            child: Container(
              color: Colors.white,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '快速选择单位',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF323232),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Obx(() => Expanded(
                        child: _buildUnitGrid(),
                      )),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单位选择网格
  Widget _buildUnitGrid() {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 2.5,
      ),
      itemCount: vm.uiState.value.unitList.length,
      itemBuilder: (context, index) {
        final unit = vm.uiState.value.unitList[index].name;
        final isSelected = _selectedUnit == unit;

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedUnit = unit;
              _unitController.text = unit;
            });
          },
          child: Container(
            decoration: BoxDecoration(
              color: isSelected
                  ? const Color(0xFF2F6BFF).withOpacity(0.1)
                  : const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: isSelected
                    ? const Color(0xFF2F6BFF)
                    : const Color(0xFFE5E5E5),
                width: 1,
              ),
            ),
            child: Center(
              child: Text(
                unit,
                style: TextStyle(
                  fontSize: 14,
                  color: isSelected
                      ? const Color(0xFF2F6BFF)
                      : const Color(0xFF666666),
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 确认选择单位
  void _confirmUnit() {
    if (_unitController.text.trim().isEmpty) {
      Get.snackbar('提示', '请输入或选择单位');
      return;
    }

    // 返回选择的单位
    YPRoute.closePage(_unitController.text.trim());
  }
}
