import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem_choose/vm/subitem_choose_vm.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';

class SubitemChoosePage extends BaseFulPage {
  const SubitemChoosePage({Key? key})
      : super(appBar: const YPAppBar(title: "分项选择"));

  @override
  State createState() => _SubitemChoosePageState();
}

class _SubitemChoosePageState extends BaseFulPageState {
  final vm = SubitemChooseVm();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    var props = routeParams as String?;
    if (props != null) {
      vm.initSubitemList(workNoteId: props);
    } else {
      vm.initSubitemList();
    }
  }

  @override
  Widget yBuild(BuildContext context) {
    return Column(
      children: [
        const SizedBox(height: 8),
        Obx(() => Expanded(
              child: Container(
                color: Colors.white,
                child: vm.uiState.value.subitemList.isNotEmpty == true
                    ? ListView.builder(
                        itemBuilder: (context, index) {
                          return _buildSelectionItem(context, index);
                        },
                        itemCount: vm.uiState.value.subitemList.length,
                      )
                    : Container(),
              ),
            )),
        SafeArea(
          top: false,
          child: Container(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
            color: Colors.white,
            child: SizedBox(
              width: double.infinity,
              height: 44,
              child: ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.addSubitem);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2F6BFF),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('添加分项', style: TextStyle(fontSize: 16)),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建选择项
  Widget _buildSelectionItem(BuildContext context, int index) {
    final itemData = vm.uiState.value.subitemList[index];
    return InkWell(
      onTap: () => {},
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16),
        margin: EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1),
          ),
        ),
        child: Row(
          children: [
            Text(
              itemData.name ?? '',
              style: TextStyle(fontSize: 19, color: Color(0xFF323232)),
            ),
            Spacer(),
            GestureDetector(
              onTap: () {
              },
              child: Text('...'),
            )
          ],
        ),
      ),
    );
  }
}
