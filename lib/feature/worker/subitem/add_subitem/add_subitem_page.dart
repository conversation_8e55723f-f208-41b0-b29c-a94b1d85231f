import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/add_subitem/entity/add_subitem_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/add_subitem/vm/add_subitem_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

class AddSubitemPage extends BaseFulPage {
  const AddSubitemPage({Key? key}) : super(appBar: null);

  @override
  State createState() => _AddSubitemPageState();
}

class _AddSubitemPageState extends BaseFulPageState {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _unitController = TextEditingController();
  final vm = AddSubitemVm();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    // 初始化页面数据
    if (routeParams != null) {
      var props = routeParams as AddSubitemProps?;
      if (props != null) {
        vm.addSubitemUs.value.setWorkId(props.workNoteId ?? "");
        vm.addSubitemUs.value.setName(props.name ?? "");
        vm.addSubitemUs.value.setUnit(props.unit ?? "");
        vm.addSubitemUs.value.setIsAdd(props.isAdd ?? true);
        vm.addSubitemUs.value.setSubitemId(props.subitemId ?? 0);

        _nameController.text = props.name ?? "";
        _unitController.text = props.unit ?? "";
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _unitController.dispose();
    super.dispose();
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leadingWidth: 38,
      titleSpacing: 8,
      title: Text(
        vm.addSubitemUs.value.isAdd ? '添加分项' : '修改分项',
        style: const TextStyle(
          color: Colors.black,
          fontSize: 22,
          fontWeight: FontWeight.w600,
        ),
      ),
      leading: GestureDetector(
        onTap: () => YPRoute.closePage(),
        child: Container(
          padding: const EdgeInsets.only(left: 8.0),
          child: Image(
            image: AssetImage(Assets.commonIconArrowBack),
          ),
        ),
      ),
    );
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          SizedBox(height: 1),
          // 输入字段区域
          Obx(() => Container(
                color: Colors.white,
                child: Column(
                  children: [
                    _buildInputRow(
                      label: '分项名称',
                      placeholder: '请输入分项名称',
                      controller: _nameController,
                      showArrow: false,
                    ),
                    _buildInputRow(
                      label: '分项单位',
                      placeholder: '请选择分项单位',
                      controller: _unitController,
                      showArrow: true,
                      onTap: () {
                        YPRoute.openPage(RouteNameCollection.addSubitemUnit,
                                params: _unitController.text)
                            ?.then((res) {
                          if (res != null) {
                            res as String;
                            _unitController.text = res;
                          }
                        });
                      },
                    ),
                  ],
                ),
              )),
          // 内容区域
          Expanded(
            child: Container(
              color: const Color(0xFFF5F5F5),
              width: double.infinity,
            ),
          ),
          // 底部保存按钮
          _buildBottomButton(),
        ],
      ),
    );
  }

  /// 构建输入行
  Widget _buildInputRow({
    required String label,
    required String placeholder,
    required TextEditingController controller,
    required bool showArrow,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 54,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        decoration: const BoxDecoration(
          border: Border(
            bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1),
          ),
        ),
        child: Row(
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF323232),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextField(
                controller: controller,
                enabled: !showArrow,
                maxLength: 20,
                // 如果是选择类型，禁用输入
                decoration: InputDecoration(
                  hintText: placeholder,
                  hintStyle: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF999999),
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                  counterText: '',
                ),
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF323232),
                ),
                onChanged: (value) {
                  if (controller == _nameController) {
                    vm.addSubitemUs.value.setName(value);
                  }
                  if (controller == _unitController) {
                    vm.addSubitemUs.value.setUnit(value);
                  }
                },
              ),
            ),
            if (showArrow)
              const Icon(
                Icons.chevron_right,
                color: Color(0xFF999999),
                size: 20,
              ),
            if (!showArrow && vm.addSubitemUs.value.name.isNotEmpty)
              GestureDetector(
                  onTap: () {
                    _nameController.clear();
                    vm.addSubitemUs.value.setName("");
                  },
                  child: Image.asset(
                    Assets.commonIcCleanInput,
                    width: 17,
                    height: 17,
                  )),
          ],
        ),
      ),
    );
  }

  /// 构建底部按钮
  Widget _buildBottomButton() {
    return SafeArea(
      top: false,
      child: Container(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 24),
        color: Colors.white,
        child: Row(
          children: [
            if (!vm.addSubitemUs.value.isAdd)
              Expanded(
                child: SizedBox(
                  height: 44,
                  child: GestureDetector(
                    onTap: () {
                      vm.showDeleteDialog();
                    },
                    child: Container(
                      alignment: Alignment.center,
                      margin: const EdgeInsets.only(right: 10),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.red,
                          width: 1,
                        ),
                        borderRadius: BorderRadius.all(Radius.circular(4)),
                      ),
                      child: const Text(
                        '删除',
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  if (vm.addSubitemUs.value.isAdd) {
                    vm.addNewSubitem();
                  } else {
                    vm.updateSubitem();
                  }
                },
                child: Container(
                  height: 44.h,
                  decoration: BoxDecoration(
                      color: ColorsUtil.primaryColor,
                      borderRadius: BorderRadius.all(Radius.circular(4.w))),
                  child: Center(
                    child: Text('保存',
                        style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.white)),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
