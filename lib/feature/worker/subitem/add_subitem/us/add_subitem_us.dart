import 'package:get/get.dart';

class AddSubitemUs {
  // 分项名称
  final _name = "".obs;

  String get name => _name.value;

  setName(String value) => _name.value = value;

  // 分项单位
  final _unit = "".obs;

  String get unit => _unit.value;

  setUnit(String value) => _unit.value = value;

  // 工本id
  final _workNoteId = "".obs;

  String get workNoteId => _workNoteId.value;

  setWorkId(String value) => _workNoteId.value = value;

  // 分项id
  final _subitemId = 0.obs;

  int get subitemId => _subitemId.value;

  setSubitemId(int value) => _subitemId.value = value;

  // 是否添加
  final _isAdd = true.obs;

  bool get isAdd => _isAdd.value;

  setIsAdd(bool value) => _isAdd.value = value;
}
