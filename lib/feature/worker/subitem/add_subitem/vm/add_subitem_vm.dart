import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/delete_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/unit_work_type_add_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/update_new_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/worker_account_record_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/add_subitem/dialog/del_subitem_dialog.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/add_subitem/us/add_subitem_us.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';

class AddSubitemVm {
  var addSubitemUs = AddSubitemUs().obs;
  final recordRep = WorkerAccountRecordRepo();

  addNewSubitem() async {
    var params = UnitWorkTypeAddParamModel(
        name: addSubitemUs.value.name,
        unit: addSubitemUs.value.unit,
        work_note: addSubitemUs.value.workNoteId);
    recordRep.addNewSubitem(params).then((result) {
      if (result.isOK()) {
        YPRoute.closePage(true);
      }
    });
  }

  updateSubitem() async {
    var params = UpdateNewAParamModel();
    params.name = addSubitemUs.value.name;
    params.unit = addSubitemUs.value.unit;
    params.work_note = double.tryParse(addSubitemUs.value.workNoteId) ?? 0;
    var workId = addSubitemUs.value.subitemId;
    recordRep.updateSubitem(params, workId).then((result) {
      if (result.isOK()) {
        YPRoute.closePage(true);
      }
    });
  }

  showDeleteDialog() {
    DelSubitemDialog.show(onConfirm: () {
      deleteSubitem();
    });
  }

  deleteSubitem() async {
    var workId = addSubitemUs.value.subitemId;
    var workNoteId = addSubitemUs.value.workNoteId.isNotEmpty
        ? int.tryParse(addSubitemUs.value.workNoteId) ?? 0
        : 0;
    var params = DeleteParamModel(work_note: workNoteId);
    recordRep.deleteSubitem(params, workId).then((result) {
      if (result.isOK()) {
        YPRoute.closeDialog();
        YPRoute.closePage(true);
      }
    });
  }
}
