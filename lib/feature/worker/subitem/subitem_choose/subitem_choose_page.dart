import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/add_subitem/entity/add_subitem_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/subitem_choose/vm/subitem_choose_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

class SubitemChoosePage extends BaseFulPage {
  const SubitemChoosePage({Key? key})
      : super(appBar: const YPAppBar(title: "分项选择"));

  @override
  State createState() => _SubitemChoosePageState();
}

class _SubitemChoosePageState extends BaseFulPageState {
  final vm = SubitemChooseVm();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    var props = routeParams as String?;
    if (props != null) {
      vm.initSubitemList(workNoteId: props);
    } else {
      vm.initSubitemList();
    }
  }

  @override
  Widget yBuild(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            const SizedBox(height: 8),
            Obx(() => Expanded(
                  child: SingleChildScrollView(
                    child: vm.uiState.value.subitemList.isNotEmpty == true
                        ? ListView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return _buildSelectionItem(context, index);
                            },
                            itemCount: vm.uiState.value.subitemList.length,
                          )
                        : Container(),
                  ),
                )),
            SizedBox(height: 74)
          ],
        ),
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: SafeArea(
            top: false,
            child: Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFF5F5F5), width: 1),
                ),
                color: Colors.white,
              ),
              child: GestureDetector(
                onTap: () {
                  YPRoute.openPage(RouteNameCollection.addSubitem)
                      ?.then((result) {
                    if (result is bool && result == true) {
                      vm.initSubitemList();
                    }
                  });
                },
                child: Container(
                  margin:
                      EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 24),
                  height: 44.h,
                  decoration: BoxDecoration(
                      color: ColorsUtil.primaryColor,
                      borderRadius: BorderRadius.all(Radius.circular(4.w))),
                  child: Center(
                    child: Text('添加分项',
                        style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.white)),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建选择项
  Widget _buildSelectionItem(BuildContext context, int index) {
    final itemData = vm.uiState.value.subitemList[index];
    return InkWell(
      onTap: () => YPRoute.closePage(itemData),
      child: Container(
        height: 54,
        padding: EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(color: Color(0xFFF5F5F5), width: 1),
          ),
        ),
        child: Row(
          children: [
            Text(
              itemData.name ?? '',
              style: TextStyle(fontSize: 17.sp, color: Color(0xFF323232)),
            ),
            Spacer(),
            GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                YPRoute.openPage(RouteNameCollection.addSubitem,
                        params: AddSubitemProps(
                            isAdd: false,
                            name: itemData.name,
                            unit: itemData.unit,
                            subitemId: itemData.id.toInt()))
                    ?.then((result) {
                  if (result is bool && result == true) {
                    vm.initSubitemList();
                  }
                });
              },
              child: Container(
                padding: EdgeInsets.all(4),
                child: Image.asset(
                  Assets.workerIconMore,
                  width: 17,
                  height: 17,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
