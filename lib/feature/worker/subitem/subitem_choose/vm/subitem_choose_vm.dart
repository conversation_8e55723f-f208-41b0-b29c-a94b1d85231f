import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/unit_work_type_get_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/worker_account_record_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/subitem_choose/us/subitem_choose_us.dart';
import 'package:get/get.dart';

class SubitemChooseVm {
  var uiState = SubitemChooseUs().obs;
  final recordRep = WorkerAccountRecordRepo();

  initSubitemList({String? workNoteId}) async {
    var params = UnitWorkTypeGetParamModel(work_note: workNoteId);
    recordRep.fetchSubitemList(params).then((result) {
      if (result.isOK()) {
        var data = result.getSucData();
        if (data != null) {
          uiState.value.setSubitemList(data.list);
        }
      }
    });
  }
}
