import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/expenses_net_model_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/unit_work_type_get_biz_model.dart';
import 'package:get/get.dart';

class SubitemChooseUs {

  final _subitemList = <UnitWorkTypeGetABizModel>[].obs;

  List<UnitWorkTypeGetABizModel> get subitemList => _subitemList.value;

  setSubitemList(List<UnitWorkTypeGetABizModel>? value) {
    _subitemList.value = value ?? [];
  }
}
