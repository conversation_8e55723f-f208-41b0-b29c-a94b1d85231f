import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/add_subitem_unit/vm/add_subitem_unit_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';

class AddSubitemUnitPage extends BaseFulPage {
  const AddSubitemUnitPage({Key? key}) : super(appBar: null);

  @override
  State createState() => _AddSubitemUnitPageState();
}

class _AddSubitemUnitPageState extends BaseFulPageState {
  final TextEditingController _unitController = TextEditingController();
  final vm = AddSubitemUnitVM();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    vm.initUnitList();
    if (routeParams != null) {
      _unitController.text = routeParams as String;
    }
  }

  @override
  void dispose() {
    _unitController.dispose();
    super.dispose();
  }

  AppBar _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leadingWidth: 38,
      titleSpacing: 8,
      title: const Text(
        '添加分项单位',
        style: TextStyle(
          color: Colors.black,
          fontSize: 22,
          fontWeight: FontWeight.w600,
        ),
      ),
      leading: GestureDetector(
        onTap: () => YPRoute.closePage(),
        child: Container(
          padding: const EdgeInsets.only(left: 8.0),
          child: Image(
            image: AssetImage(Assets.commonIconArrowBack),
          ),
        ),
      ),
      actions: [_buildConfirmBtn(), SizedBox(width: 16)],
    );
  }

  Widget _buildConfirmBtn() {
    return GestureDetector(
      onTap: () {
        YPRoute.closePage(_unitController.text);
      },
      child: Obx(() => Container(
          height: 36,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: vm.isBtnEnable.value
                ? ColorsUtil.primaryColor
                : ColorsUtil.f5f5f5,
          ),
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text('确定',
              style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: vm.isBtnEnable.value
                      ? Colors.white
                      : ColorsUtil.black10)))),
    );
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Container(
        color: Colors.white,
        child: Column(
          children: [
            // 单位输入区域
            Container(
              height: 56,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              child: Row(
                children: [
                  const Text(
                    '单位',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF323232),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: _unitController,
                      maxLength: 4,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            // 只允许输入汉字
                            RegExp(r'[\u4e00-\u9fa5]'))
                      ],
                      // 限制最多4个字符
                      decoration: InputDecoration(
                        hintText: '请输入单位名称(不超过四个汉字)',
                        hintStyle: TextStyle(
                          fontSize: 17.sp,
                          color: Color(0xFFCCCCCC),
                        ),
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.zero,
                        counterText: '',
                        // 隐藏字符计数器
                        isCollapsed: true,
                      ),
                      style: TextStyle(
                        fontSize: 17.sp,
                        color: Color(0xFF323232),
                      ),
                      onChanged: (value) {
                        if (value.trim().isNotEmpty) {
                          vm.setBtnStatus(true);
                        } else {
                          vm.setBtnStatus(false);
                        }
                      },
                    ),
                  ),
                  Obx(() => GestureDetector(
                        onTap: () {
                          _unitController.clear();
                          vm.setBtnStatus(false);
                        },
                        child: vm.isBtnEnable.value
                            ? Image.asset(
                                Assets.commonIcCleanInput,
                                width: 17,
                                height: 17,
                              )
                            : SizedBox(),
                      ))
                ],
              ),
            ),
            Container(
              height: 1,
              margin: EdgeInsets.symmetric(horizontal: 16),
              color: ColorsUtil.divideLineColor,
            ),
            // 快速选择单位区域
            Expanded(
              child: Container(
                color: Colors.white,
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '快速选择单位',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF323232),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Obx(() => Expanded(
                          child: _buildUnitGrid(),
                        )),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建单位选择网格
  Widget _buildUnitGrid() {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 2,
      ),
      itemCount: vm.uiState.value.unitList.length,
      itemBuilder: (context, index) {
        final unit = vm.uiState.value.unitList[index].name;

        return GestureDetector(
          onTap: () {
            setState(() {
              _unitController.text = unit;
            });
            YPRoute.closePage(unit);
          },
          child: Container(
            color: const Color(0xFFF5F5F5),
            height: 40,
            child: Center(
              child: Text(
                unit,
                style: TextStyle(
                  fontSize: 14,
                  color: const Color(0xFF666666),
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
