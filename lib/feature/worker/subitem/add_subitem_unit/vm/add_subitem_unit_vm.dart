import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/worker_account_record_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/add_subitem_unit/us/add_subitem_unit_us.dart';
import 'package:get/get.dart';

class AddSubitemUnitVM {
  var uiState = AddSubitemUnitUs().obs;
  var isBtnEnable = false.obs;

  setBtnStatus(bool status) {
    isBtnEnable.value = status;
  }

  final recordRep = WorkerAccountRecordRepo();

  initUnitList() async {
    recordRep.fetchSubitemUnitList().then((result) {
      if (result.isOK()) {
        var data = result.getSucData();
        if (data != null) {
          uiState.value.setUnitList(data.list);
        }
      }
    });
  }
}