import 'package:gdjg_pure_flutter/data/worker_worklog_trash/ds/param/business_get_recycle_bin_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_worklog_trash/ds/param/business_recover_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_worklog_trash/repo/worker_worklog_trash_repo.dart';
import 'package:gdjg_pure_flutter/feature/worker/trash/ui_model/worker_worklog_overview_ui_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/trash/us/worker_worklog_trash_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

import '../../../../data/worker_worklog_trash/repo/model/business_get_recycle_bin_biz_model.dart';
import '../../../../model/RecordType.dart';

class WorkerWorklogTrashVM {
  final WorkerWorklogTrashRepo _worklogRepo = WorkerWorklogTrashRepo();
  final WorkerWorklogTrashUS us = WorkerWorklogTrashUS();

  void initialize() {
    us.updateStartDate(DateTime(2020, 1, 1, 0, 0, 0, 0, 0));
    us.updateEndDate(DateTime.now());
    us.updateProjectFilter([]);
    us.updateRwaRecordTypes([]);
    _fetchWorklogList();
  }

  Future<void> _fetchWorklogList() async {
    final BusinessGetRecycleBinParamModel params =
        BusinessGetRecycleBinParamModel(
      work_notes: us.projectFilter.map((e) => e.id).join(','),
      business_type: us.rwaRecordTypes.map((e) => e.code.value).join(','),
      start_time: _transformDateToTransport(us.startTime),
      end_time: _transformDateToTransport(us.endTime),
    );
    final RespResult<BusinessGetRecycleBinBizModel?> biz =
        await _worklogRepo.getWorkerRecycleBinInfo(params);
    if (biz.isOK()) {
      final List<BusinessGetRecycleBinABizModel> dailyList =
          biz.success?.data?.list ?? <BusinessGetRecycleBinABizModel>[];
      final worklogs = List.generate(
        dailyList.length,
        (index) {
          final BusinessGetRecycleBinABizModel item = dailyList[index];
          final List<BusinessGetRecycleBinBBizModel> records = item.list;
          return WorkerWorklogDailyOverviewUiModel(
            item.date,
            List.generate(
              records.length,
              (index) {
                final BusinessGetRecycleBinBBizModel record = records[index];
                return WorkerWorklogRecordOverviewUiModel.fromBizModel(record);
              },
            ),
          );
        },
      );
      us.updateWorklogs(worklogs);
    } else {
      us.updateWorklogs([]);
    }
  }

  Future<void> recover() async {
    final Set<int> ids = us.selectedRecordIds.toSet();
    final BusinessRecoverParamModel param = BusinessRecoverParamModel(
      ids: ids.join(','),
      work_note: '0',
    );
    final RespResult<Object?> biz = await _worklogRepo.postRecover(param);
    if (biz.isOK()) {
      us.recoverRecord();
      ToastUtil.showToast('信息恢复完成，请返回项目查看');
    }
  }

  void updateFilter({
    DateTime? start,
    DateTime? end,
    List<RwaRecordType>? types,
  }) {
    if (start != null) {
      us.updateStartDate(start);
    }
    if (end != null) {
      us.updateEndDate(end);
    }
    if (types != null) {
      us.updateRwaRecordTypes(types);
    }
    _fetchWorklogList();
  }

  String _transformDateToTransport(DateTime date) =>
      '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
}
