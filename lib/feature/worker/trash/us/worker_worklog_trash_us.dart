import 'package:collection/collection.dart';
import 'package:gdjg_pure_flutter/feature/worker/trash/ui_model/project_filter_ui_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/trash/ui_model/worker_worklog_overview_ui_model.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:get/get.dart';

class WorkerWorklogTrashUS {
  final Rx<DateTime> _startDate = Rx(DateTime.now());
  final Rx<DateTime> _endDate = Rx(DateTime.now());
  final RxList<ProjectFilterUiMode> _projectFilter = RxList();
  final RxList<RwaRecordType> _rwaRecordTypes = RxList();
  final RxList<WorkerWorklogDailyOverviewUiModel> _worklogs = RxList();
  final RxSet<int> _selectedWorklogIds = RxSet();

  DateTime get startTime => _startDate.value;

  DateTime get endTime => _endDate.value;

  List<ProjectFilterUiMode> get projectFilter => _projectFilter;

  List<RwaRecordType> get rwaRecordTypes => _rwaRecordTypes;

  List<WorkerWorklogDailyOverviewUiModel> get worklogs => _worklogs;

  Set<int> get selectedRecordIds => _selectedWorklogIds;

  void updateStartDate(DateTime date) => _startDate.value = date;

  void updateEndDate(DateTime date) => _endDate.value = date;

  void updateProjectFilter(List<ProjectFilterUiMode> mode) =>
      _projectFilter.assignAll(mode);

  void updateRwaRecordTypes(List<RwaRecordType> types) =>
      _rwaRecordTypes.assignAll(types);

  void updateWorklogs(List<WorkerWorklogDailyOverviewUiModel> worklogs) =>
      _worklogs.assignAll(worklogs);

  void flipSelectAll() {
    final Set<int> ids =
        _worklogs.expand((e) => e.records).map((e) => e.id).toSet();
    if (SetEquality().equals(_selectedWorklogIds, ids)) {
      _selectedWorklogIds.clear();
    } else {
      _selectedWorklogIds.assignAll(ids);
    }
  }

  void flipSelection(int id) {
    if (!_selectedWorklogIds.remove(id)) {
      _selectedWorklogIds.add(id);
    }
  }

  void recoverRecord() {
    final Set<int> ids = _selectedWorklogIds.toSet();
    final List<WorkerWorklogDailyOverviewUiModel> newList = _worklogs
        .map(
          (e) => WorkerWorklogDailyOverviewUiModel(
            e.date,
            e.records.where((e) => !ids.contains(e.id)).toList(),
          ),
        )
        .where((e) => e.records.isNotEmpty)
        .toList();
    updateWorklogs(newList);
    _selectedWorklogIds.clear();
  }
}
