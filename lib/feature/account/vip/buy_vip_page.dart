import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/vip/repo/biz/vip_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/account/vip/vm/buy_vip_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/common_dialog.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

class BuyVipPage extends BaseFulPage {
  const BuyVipPage({super.key}) : super(appBar: const YPAppBar(title: "去广告会员"), canBack: false);

  @override
  State createState() => _BuyVipPage();
}

class _BuyVipPage extends BaseFulPageState with SingleTickerProviderStateMixin {
  final vm = BuyVipVM();

  @override
  Widget yBuild(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(height: 26.h),
            _buildVipStateView(),
            SizedBox(height: 16.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.w),
                  topRight: Radius.circular(12.w),
                ),
              ),
              child: Column(
                children: [
                  _buildVipList(),
                  SizedBox(height: 8.h),
                  _buildAgreement(),
                  SizedBox(height: 8.h),
                  _buildBuyBtn(),
                  SizedBox(height: 16.h),
                  _buildVipTip(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAgreement() {
    return GestureDetector(
      onTap: () {
        vm.setIsAgree();
      },
      child: Container(
        color: Colors.transparent,
        padding: EdgeInsets.symmetric(vertical: 10.h),
        child: Row(
          children: [
            Obx(() => IconFont(
                  vm.us.isAgree ? IconNames.saasCheck : IconNames.saasRadio,
                  size: 14,
                  color: '#5290FD',
                )),
            SizedBox(width: 5.w),
            Text.rich(
              TextSpan(
                text: '我已阅读并同意：',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0x73000000),
                ),
                children: [
                  TextSpan(
                    text: "《会员协议》",
                    style: TextStyle(fontSize: 12.sp, color: ColorsUtil.ypPrimaryColor),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        ToastUtil.showToast("跳转会员协议");
                      },
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildBuyBtn() {
    return GestureDetector(
      onTap: () {
        if (vm.us.isAgree) {
          vm.getVipOrder();
        } else {
          showCommonDialog(CommonDialogConfig(
            title: '提示',
            contentWidget: Text.rich(
              TextSpan(
                text: '${vm.getOpenOrRenewStr()}会员前，必须阅读并同意',
                style: TextStyle(fontSize: 14.sp, color: Color(0xFF8A8A99)),
                children: [
                  TextSpan(
                    text: "《会员协议》",
                    style: TextStyle(fontSize: 14.sp, color: ColorsUtil.ypPrimaryColor),
                  ),
                  TextSpan(text: "，是否继续${vm.getOpenOrRenewStr()}会员？")
                ],
              ),
            ),
            negative: '取消',
            positive: '同意并开通',
            onPositive: () {
              vm.setIsAgree();
              vm.getVipOrder();
            },
          ));
        }
      },
      child: Container(
        width: double.infinity,
        height: 44.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.w),
          gradient: LinearGradient(
            colors: [Color(0xFF82B0FF), Color(0xFF5290FD)], // 渐变颜色
            begin: Alignment.centerLeft, // 渐变起始位置
            end: Alignment.centerRight, // 渐变结束位置
          ),
        ),
        child: Center(
          child: Text(
            "确定并开通会员",
            style: TextStyle(fontSize: 16, color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildVipTip() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Color(0xFFf6f6f6),
        borderRadius: BorderRadius.circular(8.w),
      ),
      child: // 富文本
          Obx(() => Text.rich(
                TextSpan(
                  text: '会员说明：\n',
                  style: TextStyle(fontSize: 14.sp, color: Color(0xFF8A8A99)),
                  children: [
                    TextSpan(
                      text: vm.us.vipHintTitle,
                      style: TextStyle(fontSize: 14.sp, color: ColorsUtil.ypPrimaryColor),
                    ),
                    TextSpan(text: vm.us.vipHintText),
                  ],
                ),
              )),
    );
  }

  Widget _buildVipList() {
    return Container(
      width: double.infinity,
      child: LayoutBuilder(builder: (context, constraints) {
        return Obx(() => Wrap(
              spacing: 13, // 横向间距
              runSpacing: 13, // 纵向间距
              children: List.generate(vm.us.vipList.length, (index) {
                final item = vm.us.vipList[index];
                final isSelect = item.id == vm.us.selectItem?.id;
                return SizedBox(
                  width: (constraints.maxWidth - 26) / 3, // 计算宽度（考虑间距）
                  child: _buildVipItem(item, isSelect),
                );
              }),
            ));
      }),
    );
  }

  Widget _buildVipItem(VipListBizModel item, bool isSelect) {
    return GestureDetector(
        onTap: () {
          vm.selectItem(item);
        },
        child: Container(
          width: double.infinity,
          child: Stack(
            children: [
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(top: 7.h),
                padding: EdgeInsets.symmetric(vertical: 27.h),
                decoration: BoxDecoration(
                  color: isSelect ? Color(0x1A5290FD) : Color(0xFFF6F6F6),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelect ? ColorsUtil.ypPrimaryColor : Color(0x80E6EDFF),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      item.name,
                      style: TextStyle(fontSize: 16, color: Colors.black),
                    ),
                    Row(mainAxisAlignment: MainAxisAlignment.center, crossAxisAlignment: CrossAxisAlignment.center, children: [
                      Text(
                        "¥",
                        style: TextStyle(fontSize: 16, color: isSelect ? ColorsUtil.ypPrimaryColor : Colors.black),
                      ),
                      SizedBox(width: 4),
                      Text(
                        item.price,
                        style: TextStyle(fontSize: 24, color: isSelect ? ColorsUtil.ypPrimaryColor : Colors.black, fontWeight: FontWeight.w500),
                      ),
                    ]),
                    Text(
                      item.priceDesc,
                      style: TextStyle(
                        fontSize: 12,
                        color: isSelect ? ColorsUtil.ypPrimaryColor : Color(0x73000000),
                        decoration: item.priceShow == 1 ? TextDecoration.lineThrough : TextDecoration.none,
                        decorationColor: isSelect ? ColorsUtil.ypPrimaryColor : Color(0x73000000),
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                  left: 0,
                  top: 0,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 6),
                    constraints: BoxConstraints(minHeight: 21.h),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0xFFFF8D1A), Color(0xFFFF5733)],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8.w),
                        bottomRight: Radius.circular(8.w),
                      ),
                    ),
                    child: Center(
                      child: Text(item.label, style: TextStyle(fontSize: 12, color: Colors.white)),
                    ),
                  ))
            ],
          ),
        ));
  }

  Widget _buildVipStateView() {
    const double imageAspectRatio = 1029 / 360;
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 16.h),
      child: LayoutBuilder(builder: (context, constraints) {
        return Container(
          width: double.infinity,
          constraints: BoxConstraints(minHeight: constraints.maxWidth / imageAspectRatio),
          decoration: BoxDecoration(
            image: DecorationImage(
              image: AssetImage(Assets.commonWorkandaccountOpenVipCardNo),
              fit: BoxFit.fill,
            ),
          ),
          child: Obx(() => Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Text(
                          "当前会员：",
                          style: TextStyle(fontSize: 14, color: const Color(0xA6FFFFFF)),
                        ),
                        SizedBox(width: 4),
                        Text(
                          vm.isVip() ? "已开通（到期时间：${vm.vipMaturityTime}）" : "未开通",
                          style: TextStyle(fontSize: 14, color: const Color(0xFFFFFFFF)),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          "开通期限：",
                          style: TextStyle(fontSize: 14, color: const Color(0xA6FFFFFF)),
                        ),
                        SizedBox(width: 4),
                        Text(
                          vm.vipItemDuration,
                          style: TextStyle(fontSize: 14, color: const Color(0xFFFFFFFF)),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        Text(
                          "开通后到期时间：",
                          style: TextStyle(fontSize: 14, color: const Color(0xA6FFFFFF)),
                        ),
                        SizedBox(width: 4),
                        Text(
                          vm.getVipMaturityTime(),
                          style: TextStyle(fontSize: 14, color: const Color(0xFFFFFFFF)),
                        ),
                      ],
                    ),
                    SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,

                      children: [
                        Text(
                          "套餐福利：",
                          style: TextStyle(fontSize: 14, color: const Color(0xA6FFFFFF)),
                        ),
                        SizedBox(width: 4),
                        Flexible(
                            flex: 1,
                            child: Container(
                              width: double.infinity,
                              child: Wrap(
                                direction: Axis.horizontal,
                                alignment: WrapAlignment.start,
                                spacing: 8,
                                runSpacing: 8,
                                runAlignment: WrapAlignment.start,
                                children: vm.us.selectItem?.welfare
                                        .map((e) => Container(
                                  height: 22.h,
                                  padding: EdgeInsets.symmetric(horizontal: 4.w),
                                  decoration: BoxDecoration(
                                    color: Colors.transparent,
                                    borderRadius: BorderRadius.circular(4),
                                    border: Border.all(
                                      color: Colors.white,
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(e,style: TextStyle(fontSize: 13, color: const Color(0xFFFFFFFF)),textAlign: TextAlign.center,)
                                ),
                                )
                                        .toList() ??
                                    [],
                              ),
                            )),
                      ],
                    ),
                  ],
                ),
              )),
        );
      }),
    );
  }

  @override
  Decoration? getPageBackgroundDecoration() {
    return BoxDecoration(
      image: DecorationImage(
        alignment: Alignment.topCenter,
        image: AssetImage(Assets.commonWorkandaccountOpenVipBg),
        fit: BoxFit.contain,
      ),
      color: Colors.white,
    );
  }
  @override
  bool? callbackIntercept() {
      showCommonDialog(CommonDialogConfig(
      title: '工友请留步',
      contentWidget: Text.rich(
        TextSpan(
          style: TextStyle(fontSize: 14.sp, color: Color(0xFF8A8A99)),
          children: [
            TextSpan(
              text: vm.us.vipHintTitle,
              style: TextStyle(fontSize: 14.sp, color: ColorsUtil.ypPrimaryColor),
            ),
            TextSpan(text: vm.us.vipHintText),
          ],
        ),
      ),
      negative: '忍痛离开',
      positive: '继续支付',
      onNegative: () {
        YPRoute.closePage();
      }, onPositive: () {

      },
    ));
      return false;
  }



}
