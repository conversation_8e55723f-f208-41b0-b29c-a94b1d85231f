
import 'package:gdjg_pure_flutter/data/vip/ds/param/vip_purchase_param_model.dart';
import 'package:gdjg_pure_flutter/data/vip/repo/biz/vip_biz_model.dart';
import 'package:gdjg_pure_flutter/data/vip/repo/vip_repo.dart';
import 'package:gdjg_pure_flutter/feature/account/vip/vm/buy_vip_us.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/wechat_util/wechat_util.dart';
import 'package:intl/intl.dart';

class BuyVipVM {
  final _vipRepo = VipRepo();
  final us = BuyVipUS();
  BuyVipVM() {
    _getVipStatus();
    _getVipList();
  }

  _getVipList() async {
    var result = await _vipRepo.getVipList();
    if (result.isOK()) {
      var data = result.getSucData();
      if(data != null){
        us.setVipList(data.vipList);
        var list = data.explanation.split('@');
        us.setVipHintTitle(list[1] ?? "");
        us.setVipHintText(list[2] ?? "");
        if(data.vipList.isNotEmpty){
          final selectItem = data.vipList.firstWhere((item) => item.defaultShow == 1, orElse: () => data.vipList.first);
          us.setSelectItem(selectItem);
        }

      }
    }

  }

  getVipOrder() async {
    final vipId =  us.selectItem?.id.toString().trimTrailingZeros() ?? "";
    if(vipId.isEmpty){
      ToastUtil.showToast("会员${getOpenOrRenewStr()}异常，请稍后重试");
      return;
    }

    var param = VipPurchaseParamModel(
      vip_id: vipId,
        pay_channel: "WEIXIN"
    );
    var result = await _vipRepo.getVipOrder(param);
    if (result.isOK()) {
      var data = result.getSucData();
      if(data != null){
        final payResult = await WeChatUtil.wxPay(
          timestamp: data.extInfo?.timestamp ?? '',
          packageValue: data.extInfo?.packageValue ?? '',
          paySign: data.extInfo?.paySign ?? '',
          signType: data.extInfo?.signType ?? '',
          partnerId: data.extInfo?.partnerId ?? '',
          prepayId: data.extInfo?.prepayId ?? '',
          nonceStr: data.extInfo?.nonceStr ?? '',
        );
        if(payResult){
          _getVipStatus();
        }else{
          ToastUtil.showToast("支付失败");
        }

      }
    }

  }

  String getOpenOrRenewStr(){
    final expireTime = us.vipStatus?.expireTime;

    return expireTime != null && expireTime.isNotEmpty ? "续费" : "开通";
  }


  _getVipStatus() async {
    var result = await _vipRepo.getVipStatus();
    if (result.isOK()) {
      var data = result.getSucData();
      if(data != null){
        us.setVipStatus(data);

      }
    }

  }

  selectItem(VipListBizModel item){
    us.setSelectItem(item);
  }

  setIsAgree(){
    us.setIsAgree(us.isAgree== false);
  }

  bool isVip(){
    return us.vipStatus?.status == 1;
  }

  String get vipMaturityTime{
    return us.vipStatus?.expireTime.trimTrailingZeros() ?? "";
  }

  String get vipItemDuration{
    return (us.selectItem?.days??0) != 0 ? "${us.selectItem?.days}天" : "";
  }

  String getVipMaturityTime(){
    if(us.vipStatus?.status == 1){
      DateTime fixedDate = DateTime.parse(us.vipStatus?.expireTime ?? DateFormat('yyyy-MM-dd').format(DateTime.now()));
      DateTime after10Days = fixedDate.add(Duration(days: us.selectItem?.days??0));
      // 格式化为 yyyy-MM-dd
      String formattedDate = DateFormat('yyyy-MM-dd').format(after10Days);
      return formattedDate;

    }else{
      // 获取当前时间
      DateTime now = DateTime.now();
      // 当前时间
      DateTime after10Days = now.add(Duration(days: us.selectItem?.days??0));
      // 格式化为 yyyy-MM-dd
      String formattedDate = DateFormat('yyyy-MM-dd').format(after10Days);
      return formattedDate;
    }

  }
}