
import 'package:gdjg_pure_flutter/data/vip/repo/biz/vip_biz_model.dart';
import 'package:gdjg_pure_flutter/data/vip/repo/biz/vip_status_biz_model.dart';
import 'package:get/get.dart';

class BuyVipUS {

  final _vipMaturityTime = ''.obs;
  get vipMaturityTime => _vipMaturityTime.value;
  setVipMaturityTime(String value) {
    _vipMaturityTime.value = value;
  }


  final Rx<VipListBizModel?> _selectItem = Rx<VipListBizModel?>(null);
  VipListBizModel? get selectItem => _selectItem.value;
  setSelectItem(VipListBizModel? value) {
    _selectItem.value = value;
  }

  final Rx<VipStatusBizModel?> _vipStatus = Rx<VipStatusBizModel?>(null);
  VipStatusBizModel? get vipStatus => _vipStatus.value;
  setVipStatus(VipStatusBizModel? value) {
    _vipStatus.value = value;
  }



  final _isAgree = false.obs;
  get isAgree => _isAgree.value;
  setIsAgree(bool value) {
    _isAgree.value = value;
  }


  final _vipList = <VipListBizModel>[].obs;
  List<VipListBizModel> get vipList => _vipList.value;
  setVipList(List<VipListBizModel> value) {
    _vipList.value = value;
  }

  final _vipHintText = ''.obs;
  get vipHintText => _vipHintText.value;
  setVipHintText(String value) {
    _vipHintText.value = value;
  }
  final _vipHintTitle = ''.obs;
  get vipHintTitle => _vipHintTitle.value;
  setVipHintTitle(String value) {
    _vipHintTitle.value = value;
  }

}