import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/group_cloud_album/view/attendance_photo_view.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/group_cloud_album/view/record_work_photo_view.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/group_cloud_album/vm/group_cloud_album_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/combined_filter_widget.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class GroupCloudAlbumPage extends BaseFulPage {
  GroupCloudAlbumPage({super.key})
      : super(
            appBar: YPAppBar(
                title: '班组云相册',
                rightResText: '个人云相册',
                rightResTap: () {
                  ToastUtil.showToast('跳转个人云相册');
                }));

  @override
  State createState() => _GroupCloudAlbumPageState();
}

class _GroupCloudAlbumPageState extends BaseFulPageState {
  final GroupCloudAlbumVM _vm = GroupCloudAlbumVM();
  late RefreshController _refreshController;

  @override
  void onPageCreate() {
    _vm.initData();
    _refreshController = RefreshController(initialRefresh: false);
  }

  @override
  void onPageDestroy() {
    _refreshController = RefreshController(initialRefresh: false);
    super.onPageDestroy();
  }

  @override
  Widget yBuild(BuildContext context) {
    return SmartRefresher(
      controller: _refreshController,
      enablePullDown: true,
      enablePullUp: false,
      onRefresh: _onRefresh,
      child: Container(
        margin: EdgeInsets.only(top: 8),
        color: Colors.white,
        child: Obx(() => Column(
              children: [
                Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      if (_vm.albumStatUiState.projectList.isNotEmpty)
                        CombinedFilterWidget(
                          showProjectFilter: true,
                          showTypeFilter: false,
                          initialProjectList: _vm.albumStatUiState.projectList,
                        ),
                      if (_vm.albumStatUiState.statList.isEmpty)
                        Container()
                      else
                        // 统计卡片
                        Container(
                          // color: Colors.green,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          margin: const EdgeInsets.only(bottom: 8),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  _StatCard(
                                    label:
                                        _vm.albumStatUiState.statList[0].name,
                                    count: 30,
                                    index: 0,
                                    vm: _vm,
                                  ),
                                  _StatCard(
                                    label:
                                        _vm.albumStatUiState.statList[1].name,
                                    count: 30,
                                    index: 1,
                                    vm: _vm,
                                  ),
                                  _StatCard(
                                    label:
                                        _vm.albumStatUiState.statList[2].name,
                                    count: 30,
                                    index: 2,
                                    vm: _vm,
                                  ),
                                  _StatCard(
                                    label:
                                        _vm.albumStatUiState.statList[3].name,
                                    count: 30,
                                    index: 3,
                                    vm: _vm,
                                  ),
                                ],
                              ),
                              if (_vm.currentCardIndex.value == 3)
                                Container(
                                  padding: EdgeInsets.only(top: 10),
                                  child: Row(
                                    children: [
                                      Image.asset(
                                        Assets.commonWaaIcClockTip,
                                        width: 16.w,
                                        height: 16.w,
                                      ),
                                      Text(
                                        '工友在项目中拍摄的每张照片，都会更新他的打卡记录',
                                        style: TextStyle(
                                            color: ColorsUtil.primaryColor,
                                            fontSize: 12.sp),
                                      )
                                    ],
                                  ),
                                )
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
                if (_vm.albumListUiState.cardList.value.list.isNotEmpty) ...[
                  // 照片分组列表
                  if (_vm.currentCardIndex.value == 0 ||
                      _vm.currentCardIndex.value == 1)
                    RecordWorkPhotoView(
                      uiState: _vm.albumListUiState.cardList.value,
                      title: _vm.albumStatUiState
                          .statList[_vm.currentCardIndex.value].name,
                    ),
                  if (_vm.currentCardIndex.value == 2)
                    RecordWorkPhotoView(
                      uiState: _vm.albumListUiState.cardList.value,
                      title: _vm.albumStatUiState.statList[2].name,
                      isShowUpload: true,
                    ),
                  if (_vm.currentCardIndex.value == 3)
                    AttendancePhotoView(
                      uiState:
                          _vm.albumClockListUiState.list.value, // .value不能去掉
                    ),
                ] else ...[
                  // 空数据
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      color: Colors.white,
                      child: Column(
                        children: [
                          SizedBox(
                            height: 100,
                          ),
                          Image.asset(
                            Assets.commonIconPhoto,
                            width: 100,
                            height: 100,
                          ),
                          Text('当前暂无照片',
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  color: ColorsUtil.hintFontColor))
                        ],
                      ),
                    ),
                  )
                ],
              ],
            )),
      ),
    );
  }

  /// 刷新回调
  Future<void> _onRefresh() async {
    await _vm.onRefresh();
    _refreshController.refreshCompleted();
  }
}

class _StatCard extends StatelessWidget {
  final String label;
  final int count;
  final int index;
  final GroupCloudAlbumVM vm;

  const _StatCard({
    required this.label,
    required this.count,
    required this.index,
    required this.vm,
  });

  @override
  Widget build(BuildContext context) {
    Get.put(vm);
    return Obx(() => GestureDetector(
          onTap: () {
            vm.currentCardIndex.value = index;
            if (index == 3) {
              vm.fetchAlbumClockData();
            } else {
              vm.fetchAlbumListData().then((res) {
                if (res != null) {
                  vm.albumListUiState.cardList.value = res;
                }
              });
            }
          },
          child: Container(
            width: 78,
            padding: const EdgeInsets.only(top: 8, left: 8, right: 8),
            decoration: BoxDecoration(
              border: Border.all(
                  color: vm.currentCardIndex.value == index
                      ? ColorsUtil.primaryColor
                      : ColorsUtil.inputBgColor,
                  width: 1),
              borderRadius: BorderRadius.circular(4),
              color: vm.currentCardIndex.value == index
                  ? ColorsUtil.primaryColor15
                  : Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Image.network(
                      vm.albumStatUiState.statList[index].icon,
                      fit: BoxFit.cover,
                      width: 10,
                      height: 10,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(color: Colors.white);
                      },
                    ),
                    const SizedBox(height: 6),
                    Text(label,
                        style: TextStyle(
                            fontSize: 12.sp, fontWeight: FontWeight.w500)),
                  ],
                ),
                const SizedBox(height: 2),
                Text('照片数量：',
                    style: TextStyle(fontSize: 12.sp, color: Colors.grey)),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      alignment: Alignment.centerRight,
                      width: 45,
                      child: Text(
                        vm.albumStatUiState.statList[index].num.toString(),
                        style: TextStyle(
                          fontSize: 20.sp,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text('张',
                        style: TextStyle(
                          fontSize: 14.sp,
                        )),
                  ],
                ),
              ],
            ),
          ),
        ));
  }
}
