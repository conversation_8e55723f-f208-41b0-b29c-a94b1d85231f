import "package:collection/collection.dart";
import "package:gdjg_pure_flutter/feature/cloud_album/group/choose_project_filter/entity/choose_project_filter_props.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:get/get.dart";
import "protocol/choose_project_filter_us.dart";

/// @date 2025/07/07
/// @description ChooseProject页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class ChooseProjectFilterVm {
  var backParams = {};
  var dataList = {}.obs;
  var uiState = ChooseProjectFilterUs().obs;

  void initData(ChooseProjectFilterProps props) {
    uiState.value.workNoteList = props.workNoteList;
  }

  void setChecked(int index) {
    String? res = "";
    var mList = uiState.value.workNoteList.mapIndexed((idx, item) {
      if (index == idx) {
        item.isChecked = true;
        res = item.workNoteName;
        backParams = {"name": item.workNoteName, "id": item.workNoteId};
      } else {
        item.isChecked = false;
      }
      return item;
    }).toList();
    uiState.value = ChooseProjectFilterUs(workNoteList: mList);
    YPRoute.closePage(res);
  }
}
