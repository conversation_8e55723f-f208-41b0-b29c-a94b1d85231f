import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/ad/ad_vm.dart';
import 'package:gdjg_pure_flutter/data/account/repo/auth_repo.dart';
import 'package:gdjg_pure_flutter/feature/tabbar/view/rotation_switch_view.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:get/get.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

// Token登录功能相关导入 - 独立模块，便于后续移除
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/generated/pigeons/navigation_api.dart';
import 'package:gdjg_pure_flutter/data/account/ds/auth_rds.dart';
import 'package:gdjg_pure_flutter/data/account/ds/param/waa_login_param_model.dart';
import 'package:gdjg_pure_flutter/data/account/ds/account_lds.dart';

import '../group/page/group_page.dart';
import '../worker/page/worker_page.dart';
import 'us/identity_us.dart';
import 'vm/identity_vm.dart';

class MainPage extends BaseFulPage {
  const MainPage({Key? key}) : super(key: key, appBar: null);

  @override
  State createState() => _MainPageState();
}

class _MainPageState<MainPage> extends BaseFulPageState {
  final AuthRepo _authRepo = AuthRepo();
  final AdInterstitialVM adVM = Get.put(AdInterstitialVM());
  final NativeAdVM nativeAdVM = Get.put(NativeAdVM());
  final identityController = IdentityVM.to;
  var isWorker = true;
  Key _workerPageKey = UniqueKey();
  Key _groupPageKey = UniqueKey();
  late Worker _identityWorker;

  // Token登录功能相关 - 独立模块，便于后续移除
  final AuthRds _authRds = AuthRds();
  final AccountLds _accountLds = AccountLds();

  @override
  void initState() {
    super.initState();
    isWorker = identityController.identityUS.isWorker;
    nativeAdVM.getVIPConfig(isWorker ? 2 : 1);

    // 监听身份变化
    _listenToIdentityChanges();
    // testNet();
  }

  /// 监听身份变化
  void _listenToIdentityChanges() {
    _identityWorker = ever(identityController.identityUS.currentIdentityRx, (UserIdentity identity) {
      // 重建页面
      setState(() {
        _workerPageKey = UniqueKey();
        _groupPageKey = UniqueKey();
        isWorker = identity == UserIdentity.worker;
      });

      // 更新广告配置
      nativeAdVM.getVIPConfig(isWorker ? 2 : 1);
      adVM.getLoadAdInterstitial(isWorker ? 2 : 1);
    });
  }

  @override
  void dispose() {
    _identityWorker.dispose();
    super.dispose();
  }

  @override
  void onPageShow() {
    super.onPageShow();
    print("AdConfig: main_page - onPageShow isWorker:$isWorker");
    // todo 后面改到记工页面的 initState 里加载
    adVM.getLoadAdInterstitial(isWorker ? 2 : 1);
  }

  void _logout() {
    _authRepo.logout();
    YPRoute.openPage(RouteNameCollection.login, clearStackAndPush: true);
  }

  // ==================== Token登录功能 - 独立模块开始 ====================

  /// 显示Token登录对话框
  void _showTokenLoginDialog() {
    final TextEditingController tokenController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Token登录'),
          content: TextField(
            controller: tokenController,
            decoration: const InputDecoration(
              hintText: '请输入token',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _tokenLogin(tokenController.text.trim());
              },
              child: const Text('登录'),
            ),
          ],
        );
      },
    );
  }

  /// Token登录逻辑
  Future<void> _tokenLogin(String token) async {
    if (token.isEmpty) {
      ToastUtil.showToast('请输入token');
      return;
    }

    ToastUtil.showLoading(message: '登录中...');

    try {
      final resp = await _authRds.memberGetYuapoInfo(MemberGetYuapoInfoAParamModel()
        ..token = token
        ..new_member = ""
        ..origin = ""
        ..refid = "");

      ToastUtil.hideLoading();

      if (resp.isOK()) {
        final data = resp.getSucData();
        if (data != null) {
          // 保存用户数据
          _accountLds.save(data);

          // 设置原生账户数据
          final account = _authRepo.getAccount();
          CallNativeApi().setAccountData(AccountData(
            userId: account.uid,
            token: account.token,
            uuid: account.uuid,
            singleSignToken: account.singletoken,
          ));

          // 调用enterDefault获取身份信息
          try {
            await _authRepo.enterDefault();
          } catch (e) {
            print('enterDefault failed: $e');
          }

          ToastUtil.showToast('登录成功');
        }
      } else {
        ToastUtil.showToast(resp.fail?.errorMsg ?? "登录失败");
      }
    } catch (e) {
      ToastUtil.hideLoading();
      ToastUtil.showToast('登录失败：$e');
    }
  }

  // ==================== Token登录功能 - 独立模块结束 ====================

  Future<void> testNet() async {
    print('----------------- start testNet');
    var jsonMap = {
      "identity": 2,
      "start_time": "2025-06-09",
      "end_time": "2025-06-09",
      "status": 0,
    };
    var res = await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/business/get-business-list',
            method: HTTP_METHOD.GET,
            content: jsonMap), (json) {
      print('----------------- testNet res: $json');
      return json.toString();
    });
    print(res);
  }

  @override
  Widget yBuild(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          child: Obx(
            () => RotationSwitchView(
              frontWidget: WorkerPage(key: _workerPageKey),
              backWidget: GroupPage(key: _groupPageKey),
              showFrontSide: identityController.identityUS.currentIdentity == UserIdentity.worker,
            ),
          ),
        ),
        Positioned(
            bottom: 200,
            right: 10,
            child: GestureDetector(
              onTap: _logout,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 10),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(0, 0, 0, 0.55),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '退出登录',
                  style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            )),
        // Token登录按钮 - 独立模块，便于后续移除
        Positioned(
            bottom: 100,
            right: 10,
            child: GestureDetector(
              onTap: _showTokenLoginDialog,
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 10),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(0, 0, 0, 0.55),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Token登录',
                  style: TextStyle(color: Colors.orange, fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
            )),
        Positioned(
            bottom: 150,
            right: 10,
            child: GestureDetector(
              onTap: () {
                // Get.toNamed(RouteNameCollection.test);
                YPRoute.openPage(RouteNameCollection.test);
                // YPRoute.openPage(RouteNameCollection.personalRecordWorkPoints);
                // Get.toNamed(Routes.test);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 10),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(0, 0, 0, 0.55),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '测试',
                  style: TextStyle(color: Colors.red, fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ),
            ))
      ],
    );
  }
}
