import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/tabbar/vm/identity_vm.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

/// 顶部——身份切换导航栏
class RoleSwitchBar extends StatelessWidget {
  final String rightText; // 右侧文案
  final String rightIconAsset; // 右侧icon资源路径
  final Color? backgroundColor; // 整体背景色
  final VoidCallback? onRightTap; // 右侧点击

  const RoleSwitchBar({
    super.key,
    required this.rightText,
    this.rightIconAsset = "",
    this.backgroundColor,
    this.onRightTap,
  });

  @override
  Widget build(BuildContext context) {
    final controller = IdentityVM.to;
    return Container(
      color: backgroundColor ?? Colors.white,
      child: SafeArea(
        top: false,
        bottom: false,
        child: Container(
          padding: const EdgeInsets.only(left: 16, right: 16, top: 5, bottom: 7),
          child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Obx(() => GestureDetector(
                    onTap: controller.isSwitching ? null : controller.showRoleSelectionDialog,
                    behavior: HitTestBehavior.opaque,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset('assets/images/common/icon_exchange.webp',
                            width: 30, height: 30),
                        const SizedBox(width: 10),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Obx(() => Text(
                                  '我是${controller.identityUS.currentIdentity.label}',
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    height: 1.4,
                                  ),
                                )),
                            Obx(() => Text(
                                  controller.identityUS.subTitle,
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: ColorsUtil.primaryColor,
                                  ),
                                )),
                          ],
                        ),
                      ],
                    ),
                  )),
                ),
                GestureDetector(
                  onTap: onRightTap,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                      color: const Color(0x265290fd),
                      borderRadius: BorderRadius.circular(14),
                    ),
                    child: Row(
                      children: [
                        if (rightIconAsset.isNotEmpty)
                          Image.asset(rightIconAsset, width: 18, height: 18),
                        const SizedBox(width: 4),
                        Text(
                          rightText,
                          style: TextStyle(
                            color: ColorsUtil.primaryColor,
                            fontSize: 14,
                            height: 1.3,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
        ),
      ),
    );
  }
}
