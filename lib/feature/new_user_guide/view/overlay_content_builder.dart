import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_day.dart';

import '../config/guide_step_ui_model.dart';
import '../guide_utils/widget_locator.dart';
import 'hollow_rect_view.dart';
import 'pass_through_view.dart';
import 'triangle_painter_view.dart';

/// 浮层内容构建器
/// 负责构建引导浮层的UI内容
class OverlayContentBuilder {
  final BuildContext _context; // 上下文

  OverlayContentBuilder(this._context);

  // 构建完整的引导浮层内容
  Widget build({
    required Rect targetRect, // 目标组件区域
    required Rect holeRect, // 镂空区域
    required GuideStepUIModel stepConfig, // 步骤配置
    required bool isVoiceOn, // 语音状态
    required GuideStepType stepType, // 语音状态
    required VoidCallback onNextStep, // 下一步回调
    required Function(String) onToggleVoice, // 切换语音回调
  }) {
    final screenSize = MediaQuery.of(_context).size;
    const cardHorizontalMargin = 16.0;
    final cardWidth = screenSize.width - 2 * cardHorizontalMargin;
    print("=====");

    // 获取今天日期的位置（用于日历步骤的手指动画）
    // 移除了静态GlobalKey
    final fingerRect = null; // 临时设为null

    return PassThroughView(
      passThroughAreas: stepConfig.shouldPassThrough ? [holeRect] : [],
      // onPassThroughTap: () {
      //   _removeOverlay(); // 点击穿透区域后关闭蒙层
      // },
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          _buildMask(holeRect), // 半透明蒙层（带镂空）
          // if (fingerRect != null) _buildFingerAnimation(fingerRect), // 手指动画
          // 引导卡片
          _buildHoleTapArea(holeRect, stepConfig, onNextStep), // 处理镂空区域点击穿透
          stepType != GuideStepType.guideCalenderBuJi
              ? _buildGuideCardA(
                  //常规引导样式
                  holeRect: holeRect,
                  stepConfig: stepConfig,
                  cardWidth: cardWidth,
                  cardHorizontalMargin: cardHorizontalMargin,
                  isVoiceOn: isVoiceOn,
                  onNextStep: onNextStep,
                  onToggleVoice: onToggleVoice,
                )
              : _buildGuideCardB(holeRect: holeRect) //补记样式
        ],
      ),
    );
  }

  // 构建半透明蒙层（带镂空效果）
  Widget _buildMask(Rect holeRect) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: CustomPaint(
        painter: HollowRectView(holeRect: holeRect),
      ),
    );
  }

  // 构建手指动画（引导用户点击）
  Widget _buildFingerAnimation(Rect rect) {
    return Positioned(
      left: rect.left - 10,
      top: rect.top + 10,
      child: Image.asset(
        'assets/images/worker/finger_run.gif',
        width: 50,
        height: 50,
        fit: BoxFit.contain,
      ),
    );
  }

  // 构建镂空区域的点击处理
  Widget _buildHoleTapArea(
    Rect holeRect,
    GuideStepUIModel stepConfig,
    VoidCallback onNextStep,
  ) {
    return Positioned.fromRect(
      rect: holeRect,
      child: stepConfig.shouldPassThrough
          ? Container() // 允许穿透时不处理点击
          : GestureDetector(
              onTap: onNextStep,
              behavior: HitTestBehavior.opaque,
              child: Container(color: Colors.transparent),
            ),
    );
  }

  // 构建引导卡片（包含标题、语音控制和下一步按钮）
  Widget _buildGuideCardA({
    required Rect holeRect,
    required GuideStepUIModel stepConfig,
    required double cardWidth,
    required double cardHorizontalMargin,
    required bool isVoiceOn,
    required VoidCallback onNextStep,
    required Function(String) onToggleVoice,
  }) {
    const triangleSize = 24.0;
    final screenSize = MediaQuery.of(_context).size;

    // 计算高亮区域上方和下方的可用空间
    final spaceBelow = screenSize.height - holeRect.bottom - 20;
    final spaceAbove = holeRect.top - 20;

    // 决定卡片显示位置（默认显示在下方）
    bool showBelow = spaceBelow > spaceAbove;

    // 如果上方空间明显更大，则显示在上方
    if (spaceAbove > spaceBelow + 100) {
      showBelow = false;
    }

    // 如果卡片高度可能超过可用空间，强制显示在上方或下方（根据剩余空间决定）
    const estimatedCardHeight = 150.0; // 估计卡片高度
    if (spaceBelow < estimatedCardHeight && spaceAbove >= estimatedCardHeight) {
      showBelow = false;
    } else if (spaceAbove < estimatedCardHeight &&
        spaceBelow >= estimatedCardHeight) {
      showBelow = true;
    }

    // 计算三角指示器的水平位置（根据stepConfig控制）
    double getTrianglePosition(double cardWidth) {
      switch (stepConfig.triangleAlignment) {
        case TriangleAlignment.center:
          return (cardWidth / 2 - triangleSize / 2) + 10;
        case TriangleAlignment.left:
          return 20; // 居左位置，距离左侧20像素
        case TriangleAlignment.right:
          return cardWidth - 20 - triangleSize; // 默认居左
      }
    }
    //卡片布局举例右侧间距
    double cardContainerRMargin =
        stepConfig.guideBGAlignment == TriangleAlignment.center
            ? cardHorizontalMargin
            : cardHorizontalMargin * 3;

    // 构建卡片内容
    final cardContent = Container(
      width: cardWidth,
      padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildVoiceAndStepRow(
            // 语音控制和步骤指示器
            stepConfig: stepConfig,
            isVoiceOn: isVoiceOn,
            onToggleVoice: onToggleVoice,
          ),
          const SizedBox(height: 6),
          Text(
            // 引导标题
            stepConfig.title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w400,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 2),
          const Text(
            '(点击小喇叭，可关闭语音)', // 辅助文本
            style: TextStyle(fontSize: 14, color: Colors.black87),
          ),
          const SizedBox(height: 12),
          _buildNextButton(stepConfig, onNextStep), // 下一步按钮
        ],
      ),
    );

    // 计算三角指示器位置
    final trianglePosition =
        getTrianglePosition(cardWidth - cardHorizontalMargin * 4);

    // 根据位置构建不同的布局
    if (showBelow) {
      // 显示在高亮区域下方
      return Positioned(
        top: holeRect.bottom + 20,
        left: cardHorizontalMargin,
        right: cardContainerRMargin,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // 倒三角指示器（指向目标组件）
            Positioned(
              left: trianglePosition,
              top: -triangleSize / 2 + 2,
              child: CustomPaint(
                size: Size(triangleSize * 1.5, triangleSize),
                painter: TrianglePainterView(color: Colors.white),
              ),
            ),
            cardContent,
          ],
        ),
      );
    } else {
      // 显示在高亮区域上方
      return Positioned(
        bottom: screenSize.height - holeRect.top + 20,
        left: cardHorizontalMargin,
        right: cardContainerRMargin,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // 正三角指示器（指向目标组件）
            Positioned(
              left: trianglePosition,
              bottom: -triangleSize / 2 + 2,
              child: Transform.rotate(
                angle: 3.14159, // 旋转180度
                child: CustomPaint(
                  size: Size(triangleSize * 1.5, triangleSize),
                  painter: TrianglePainterView(color: Colors.white),
                ),
              ),
            ),
            cardContent,
          ],
        ),
      );
    }
  }

  // 构建语音控制和步骤指示器行
  Widget _buildVoiceAndStepRow({
    required GuideStepUIModel stepConfig,
    required bool isVoiceOn,
    required Function(String) onToggleVoice,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          // 语音控制按钮
          onTap: () => onToggleVoice(stepConfig.voicePath),
          child: Image.asset(
            isVoiceOn
                ? 'assets/images/worker/waa_ic_guide_voice_on.gif' // 语音开启图标
                : 'assets/images/worker/waa_ic_guide_voice_off.webp', // 语音关闭图标
            height: 20,
          ),
        ),
        Text(
          // 步骤指示器
          stepConfig.step,
          style: const TextStyle(fontSize: 14, color: Colors.grey),
        ),
      ],
    );
  }

  // 构建下一步按钮
  Widget _buildNextButton(GuideStepUIModel config, VoidCallback onNext) {
    return Align(
      alignment: Alignment.centerRight,
      child: ElevatedButton(
        onPressed: onNext,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF5290FD),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 6),
          minimumSize: Size(30, 30),
          // 设置最小尺寸
          maximumSize: Size(double.infinity, 30), // 设置最大高度
        ),
        child: Text(config.isFinish ? '开始记工' : '下一步'),
      ),
    );
  }

// 构建快速补记引导卡片
  Widget _buildGuideCardB({
    required Rect holeRect,
  }) {
    const triangleSize = 24.0;
    final screenSize = MediaQuery.of(_context).size;

    // 构建卡片内容
    final cardContent = Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.blue,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '选择日期，快速进行补记',
            style: const TextStyle(fontSize: 18, color: Colors.white),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );

    // 外部文本（靠左显示，左侧间距30）
    final externalText = Expanded(
      child: Padding(
        padding: const EdgeInsets.only(top: 10), // 顶部10间距，左侧30间距
        child: Text(
          '在日历中通过图标，可快速查看记工内容…',
          style: TextStyle(
            fontSize: 14,
            color: Colors.white,
          ),
          textAlign: TextAlign.left, // 文本左对齐
        ),
      ),
    );

    // 右侧图标（水平对齐）
    final rightIcon = Padding(
      padding: const EdgeInsets.only(top: 10, left: 20, right: 10), // 与文本保持间距
      child: Image.asset(
        'assets/images/worker/ic_gdjg_buji_calendar.webp', // 替换为实际图标路径
        width: 50,
        height: 50,
      ),
    );

    // 文本和图标组合
    final textAndIconRow = Row(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start, // 顶部对齐
      children: [
        externalText, // 左侧文本
        rightIcon, // 右侧图标
      ],
    );

    // 自适应宽度的卡片+外部文本组合
    final contentWithExternalText = Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start, // 整体内容靠左对齐
      children: [
        Center(child: cardContent), // 卡片保持居中
        textAndIconRow, // 文本和图标组合
      ],
    );

    // 限制最大宽度，避免内容过宽
    final constrainedContent = ConstrainedBox(
      constraints: BoxConstraints(
        maxWidth: screenSize.width * 0.8,
      ),
      child: contentWithExternalText,
    );

    return Positioned(
      top: holeRect.bottom + 20,
      left: 0,
      right: 0,
      child: Center(
        child: Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.topCenter,
          children: [
            // 倒三角指示器（屏幕水平居中）
            Positioned(
              top: -triangleSize / 2 + 2,
              child: CustomPaint(
                size: Size(triangleSize * 1.5, triangleSize),
                painter: TrianglePainterView(color: Colors.blue),
              ),
            ),
            // 卡片+外部文本组合（整体居中，但文本靠左）
            constrainedContent,
          ],
        ),
      ),
    );
  }
}
