import 'package:gdjg_pure_flutter/data/group_data/on_site_workers/repo/on_site_workers_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/on_site_workers/ds/model/param/worker_list_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/download_workers_material/repo/download_workers_material_repo.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/download_workers_material/entity/download_workers_material_props.dart';
import 'package:gdjg_pure_flutter/utils/wechat_util/wechat_util.dart';

import 'download_workers_material_us.dart';

/// 下载工友资料业务逻辑
class DownloadWorkersMaterialVM {
  final us = DownloadWorkersMaterialUS();
  final _repo = OnSiteWorkersRepo();
  final _downloadRepo = DownloadWorkersMaterialRepo();
  DownloadWorkersMaterialProps? _props;

  void init(DownloadWorkersMaterialProps? props) {
    _props = props;
    fetch();
  }

  Future<void> fetch() async {
    // 获取所有未删除的工友数据
    final param = WorkerListDeptParamModel(
      dept_id: _props?.deptId,
      is_deleted: '0',
    );
    final re = await _repo.getWorkerList(param);
    if (re.isOK()) {
      final data = re.getSucData();
      if (data != null) {
        // 显示所有工友（在场+休假）
        us.setList(data.noteWorker);
      }
    }
  }

  void toggle(double workerId) => us.toggle(workerId);
  void setAll(bool all) => us.setAll(all);
  void onSearchChanged(String query) => us.setSearch(query);

  /// 批量下载工友资料
  Future<bool> batchDownload() async {
    if (us.selected.isEmpty) return false;

    final workerIds = us.selected.map((id) => id.toInt().toString()).toList();

    // 调用下载接口
    final result = await _downloadRepo.downloadWorkers(
      deptId: _props?.deptId,
      workerIds: workerIds,
    );

    if (result.isOK()) {
      final data = result.getSucData();
      if (data != null && data.url.isNotEmpty) {
        // 调用微信分享功能
        final success = await WeChatUtil.shareFile(url: data.url, title: data.filename);
        return success;
      }
    }

    return false;
  }
}
