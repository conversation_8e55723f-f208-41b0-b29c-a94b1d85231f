import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/on_site_workers/mass_operation/entity/mass_operation_props.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/feature/income_expenses/utils/str_utils.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';

import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/widget/divider_view.dart';
import '../vm/on_site_workers_vm.dart';
import 'package:gdjg_pure_flutter/data/group_data/on_site_workers/repo/model/worker_list_dept_biz_model.dart';

/// 在场 Tab card列表
class OnSiteListTab extends StatelessWidget {
  final OnSiteWorkersVM vm;
  const OnSiteListTab({super.key, required this.vm});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Obx(() {
            final list = vm.us.onSiteList;
            if (list.isEmpty) {
              return _buildEmptyView();
            }
            return ListView.separated(
              itemCount: list.length,
              separatorBuilder: (_, __) => const DividerView(),
              itemBuilder: (context, index) {
                final item = list[index];
                return _buildItem(context, item);
              },
            );
          }),
        ),
        _buildBottomButtons(),
      ],
    );
  }

  Widget _buildItem(BuildContext context, NoteWorkerBizModel item) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        children: [
          _buildAvatar(item),
          SizedBox(width: 12.w),
          Expanded(child: _buildMainInfo(item)),
          _buildSettingIcon(),
        ],
      ),
    );
  }

  Widget _buildAvatar(NoteWorkerBizModel item) {
    return Container(
      width: 48.w,
      height: 48.w,
      decoration: BoxDecoration(
        color: ColorsUtil.primaryColor,
        borderRadius: BorderRadius.circular(4.r),
      ),
      alignment: Alignment.center,
      child: (item.avatar.isNotEmpty)
          ? ClipRRect(
              borderRadius: BorderRadius.circular(4.r),
              child: Image.network(item.avatar, width: 48.w, height: 48.w, fit: BoxFit.cover),
            )
          : Text(
              item.name.isNotEmpty ? (item.name.lastTwoChars ?? '') : '',
              style: TextStyle(fontSize: 18.sp, color: Colors.white, fontWeight: FontWeight.w600),
            ),
    );
  }

  Widget _buildMainInfo(NoteWorkerBizModel item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          StrUtils.getMaxLength(item.name, 13),
          style: TextStyle(fontSize: 16.sp, color: const Color(0xFF222222)),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: 2.h),
        Text(item.tel, style: TextStyle(fontSize: 14.sp, color: ColorsUtil.black45)),
        if (item.isGrant <= 0) ...[
          SizedBox(height: 4.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: const Color(0xFFF2F2F2),
              borderRadius: BorderRadius.circular(2.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Image.asset(Assets.commonIcInformation, width: 14.w, height: 14.w),
                SizedBox(width: 2.w),
                Text('名片未授权', style: TextStyle(fontSize: 12.sp, color: ColorsUtil.black45)),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildSettingIcon() {
    return Image.asset(Assets.commonIcProjectSetting, width: 20.w, height: 20.w);
  }

  Widget _buildEmptyView() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: 80.h),
      child: Column(
        children: [
          Image.asset(
            Assets.groupIcEmptyViewNoStaff,
            width: 140.w,
            height: 140.w,
          ),
          Text(
            '暂无工友',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF999999),
            ),
          ),
          SizedBox(height: 20.h),
          SizedBox(
            width: 200.w,
            child: ButtonUtil.buildCommonButton(
              text: '去添加',
              onPressed: () {},
              textStyle: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(16.w, 10.h, 16.w, 6.h),
      child: Row(children: [
        Expanded(
          child: GestureDetector(
            onTap: () {
              YPRoute.openPage(RouteNameCollection.massLeave, params: MassOperationProps(
                deptId: vm.props?.deptId,
                workNoteName: vm.props?.workNoteName,
                workNoteId: vm.props?.workNoteId,
              ));
            },
            child: Container(
              height: 44.h,
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(5.w),
                border: Border.all(
                  color: ColorsUtil.primaryColor,
                  width: 1,
                ),
              ),
              alignment: Alignment.center,
              child: Text(
                '批量退场/休假',
                style: TextStyle(
                  color: ColorsUtil.primaryColor,
                  fontSize: 17.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
            child: ButtonUtil.buildCommonButton(
          text: '添加工友',
          onPressed: () {},
          textStyle: TextStyle(
            color: Colors.white,
            fontSize: 17.sp,
            fontWeight: FontWeight.w400,
          ),
        )),
      ]),
    );
  }

}

