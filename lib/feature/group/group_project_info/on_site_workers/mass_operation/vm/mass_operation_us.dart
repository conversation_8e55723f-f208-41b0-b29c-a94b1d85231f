import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/data/group_data/on_site_workers/repo/model/worker_list_dept_biz_model.dart';

/// 批量退场/休假UI状态管理
class MassOperationUS {
  final _allList = <NoteWorkerBizModel>[]; // 原始数据
  final _list = <NoteWorkerBizModel>[].obs; // 显示数据
  final _selected = <double>[].obs; // 使用workerId列表
  final _search = ''.obs;

  RxList<NoteWorkerBizModel> get list => _list;
  RxList<double> get selected => _selected;
  String get search => _search.value;

  void setList(List<NoteWorkerBizModel> data) {
    _allList.clear();
    _allList.addAll(data);
    _filterList();
  }

  void clearSelected() {
    _selected.clear();
  }

  void removeWorkers(List<double> workerIds) {
    // 从原始数据中移除
    _allList.removeWhere((worker) => workerIds.contains(worker.workerId));
    // 重新过滤显示数据
    _filterList();
  }

  void setSearch(String query) {
    _search.value = query;
    _filterList();
  }

  void _filterList() {
    if (_search.value.isEmpty) {
      _list.assignAll(_allList);
    } else {
      final query = _search.value.toLowerCase();
      final filtered = _allList.where((item) {
        return item.name.toLowerCase().contains(query) ||
               item.tel.contains(query);
      }).toList();
      _list.assignAll(filtered);
    }
  }

  void toggle(double workerId) {
    if (_selected.contains(workerId)) {
      _selected.remove(workerId);
    } else {
      _selected.add(workerId);
    }
  }

  void setAll(bool all) {
    if (all) {
      _selected.assignAll(_list.map((e) => e.workerId));
    } else {
      _selected.clear();
    }
  }

  bool get isAllSelected => _list.isNotEmpty && _selected.length == _list.length;
}
