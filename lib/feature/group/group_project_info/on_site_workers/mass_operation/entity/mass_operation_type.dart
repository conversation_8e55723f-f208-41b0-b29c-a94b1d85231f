enum MassOperationType {
  /// 批量退场/休假（从在场工友进入）
  leave,
  
  /// 批量出勤（从休假工友进入）
  attendance,
  
  /// 批量退场（从休假工友进入）
  leaveFromVacation,
}

extension MassOperationTypeExtension on MassOperationType {
  /// 获取工友列表标签
  String get workerListTitle {
    switch (this) {
      case MassOperationType.leave:
        return '出勤工友';
      case MassOperationType.attendance:
      case MassOperationType.leaveFromVacation:
        return '休假工友';
    }
  }
  
  /// 是否需要获取休假工友数据
  bool get needVacationWorkers {
    switch (this) {
      case MassOperationType.leave:
        return false;
      case MassOperationType.attendance:
      case MassOperationType.leaveFromVacation:
        return true;
    }
  }
}
