import 'mass_operation_type.dart';

/// 批量退场/休假页面参数
class MassOperationProps {
  final String? deptId;
  final String? workNoteName;
  final String? workNoteId;
  final MassOperationType operationType;

  MassOperationProps({
    this.deptId,
    this.workNoteName,
    this.workNoteId,
    this.operationType = MassOperationType.leave,
  });

  @override
  String toString() {
    return 'MassOperationProps{deptId: $deptId, workNoteName: $workNoteName, workNoteId: $workNoteId, operationType: $operationType}';
  }
}
