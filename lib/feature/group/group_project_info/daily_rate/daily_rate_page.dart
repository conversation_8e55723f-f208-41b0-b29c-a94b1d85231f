import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/daily_rate/entity/daily_rate_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/daily_rate/vm/daily_rate_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/daily_rate/widget/worker_rate_item.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/SearchInput.dart';
import 'package:get/get.dart';

class DailyRatePage extends BaseFulPage {
  const DailyRatePage({super.key}) : super(appBar: const YPAppBar(title: "点工工价"));

  @override
  createState() => _DailyRatePageState();
}

class _DailyRatePageState extends BaseFulPageState {
  late final DailyRateViewModel _viewModel;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    DailyRateProps? props = routeParams as DailyRateProps?;
    _viewModel = DailyRateViewModel();
    _viewModel.init(props);
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _viewModel.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 点击空白区域取消输入框焦点
        FocusScope.of(context).unfocus();
      },
      child: Container(
        color: ColorsUtil.ypBgColor,
        child: Column(
          children: [
            // 搜索栏
            _buildSearchSection(),
            // 提示栏
            _buildTips(),
            // 工友列表
            Expanded(
              child: Container(
                color: Colors.white,
                child: _buildWorkerList(),
              ),
            ),
            // 全选和批量设置
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  /// 搜索栏
  Widget _buildSearchSection() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(8.w),
      child: SearchInput(
        backgroundColor: Color(0xFFF4F8FF),
        hintText: '请输入工友姓名/手机号码',
        prefixIcon: Image.asset(
          'assets/images/common/icon_search.png',
          color: Colors.black,
        ),
        onChanged: (value) => _viewModel.searchWorkers(value),
      ),
    );
  }

  /// 提示栏
  Widget _buildTips() {
    return Container(
      color: Color(0xFFF4F8FF),
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 7.h),
      child: Row(
        children: [
          Text(
            "*记工时会按此工价自动算工资",
            style: TextStyle(
              color: ColorsUtil.primaryColor,
              fontSize: 14.sp,
              fontWeight: FontWeight.normal,
            ),
            textAlign: TextAlign.left,
          ),
        ],
      ),
    );
  }

  /// 工友列表
  Widget _buildWorkerList() {
    return Obx(() {
      final workers = _viewModel.us.filteredWorkerList;
      if (workers.isEmpty) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.only(top: 90.h, bottom: 50.h),
          child: Column(
            children: [
              Image.asset(
                Assets.commonIconEmptyTeamProject,
                width: 140.w,
                height: 140.w,
                fit: BoxFit.contain,
              ),
              Text(
                '暂无数据',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF8A8A99),
                ),
              ),
            ],
          ),
        );
      }

      return ListView.builder(
        itemCount: workers.length,
        itemBuilder: (context, index) {
          final worker = workers[index];
          return Obx(() {
            return WorkerRateItem(
              worker: worker,
              isSelected: _viewModel.us.selectedWorkerIds.contains(worker.workerId.toInt()),
              onSelectTap: () => _viewModel.toggleWorkerSelection(worker.workerId.toInt()),
              onWageSettingTap: () => _viewModel.showSingleWorkerWageDialog(
                worker.workerId.toInt(),
                worker.name,
              ),
            );
          });
        },
      );
    });
  }

  /// 全选和批量设置
  Widget _buildBottomActions() {
    return Obx(() {
      return Container(
        color: Colors.white,
        padding: EdgeInsets.all(6.w),
        child: SafeArea(
          child: Row(
            children: [
              GestureDetector(
                onTap: _viewModel.toggleSelectAll,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 20.w,
                      height: 20.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: _viewModel.us.isSelectAll ? ColorsUtil.primaryColor : ColorsUtil.greyColor,
                          width: 1.5.w,
                        ),
                        color: _viewModel.us.isSelectAll ? ColorsUtil.primaryColor : Colors.transparent,
                      ),
                      child: _viewModel.us.isSelectAll
                          ? Icon(
                              Icons.check,
                              size: 14.w,
                              color: Colors.white,
                            )
                          : null,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      '全选',
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: ColorsUtil.black85,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: 100.w),
              Expanded(
                child: ButtonUtil.buildCommonButton(
                  height: 40.h,
                  text: _viewModel.us.hasSelectedWorkers
                      ? '批量设置(${_viewModel.us.selectedCount})'
                      : '批量设置',
                  fontSize: 16.0,
                  textStyle: TextStyle(
                    color: Colors.white,
                    fontSize: 16.sp,
                    fontWeight: FontWeight.normal,
                  ),
                  onPressed: _viewModel.showBatchWageDialog,
                  enabled: _viewModel.us.hasSelectedWorkers,
                  backgroundColor: _viewModel.us.hasSelectedWorkers
                      ? ColorsUtil.primaryColor
                      : Color(0xFF86BFFF),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
