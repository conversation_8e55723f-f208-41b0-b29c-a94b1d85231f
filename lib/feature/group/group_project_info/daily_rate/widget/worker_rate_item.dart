import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/on_site_workers/repo/model/worker_list_dept_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 工友工价列表项组件
class WorkerRateItem extends StatelessWidget {
  final NoteWorkerBizModel worker;
  final bool isSelected;
  final VoidCallback? onSelectTap;
  final VoidCallback? onWageSettingTap;

  const WorkerRateItem({
    super.key,
    required this.worker,
    this.isSelected = false,
    this.onSelectTap,
    this.onWageSettingTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onSelectTap,
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 12.h),
              child: Row(
                children: [
                  // 选择框
                  _buildSelectBox(),
                  SizedBox(width: 12.w),
                  // 工友姓名
                  Expanded(
                    child: Text(
                      worker.name,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: ColorsUtil.black85,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  SizedBox(width: 6.w),
                  // 工价显示
                  GestureDetector(
                    onTap: onWageSettingTap,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildWageDisplayWidget(),
                        SizedBox(width: 4.w),
                        Image.asset(
                          'assets/images/common/ic_setting_2.png',
                          width: 16.w,
                          height: 16.w,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // 分割线
            Container(
              height: 0.5.h,
              color: ColorsUtil.divideLineColor,
            ),
          ],
        ),
      ),
    );
  }

  /// 选择框
  Widget _buildSelectBox() {
    return GestureDetector(
      onTap: onSelectTap,
      child: Container(
        width: 20.w,
        height: 20.w,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: isSelected ? ColorsUtil.primaryColor : ColorsUtil.greyColor,
            width: 1.5.w,
          ),
          color: isSelected ? ColorsUtil.primaryColor : Colors.transparent,
        ),
        child: isSelected
            ? Icon(
                Icons.check,
                size: 14.w,
                color: Colors.white,
              )
            : null,
      ),
    );
  }

  /// 格式化数字显示
  String _formatNumber(double number) {
    if (number == number.toInt()) {
      return number.toInt().toString();
    }
    String formatted = number.toStringAsFixed(2);
    formatted = formatted.replaceAll(RegExp(r'\.?0+$'), '');
    return formatted;
  }

  /// 工价显示Widget
  Widget _buildWageDisplayWidget() {
    if (worker.isWorkerFee != 1 || worker.workerFee == null) {
      return Text(
        '未设置',
        style: TextStyle(
          fontSize: 13.sp,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF666666),
        ),
      );
    }

    final fee = worker.workerFee!;
    String baseWageText = '1个工${_formatNumber(fee.workingHoursStandard)}小时${_formatNumber(fee.workingHoursPrice)}元';

    // 检查是否有加班信息
    if (fee.overtimeType == 0 ||
        (fee.overtimeHoursStandard == 0 && fee.overtimeHoursPrice == 0)) {
      return Text(
        baseWageText,
        style: TextStyle(
          fontSize: 13.sp,
          fontWeight: FontWeight.w400,
          color: const Color(0xFF666666),
        ),
      );
    }

    // 加班信息
    String overtimeText = '';
    if (fee.overtimeType == 1) {
      String formattedStandard = _formatNumber(fee.overtimeHoursStandard);
      overtimeText = '加班$formattedStandard小时1个工';
    } else if (fee.overtimeType == 2) {
      String formattedPrice = _formatNumber(fee.overtimeHoursPrice);
      overtimeText = '加班1小时$formattedPrice元';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          baseWageText,
          style: TextStyle(
            fontSize: 13.sp,
            fontWeight: FontWeight.w400,
            color: const Color(0xFF666666),
          ),
        ),
        if (overtimeText.isNotEmpty) ...[
          Text(
            overtimeText,
            style: TextStyle(
              fontSize: 13.sp,
              fontWeight: FontWeight.w400,
              color: const Color(0xFF666666),
            ),
          ),
        ],
      ],
    );
  }
}
