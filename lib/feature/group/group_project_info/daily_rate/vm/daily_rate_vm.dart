import 'package:gdjg_pure_flutter/data/group_data/on_site_workers/ds/model/param/worker_list_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/on_site_workers/repo/on_site_workers_repo.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/WageRulesSettingDialog.dart';
import 'package:gdjg_pure_flutter/feature/group/dialog/wage_rule_setting/entity/wage_rules_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/daily_rate/entity/daily_rate_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/daily_rate/vm/daily_rate_us.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:get/get.dart';

/// 点工工价页面ViewModel
class DailyRateViewModel {
  final DailyRateUS us = DailyRateUS();
  final OnSiteWorkersRepo _workerRepo = OnSiteWorkersRepo();

  /// 工作记录ID
  String? workNoteId;

  /// 工作记录名称
  String? workNoteName;

  /// 初始化
  void init(DailyRateProps? props) {
    final deptId = props?.deptId ?? '';
    workNoteId = props?.workNoteId;
    workNoteName = props?.workNoteName;

    us.setDeptId(deptId);
    fetchWorkerList();
  }

  /// 获取工友列表
  Future<void> fetchWorkerList() async {
    if (us.deptId.isEmpty) {
      ToastUtil.showToast('项目ID不能为空');
      return;
    }

    try {
      final params = WorkerListDeptParamModel(
        dept_id: us.deptId,
        is_deleted: '0',
        businessType: '1',
        fee: '1',
      );

      final result = await _workerRepo.getWorkerList(params);
      if (result.isOK()) {
        final bizModel = result.getSucData();
        final workers = bizModel?.noteWorker ?? [];
        us.setWorkerList(workers);
      } else {
        ToastUtil.showToast('获取工友列表失败');
      }
    } catch (e) {
      ToastUtil.showToast('获取工友列表异常: $e');
    }
  }

  /// 搜索工友
  void searchWorkers(String keyword) {
    us.setSearchKeyword(keyword);
  }

  /// 全选/取消全选
  void toggleSelectAll() {
    us.setSelectAll(!us.isSelectAll);
  }

  /// 选择/取消选择工友
  void toggleWorkerSelection(int workerId) {
    us.toggleWorkerSelection(workerId);
  }

  /// 单个工友工价设置
  void showSingleWorkerWageDialog(int workerId, String workerName) {
    final worker = us.workerList.firstWhereOrNull((w) => w.workerId.toInt() == workerId);
    if (worker == null) return;

    final props = WageRulesProps(
      title: workerName,
      workNoteId: workNoteId ?? '',
      businessType: RwaRecordType.workDays,
      recordNoteType: RecordNoteType.group,
      deleteRequestApi: false,
      confirmRequestApi: true,
      isShowDelete: false,
      workers: [
        WorkerModel(
          workerId: workerId.toDouble(),
          workerName: workerName,
        )
      ],
    );

    WageRulesSettingDialog.show(
      props: props,
      onSelected: (feeInfo) {
        ToastUtil.showToast('工价设置成功');
        fetchWorkerList(); // 刷新列表
      },
    );
  }

  /// 批量工价设置
  void showBatchWageDialog() {
    if (!us.hasSelectedWorkers) {
      ToastUtil.showToast('请先选择工友');
      return;
    }

    // 获取选中的工友信息
    final selectedWorkers = us.filteredWorkerList
        .where((worker) => us.selectedWorkerIds.contains(worker.workerId.toInt()))
        .map((worker) => WorkerModel(
              workerId: worker.workerId,
              workerName: worker.name,
            ))
        .toList();

    final props = WageRulesProps(
      title: '批量设置点工工价 (${us.selectedCount}人)',
      workNoteId: workNoteId ?? '',
      businessType: RwaRecordType.workDays,
      recordNoteType: RecordNoteType.group,
      deleteRequestApi: false,
      confirmRequestApi: true,
      isShowDelete: false,
      workers: selectedWorkers,
    );

    WageRulesSettingDialog.show(
      props: props,
      onSelected: (feeInfo) {
        ToastUtil.showToast('批量设置成功');
        us.clearSelection();
        fetchWorkerList(); // 刷新列表
      },
    );
  }

  /// 刷新数据
  void refresh() {
    fetchWorkerList();
  }

  /// 清空搜索
  void clearSearch() {
    us.setSearchKeyword('');
  }

  /// 销毁
  void dispose() {
    us.reset();
  }
}