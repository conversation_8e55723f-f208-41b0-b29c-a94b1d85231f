import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/data/group_data/on_site_workers/repo/model/worker_list_dept_biz_model.dart';

/// 点工工价页面UI状态管理
class DailyRateUS {
  /// 工友列表
  final _workerList = <NoteWorkerBizModel>[].obs;

  /// 过滤后的工友列表
  final _filteredWorkerList = <NoteWorkerBizModel>[].obs;

  /// 搜索关键词
  final _searchKeyword = ''.obs;

  /// 是否全选
  final _isSelectAll = false.obs;

  /// 选中的工友ID列表
  final _selectedWorkerIds = <int>[].obs;

  /// 批量设置的工价
  final _batchWorkPrice = ''.obs;

  /// 批量设置的加班工价
  final _batchOvertimePrice = ''.obs;

  /// 项目ID
  final _deptId = ''.obs;

  // Getters
  List<NoteWorkerBizModel> get workerList => _workerList;
  List<NoteWorkerBizModel> get filteredWorkerList => _filteredWorkerList;
  String get searchKeyword => _searchKeyword.value;
  bool get isSelectAll => _isSelectAll.value;
  RxList<int> get selectedWorkerIds => _selectedWorkerIds;
  String get batchWorkPrice => _batchWorkPrice.value;
  String get batchOvertimePrice => _batchOvertimePrice.value;
  String get deptId => _deptId.value;

  /// 选中的工友数量
  int get selectedCount => selectedWorkerIds.length;

  /// 是否有选中的工友
  bool get hasSelectedWorkers => selectedWorkerIds.isNotEmpty;

  // Setters
  void setWorkerList(List<NoteWorkerBizModel> list) {
    _workerList.value = list;
    _updateFilteredList();
  }

  void setSearchKeyword(String keyword) {
    _searchKeyword.value = keyword;
    _updateFilteredList();
  }

  /// 更新过滤后的工友列表
  void _updateFilteredList() {
    if (_searchKeyword.value.isEmpty) {
      _filteredWorkerList.value = List.from(_workerList);
    } else {
      _filteredWorkerList.value = _workerList.where((worker) =>
        worker.name.contains(_searchKeyword.value) ||
        worker.tel.contains(_searchKeyword.value)
      ).toList();
    }
  }

  void setSelectAll(bool value) {
    _isSelectAll.value = value;
    if (value) {
      _selectedWorkerIds.value = filteredWorkerList.map((e) => e.workerId.toInt()).toList();
    } else {
      _selectedWorkerIds.clear();
    }
  }

  void toggleWorkerSelection(int workerId) {
    if (_selectedWorkerIds.contains(workerId)) {
      _selectedWorkerIds.remove(workerId);
    } else {
      _selectedWorkerIds.add(workerId);
    }
    // 更新全选状态
    _isSelectAll.value = _selectedWorkerIds.length == filteredWorkerList.length;
  }

  void setBatchWorkPrice(String price) {
    _batchWorkPrice.value = price;
  }

  void setBatchOvertimePrice(String price) {
    _batchOvertimePrice.value = price;
  }

  void setDeptId(String id) {
    _deptId.value = id;
  }

  /// 清空选择
  void clearSelection() {
    _selectedWorkerIds.clear();
    _isSelectAll.value = false;
  }

  /// 重置状态
  void reset() {
    _workerList.clear();
    _filteredWorkerList.clear();
    _searchKeyword.value = '';
    _selectedWorkerIds.clear();
    _isSelectAll.value = false;
    _batchWorkPrice.value = '';
    _batchOvertimePrice.value = '';
  }
}