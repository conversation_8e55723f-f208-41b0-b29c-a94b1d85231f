import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/shift_lead_setup/entity/shift_lead_setup_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/shift_lead_setup/vm/shift_lead_setup_vm.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';

class ShiftLeadSetupPage extends BaseFulPage {
  const ShiftLeadSetupPage({super.key}) : super(appBar: const YPAppBar(title: "带班设置"));

  @override
  State<ShiftLeadSetupPage> createState() => _ShiftLeadSetupPageState();
}

class _ShiftLeadSetupPageState extends BaseFulPageState<ShiftLeadSetupPage> {
  final viewModel = ShiftLeadSetupViewModel();

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    final props = routeParams as ShiftLeadSetupProps?;
    if (props != null) {
      dynamicTitle = props.workNoteName ?? '带班设置';
      viewModel.init(props);
    }
  }

  @override
  void onPageShow() {
    super.onPageShow();
    viewModel.refresh();
  }

  @override
  Widget yBuild(BuildContext context) {

    return Obx(() {
      return Container(
        margin: EdgeInsets.only(top: 8.h),
        color: Colors.white,
        child: Column(
          children: [
            _buildHeader(viewModel.us.workers.length),
            Expanded(child: _buildWorkerList(viewModel.us.workers)),
            if (viewModel.us.workers.isNotEmpty) _buildBottomButtons(),
          ],
        ),
      );
    });
  }

  Widget _buildHeader(int count) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
      child: Row(
        children: [
          Text(
            "带班工友($count)",
            style: TextStyle(
              fontSize: 20.sp,
              //fontWeight: FontWeight.bold,
              color: const Color(0xFF323232),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyView() {
    return Container(
      padding: EdgeInsets.only(top: 80.h),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Image.asset(Assets.commonIconEmptyTeamProject, width: 140.w, height: 140.w),
          Text(
            '暂无带班工友',
            style: TextStyle(fontSize: 14.sp, color: ColorsUtil.black45),
          ),
          SizedBox(height: 20.h),
          SizedBox(
            width: 200.w,
            child: ButtonUtil.buildCommonButton(
              text: '去添加',
              onPressed: () {},
              textStyle: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkerList(List workers) {
    if (workers.isEmpty) {
      return _buildEmptyView();
    }

    return ListView.separated(
      itemCount: workers.length,
      separatorBuilder: (context, index) => Container(
        height: 0.5.h,
        color: const Color(0xFFF5F6FA),
      ),
      itemBuilder: (context, index) {
        final worker = workers[index];
        return _buildWorkerItem(worker);
      },
    );
  }

  Widget _buildWorkerItem(worker) {
    return GestureDetector(
      onTap: () => viewModel.onWorkerSetting(worker.id.toInt()),
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.fromLTRB(16.w, 14.h, 10.w, 14.h),
        child: Row(
          children: [
            Container(
              width: 50.w,
              height: 50.h,
              decoration: BoxDecoration(
                color: Colors.blue,
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Center(
                child: Text(
                  worker.name.length >= 2
                      ? worker.name.substring(worker.name.length - 2)
                      : worker.name,
                  style: TextStyle(color: Colors.white, fontSize: 16.sp),
                ),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    worker.name,
                    style: TextStyle(
                      color: ColorsUtil.black85,
                      fontSize: 15.sp,
                      fontWeight: FontWeight.w400,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    worker.tel,
                    style: TextStyle(
                      fontSize: 13.sp,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            Image.asset(
              Assets.visitorIcMineFuncSetting,
              width: 14.w,
              height: 14.w,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: EdgeInsets.fromLTRB(16.w, 16.w, 16.w, 4.w),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: viewModel.onAddShiftLead,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 10.h),
                decoration: BoxDecoration(
                  color: const Color(0xFF007AFF),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '添加带班',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.white,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Image.asset(
                        Assets.groupIcAddShiftLead,
                        width: 16.w,
                        height: 16.w,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: GestureDetector(
              onTap: viewModel.onDeleteShiftLead,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 10.h),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.red),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '删除带班',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.red,
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Image.asset(
                        Assets.workerIconDelete,
                        width: 16.w,
                        height: 16.w,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}