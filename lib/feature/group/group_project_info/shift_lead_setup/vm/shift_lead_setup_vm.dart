import 'package:gdjg_pure_flutter/data/group_data/shift_lead_setting/repo/shift_lead_setting_repo.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/shift_lead_setup/entity/shift_lead_setup_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/shift_lead_setup/shift_lead_delete/entity/shift_lead_delete_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/shift_lead_setup/shift_lead_info/entity/shift_lead_info_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/shift_lead_setup/vm/shift_lead_setup_us.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';

class ShiftLeadSetupViewModel {
  final ShiftLeadSettingRepo _repo = ShiftLeadSettingRepo();
  final us = ShiftLeadSetupUS();

  late ShiftLeadSetupProps props;

  void init(ShiftLeadSetupProps props) {
    this.props = props;
    _loadShiftLeadWorkers();
  }

  Future<void> _loadShiftLeadWorkers() async {
    if (props.deptId == null) return;

    final cleanDeptId = props.deptId!.trimTrailingZeros();
    final result = await _repo.fetchShiftLeadWorkers(cleanDeptId);

    if (result.isOK()) {
      final workers = result.getSucData()?.list ?? [];
      us.setWorkers(workers);
    }
  }

  void onAddShiftLead() {
    // TODO: 实现添加带班逻辑
  }

  void onDeleteShiftLead() {
    YPRoute.openPage("/group/shift_lead_delete", params: ShiftLeadDeleteProps(
      deptId: props.deptId,
      workNoteName: props.workNoteName,
    ));
  }

  void onWorkerSetting(int workerId) {
    // 获取工友信息
    final worker = us.workers.firstWhere(
      (w) => w.id.toInt() == workerId,
      orElse: () => us.workers.first,
    );

    YPRoute.openPage(RouteNameCollection.shiftLeadInfo, params: ShiftLeadInfoProps(
      workerId: workerId,
      deptId: props.deptId,
      workerName: worker.name,
      workNoteName: props.workNoteName,
    ));
  }

  /// 刷新数据
  Future<void> refresh() async {
    await _loadShiftLeadWorkers();
  }
}