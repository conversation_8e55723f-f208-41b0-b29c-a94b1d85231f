import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/data/group_data/shift_lead_setting/repo/model/workers_get_worker_info_biz_model.dart';

/// 带班工友信息页面UI状态管理
class ShiftLeadInfoUS {
  /// 工友详细信息
  final _workerInfo = Rxn<WorkersGetWorkerInfoBizModel>();

  // Getters
  WorkersGetWorkerInfoBizModel? get workerInfo => _workerInfo.value;

  // Setters
  void setWorkerInfo(WorkersGetWorkerInfoBizModel? info) {
    _workerInfo.value = info;
  }
}
