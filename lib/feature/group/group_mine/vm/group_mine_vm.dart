import 'package:gdjg_pure_flutter/feature/group/group_mine/vm/mine_model_converter.dart';

import '../../../../data/mine/repo/mine_repo.dart';
import '../../../../utils/user_info/user_info_utils.dart';
import '../us/group_mine_us.dart';
import '../utils/item_data_utils.dart';

class GroupMineVm {
  final us = GroupMineUs();
  final _repo = MineRepo();

  final _converter = MineModelConverter();

  void initData() {
    //leader
    final identity = UserInfoUtils.getIdentity();
    String role = identity.stringValue;
    us.updateRole(role);
    us.setMineTopList(ItemDataUtils().getMineRecordItems(role));
    us.setMineRecordConfigList1List(ItemDataUtils().getMineRecordConfig1(role));
    us.setMineRecordConfigList2List(ItemDataUtils().getMineRecordConfig2(role));
    us.setMineRecordConfigList3List(ItemDataUtils().getMineRecordConfig3(role));
    //获取个人资料
    getUserInfo();
    //获取VIP状态
    getVipStatus();
    //我的-中部-可配置Item
    getMorePageShow();
    // 我的-视频教程
    getVideoListInfo();
  }

  ///我的-个人资料
  Future<void> getUserInfo() async {
    final resp = await _repo.getUserInfo();

    if (resp.isOK()) {
      us.setUserInfo(_converter.convertUserInfo(resp.getSucData()));
    }
  }

  ///我的-VIP状态
  Future<void> getVipStatus() async {
    final resp = await _repo.getVipStatus();
    if (resp.isOK()) {
      //去广告会员，限时特惠29.99元\/年
      var model = _converter.convertUserVipStatusInfo(resp.getSucData());
      // if (model.status == 0) {
      //   final resp1 = await _settingRepo.getSettingList(identity: 2);
      //   model.content = resp1.getSucData()?.vip?.vipText ?? "";
      // }
      model.content = "去广告会员，限时特惠29.99元/年";
      us.setVipInfo(model);
    }
  }

  ///我的-中部-可配置Item
  Future<void> getMorePageShow() async {
    final role = UserInfoUtils.getIdentity().apiValue;
    final resp = await _repo.getMorePageShow(role);
    if (resp.isOK()) {
      us.setMineOperationList(
          _converter.convertToMineItemList(resp.getSucData()?.welfare?.module));
    }
  }

  /// 我的-视频教程
  Future<void> getVideoListInfo() async {
    final role = UserInfoUtils.getIdentity().apiValue;
    final resp = await _repo.getVideoListInfo(role);
    if (resp.isOK()) {
      us.setVideoUrl(resp.success?.data?.url);
    }
  }
}
