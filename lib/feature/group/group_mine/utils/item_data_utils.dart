import 'package:flutter/cupertino.dart';

import '../constant/mine_item_constant.dart';
import '../ui_model/mine_item_business_ui_model.dart';

class ItemDataUtils {
  //顶部记账业务部分数据(gridView)
  List<MineItemBusinessUiModel> getMineRecordItems(String role) {
    final List<MineItemBusinessUiModel> items = [
      if (role == "worker") ...[
        MineItemBusinessUiModel(
            typeId: MineItemConstant.attendanceRecord,
            title: "考勤表",
            icon: "assets/images/mine/gdjg_icon_more_attendance_tab.webp",
            isNetImage: false),
        MineItemBusinessUiModel(
            typeId: MineItemConstant.onlineReconciliation,
            title: "在线对工",
            icon: "assets/images/mine/gdjg_icon_more_dg.webp",
            isNetImage: false),
      ] else ...[
        MineItemBusinessUiModel(
            typeId: MineItemConstant.addressBook,
            title: "通讯录",
            icon: "assets/images/mine/gdjg_icon_more_workers.webp",
            isNetImage: false),
      ],
      // 以下是所有角色都有的公共项
      MineItemBusinessUiModel(
          typeId: MineItemConstant.incomeSpend,
          title: "全年收支",
          icon: "assets/images/mine/gdjg_icon_more_qnsz.webp",
          isNetImage: false),
      MineItemBusinessUiModel(
          typeId: MineItemConstant.cloudPhoto,
          title: "云相册",
          icon: "assets/images/mine/gdjg_icon_more_cloud.webp",
          isNetImage: false),
      MineItemBusinessUiModel(
          typeId: MineItemConstant.videoTutorials,
          title: "视频教程",
          icon: "assets/images/mine/icon_top_video.webp",
          isNetImage: false),
    ];
    return items;
  }

  //记账设置相关（listview）
  List<MineItemBusinessUiModel> getMineRecordConfig1(String role) {
    final List<MineItemBusinessUiModel> items = [
      // 以下是所有角色都有的公共项
      MineItemBusinessUiModel(
          typeId: MineItemConstant.recordDisplayType,
          title: "记工显示单位",
          subTitle: ""),
      if (role == "worker")
        MineItemBusinessUiModel(
            typeId: MineItemConstant.recordDefaultTpl,
            title: "默认点工工价",
            subTitle: "设置项目默认点工工价"),
      MineItemBusinessUiModel(
          typeId: MineItemConstant.recordNotice,
          title: "消息通知提示",
          subTitle: "设置系统消息通知、声音"),
    ];
    return items;
  }

  //记账设置相关（listview）
  List<MineItemBusinessUiModel> getMineRecordConfig2(String role) {
    final List<MineItemBusinessUiModel> items = [
      // 以下是所有角色都有的公共项
      MineItemBusinessUiModel(
          typeId: MineItemConstant.recordDataTransfer,
          title: "转移记工信息",
          subTitle: "旧手机号数据与当前手机号互换"),
      if (role == "worker")
        MineItemBusinessUiModel(
            typeId: MineItemConstant.recordDataDeleteList,
            title: "数据回收站",
            subTitle: "60 天内删除数据找回"),
    ];
    return items;
  }

  //账号管理相关（listview）
  List<MineItemBusinessUiModel> getMineRecordConfig3(String role) {
    final List<MineItemBusinessUiModel> items = [
      // 以下是所有角色都有的公共项
      MineItemBusinessUiModel(
          typeId: MineItemConstant.accountManage,
          title: "账号管理",
          subTitle: "绑定手机号变更、登录管理"),
      MineItemBusinessUiModel(
          typeId: MineItemConstant.recordFeedback,
          title: "问题反馈",
          subTitle: "反馈问题、客服及时解答"),
      MineItemBusinessUiModel(
          typeId: MineItemConstant.recordShare,
          title: "推荐给好友",
          subTitle: "通过微信推荐给好友使用"),
      MineItemBusinessUiModel(
          typeId: MineItemConstant.appAbout, title: "关于我们", subTitle: ""),
    ];
    return items;
  }
}
