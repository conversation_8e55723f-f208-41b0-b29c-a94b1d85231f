import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../init_module/init_route.dart';
import '../../../../utils/route_util/route_api/yp_route.dart';
import '../../../../utils/system_util/date_util.dart';
import '../ui_model/mine_user_info_ui_model.dart';

///我的-gridview
class MineUserInfoView extends StatelessWidget {
  final MineUserInfoUiModel? userInfo;
  final VoidCallback? onTap;

  const MineUserInfoView({
    super.key,
    this.userInfo,
    this.onTap,
  });

  /// 白色文本样式
  TextStyle _whiteTextStyle({double fontSize = 14, FontWeight? fontWeight}) {
    return TextStyle(
      color: Colors.white,
      fontSize: fontSize.sp,
      fontWeight: fontWeight,
    );
  }

  /// 记工信息文本样式
  TextStyle _recordTextStyle({
    double fontSize = 12,
    Color color = const Color(0xA6000000),
  }) {
    return TextStyle(
      color: color,
      fontSize: fontSize.sp,
    );
  }

  /// 加载本地图片
  Widget _buildImage(String assetName, {double size = 14}) {
    return Image.asset(
      assetName,
      width: size.w,
      height: size.h,
      fit: BoxFit.contain,
    );
  }

  // ****************************** 主布局 ******************************
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(12.w, 10.h, 12.w, 12.h),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            Color(0xFF0167FF),
            Color(0xFF018BFF),
          ],
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 姓名和修改资料行
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildUserInfo(),
              _buildEditButton(),
            ],
          ),
          SizedBox(height: 16.h),
          // 记工信息行
          _buildWorkRecordInfo(),
        ],
      ),
    );
  }

  /// 姓名和手机号区域
  Widget _buildUserInfo() {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: userInfo?.username ?? '默认工人',
            style: _whiteTextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          // 插入间距：宽度为8，高度0（仅水平间距）
          const WidgetSpan(
            child: SizedBox(width: 8, height: 0),
          ),
          TextSpan(
            text: userInfo?.tel ?? '默认工人',
            style: _whiteTextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// 修改资料按钮区域
  Widget _buildEditButton() {
    return InkWell(
      onTap:onTap,
      borderRadius: BorderRadius.circular(4.r),
      child: Row(
        children: [
          _buildImage('assets/images/group/mine_ic_edit.png'),
          SizedBox(width: 4.w),
          Text(
            '修改资料',
            style: _whiteTextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// 累计记工信息区域
  Widget _buildWorkRecordInfo() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.r),
      ),
      child: Row(
        children: [
          _buildImage('assets/images/group/mine_ic_record_hint.png'),
          SizedBox(width: 4.w),
          _buildRecordTextContent(),
        ],
      ),
    );
  }

// 记工信息文本内容（优化竖线居中）
  Widget _buildRecordTextContent() {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: '累计记工 ',
            style: _recordTextStyle(),
          ),
          TextSpan(
            text: '${userInfo?.bookkeepingDay}天',
            style: _recordTextStyle(
              fontSize: 14,
              color: const Color(0xFF0274FF),
            ),
          ),
          // 优化后的竖线分隔符
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: Container(
              width: 1.w,
              height: 12.h,
              color: const Color(0x4d000000),
              margin: EdgeInsets.symmetric(horizontal: 10.w),
            ),
          ),
          TextSpan(
            text:
                '上次记工时间 ${DateUtil.formatYYYYMMDD(userInfo?.lastBookkeepingTime ?? 0)}',
            style: _recordTextStyle(
              color: const Color(0x73000000),
            ),
          ),
        ],
      ),
    );
  }
}
