import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../ui_model/mine_item_business_ui_model.dart';
import '../us/group_mine_us.dart';

/// 我的页面列表组件
/// 功能：展示指定数据源的列表项，支持点击回调
class MineListView extends StatefulWidget {
  final GroupMineUs us;
  final int type; // 数据源类型：0-顶部列表；1-操作列表
  final Function(MineItemBusinessUiModel)? onItemTap; // 列表项点击回调

  const MineListView({
    super.key,
    required this.us,
    this.type = 0,
    this.onItemTap,
  });

  @override
  State<MineListView> createState() => _MineListViewState();
}

class _MineListViewState extends State<MineListView> {
  // 颜色常量
  static const Color _titleColor = Color(0xd9000000); // 主标题颜色
  static const Color _subTitleColor = Color(0xff8a8a99); // 副标题颜色
  static const Color _arrowColor = Color(0xff8a8a99); // 箭头颜色
  static const Color _borderColor = Color(0xE9EDF3FF); // 边框颜色

  // 尺寸常量（使用基础数值，通过screenutil适配）
  static const double _itemHorizontalPadding = 12; // 列表项左右内边距
  static const double _itemVerticalPadding = 13; // 列表项上下内边距
  static const double _arrowSize = 16; // 箭头图标大小
  static const double _subTitleArrowGap = 6; // 副标题与箭头的间距
  static const double _borderWidth = 0.5; // 分割线宽度
  static const double _badgeSize = 14; // 角标大小

  // -------------------------- 数据源处理 --------------------------
  /// 根据类型获取对应的数据源
  List<MineItemBusinessUiModel> get _currentList {
    switch (widget.type) {
      case 2:
        return widget.us.mineRecordConfigList2;
      case 3:
        return widget.us.mineRecordConfigList3;
      default:
        return widget.us.mineRecordConfigList1;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Obx(
        () => ListView.builder(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          // 自适应内容高度（避免无限高度错误）
          padding: EdgeInsets.zero,
          // 取消列表内边距，由列表项自身控制
          itemCount: _currentList.length,
          itemBuilder: (context, index) =>
              _buildListItem(_currentList[index], index),
        ),
      ),
    );
  }

  // -------------------------- 列表项构建 --------------------------
  /// 构建单个列表项
  /// [um]：列表项数据模型
  /// [index]：索引（用于判断是否为最后一项，控制分割线）
  Widget _buildListItem(MineItemBusinessUiModel um, int index) {
    final isLastItem = index == _currentList.length - 1;
    // 外层使用Padding替代Container的padding，便于内部内容溢出
    return GestureDetector(
      onTap: () => widget.onItemTap?.call(um), // 点击回调
      child: Padding(
        // 列表项内边距
        padding: EdgeInsets.only(
          left: _itemHorizontalPadding.w,
          right: _itemHorizontalPadding.w,
          top: _itemVerticalPadding.h,
        ),
        // 使用Stack包裹，允许角标溢出显示
        child: Stack(
          clipBehavior: Clip.none, // 允许子组件溢出父容器
          children: [
            Container(
              padding: EdgeInsets.only(bottom: _itemVerticalPadding.h),
              width: double.infinity,
              decoration: BoxDecoration(
                border: isLastItem ? null : _buildBottomBorder(),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween, // 左右两端对齐
                children: [
                  // 左侧主标题
                  _buildTextWidget(
                      um.title,
                      TextStyle(
                        fontSize: 16.sp,
                        color: _titleColor,
                      )),
                  // 右侧：副标题 + 箭头
                  Row(
                    children: [
                      // 副标题（优先使用subTitle，为空时显示空字符串）
                      _buildTextWidget(
                          um.subTitle,
                          TextStyle(
                            fontSize: 13.sp,
                            color: _subTitleColor,
                          )),
                      SizedBox(width: _subTitleArrowGap.w), // 副标题与箭头间距
                      _buildArrowIcon(0), // 箭头图标（传入角标数量）
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // -------------------------- 通用组件封装 --------------------------
  /// 构建底部分割线
  Border _buildBottomBorder() {
    return Border(
      bottom: BorderSide(
        color: _borderColor,
        width: _borderWidth.w,
      ),
    );
  }

  /// 通用文本组件
  Widget _buildTextWidget(String text, TextStyle style) {
    return Text(
      text,
      style: style,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 构建箭头图标（包含上方数字角标）
  Widget _buildArrowIcon(int badgeCount) {
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none, // 允许角标溢出箭头区域
      children: [
        // 原有箭头图标
        Icon(
          Icons.arrow_forward_ios,
          size: _arrowSize.w,
          color: _arrowColor,
        ),

        // 数字角标（只在数量大于0时显示）
        if (badgeCount > 0)
          Positioned(
            // 调整位置，使其完全显示在列表内边距外
            top: -(_badgeSize).w,
            right: -(_badgeSize - 10).w,
            child: Container(
              width: _badgeSize.w,
              height: _badgeSize.w,
              decoration: const BoxDecoration(
                color: Color(0XFFE8362E),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  badgeCount > 9 ? "9+" : badgeCount.toString(),
                  style: TextStyle(
                    fontSize: 10.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
      ],
    );
  }
}
