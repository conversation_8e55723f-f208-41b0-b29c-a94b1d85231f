import 'package:gdjg_pure_flutter/feature/group/group_mine/ui_model/mine_user_info_ui_model.dart';
import 'package:gdjg_pure_flutter/feature/income_expenses/ui_model/annual_incomes_pend_year_ui_model.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';

import '../../../income_expenses/ui_model/annual_incomes_pend_dept_ui_model.dart';
import '../ui_model/mine_item_business_ui_model.dart';
import '../ui_model/mine_user_vip_status_ui_model.dart';

class GroupMineUs {
  /// 身份
  final Rx<String> _role = "worker".obs;

  /// 个人资料
  final Rx<MineUserInfoUiModel> _userInfo = MineUserInfoUiModel().obs;

  /// vip信息
  final Rx<MineUserVipStatusUiModel> _vipInfo = MineUserVipStatusUiModel().obs;

  ///我的-顶部-记账业务模块
  final RxList<MineItemBusinessUiModel> _mineTopList =
      <MineItemBusinessUiModel>[].obs;

  ///我的-中部-运营配置模块
  final RxList<MineItemBusinessUiModel> _mineOperationList =
      <MineItemBusinessUiModel>[].obs;

  ///我的-中部-列表配置1
  final RxList<MineItemBusinessUiModel> _mineRecordConfigList1 =
      <MineItemBusinessUiModel>[].obs;

  ///我的-中部-列表配置2
  final RxList<MineItemBusinessUiModel> _mineRecordConfigList2 =
      <MineItemBusinessUiModel>[].obs;

  ///我的-中部-列表配置3
  final RxList<MineItemBusinessUiModel> _mineRecordConfigList3 =
      <MineItemBusinessUiModel>[].obs;

  /// 我的-视频教程
  final _videoUrl = "".obs;
  String get videoUrl => _videoUrl.value;

  List<MineItemBusinessUiModel> get mineTopList => _mineTopList;

  List<MineItemBusinessUiModel> get mineOperationList => _mineOperationList;

  List<MineItemBusinessUiModel> get mineRecordConfigList1 =>
      _mineRecordConfigList1;

  List<MineItemBusinessUiModel> get mineRecordConfigList2 =>
      _mineRecordConfigList2;

  List<MineItemBusinessUiModel> get mineRecordConfigList3 =>
      _mineRecordConfigList3;

  String get role => _role.value;

  MineUserInfoUiModel get userInfo => _userInfo.value;
  MineUserVipStatusUiModel get vipInfo => _vipInfo.value;
  void setUserInfo(MineUserInfoUiModel userInfo) {
    _userInfo.value = userInfo;
  }

  void setVipInfo(MineUserVipStatusUiModel vipInfo) {
    _vipInfo.value = vipInfo;
  }
  void updateVipContent(String text) {
    switch (vipInfo.status) {
      case 0:
        text = text;
        break;
      case 1:
        text = "去广告会员生效中";
        break;
    }
    _vipInfo.value.content = text;
  }
  void updateRole(String role) {
    _role.value = role;
  }

  void setMineTopList(List<MineItemBusinessUiModel> list) {
    _mineTopList.assignAll(list);
  }

  void setMineOperationList(List<MineItemBusinessUiModel> list) {
    _mineOperationList.assignAll(list);
  }

  void setMineRecordConfigList1List(List<MineItemBusinessUiModel> list) {
    mineRecordConfigList1.assignAll(list);
  }

  void setMineRecordConfigList2List(List<MineItemBusinessUiModel> list) {
    mineRecordConfigList2.assignAll(list);
  }

  void setMineRecordConfigList3List(List<MineItemBusinessUiModel> list) {
    mineRecordConfigList3.assignAll(list);
  }
  void setVideoUrl(String? url) {
    _videoUrl.value = url?? "";
  }
}
