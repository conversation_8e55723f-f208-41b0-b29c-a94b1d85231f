import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/update_row_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/group_pro_bill_repo.dart';
import 'package:gdjg_pure_flutter/feature/group/edit_worker_info/entity/edit_worker_info_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/wechat_share_us.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 编辑用户信息VM
class EditWorkerInfoVM {
  final _groupProBillRepo = GroupProBillRepo();
  var us = WechatShareUS();

  init(EditWorkerInfoProps props) {
    us.setWorkerName(props.workerName);
    us.setWorkerPhoneText(props.workerPhone);
    us.setWorkerId(props.workerId);
    us.setShareWorkerBtn(props.workerPhone.isEmpty);
    us.setShowSalary(props.showSalary);
    us.setWorkerConfirm(props.workerConfirm);
    us.setWorkNote(props.workerNote);
  }

  /// 更新工友姓名
  void updateWorkerName(String name) {
    us.setWorkerName(name);
  }

  /// 更新工友手机号
  void updateWorkerPhone(String phone) {
    us.setWorkerPhoneText(phone);
    // 根据手机号是否为空来控制分享按钮显示
    us.setShareWorkerBtn(phone.isEmpty);
  }

  /// 检查是否有变更
  bool hasChanges(String originalName, String originalPhone) {
    return us.workerName != originalName || us.workerPhoneText != originalPhone;
  }
  /// 保存按钮点击事件
  onSaveTap() {
    // 验证数据
    if (us.workerName.trim().isEmpty) {
      ToastUtil.showToast("输入的姓名有误");
      return;
    }
    final phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    if (us.workerPhoneText.trim().isEmpty ||
        !phoneRegex.hasMatch(us.workerPhoneText.trim())) {
      ToastUtil.showToast("输入的手机号有误");
      return;
    }
    UpdateRowAParamModel params = UpdateRowAParamModel()
    ..worker_id = us.workerId
    ..work_note= us.workNote
    ..name = us.workerName.trim()
    ..tel = us.workerPhoneText.trim();
    _groupProBillRepo.updateWorkerInfo(params).then((res) {
      if(res.isOK()){
        EditWorkerInfoProps resultData = EditWorkerInfoProps(
          workerName: us.workerName.trim(),
          workerPhone: us.workerPhoneText.trim(),
          workerId: us.workerId,
          workerConfirm: us.isWorkerConfirm,
          showSalary: us.isShowSalary,
        );
        // 返回数据并关闭页面
        YPRoute.closePage(resultData);
      }
    });
  }
}
