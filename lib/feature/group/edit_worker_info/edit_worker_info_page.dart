import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/edit_worker_info/entity/edit_worker_info_props.dart';
import 'package:gdjg_pure_flutter/feature/group/edit_worker_info/vm/edit_worker_info_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

/// 编辑工友信息页面
class EditWorkerInfoPage extends BaseFulPage {
  final String? noteId;
  final String? workerId;
  final String? deptId;

  const EditWorkerInfoPage({
    super.key,
    this.noteId,
    this.workerId,
    this.deptId,
  }) : super(appBar: null);

  @override
  State<EditWorkerInfoPage> createState() => _EditWorkerInfoPageState();
}

class _EditWorkerInfoPageState extends BaseFulPageState<EditWorkerInfoPage> {
  final EditWorkerInfoVM _vm = EditWorkerInfoVM();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _nameFocusNode = FocusNode();
  final FocusNode _phoneFocusNode = FocusNode();

  // 页面参数
  EditWorkerInfoProps? _props;

  // 数据状态
  bool _isSelfCreated = false;
  String _originalName = "";
  String _originalPhone = "";
  String _workerId = "";

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    _props = routeParams as EditWorkerInfoProps?;
    _vm.init(_props ?? EditWorkerInfoProps());
  }

  @override
  void onPageCreate() {
    super.onPageCreate();
    _loadWorkerInfo();
  }

  @override
  void dispose() {
    _nameController.removeListener(_onNameChanged);
    _phoneController.removeListener(_onPhoneChanged);
    _nameController.dispose();
    _phoneController.dispose();
    _nameFocusNode.dispose();
    _phoneFocusNode.dispose();
    super.dispose();
  }

  /// 加载工友信息
  void _loadWorkerInfo() {
    // 从ViewModel的US中获取数据
    _originalName = _vm.us.workerName;
    _originalPhone = _vm.us.workerPhoneText;
    _workerId = _vm.us.workerId;

    _nameController.text = _originalName;
    _phoneController.text = _originalPhone;

    // 监听输入变化以更新ViewModel和保存按钮状态
    _nameController.addListener(_onNameChanged);
    _phoneController.addListener(_onPhoneChanged);

    setState(() {});
  }

  /// 姓名输入变化监听
  void _onNameChanged() {
    _vm.updateWorkerName(_nameController.text);
    setState(() {});
  }

  /// 手机号输入变化监听
  void _onPhoneChanged() {
    _vm.updateWorkerPhone(_phoneController.text);
    setState(() {});
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leadingWidth: 40.w,
        // 调整左侧区域宽度，影响标题位置
        leading: IconButton(
          icon: IconFont(
            IconNames.saasArrowLeft,
            size: 24,
            color: "#000000",
          ),
          onPressed: () {
            Navigator.of(context).pop();
          },
          splashColor: Colors.transparent,
          // 移除水波纹效果
          highlightColor: Colors.transparent,
          // 移除高亮效果
          hoverColor: Colors.transparent,
          // 移除悬停效果
          focusColor: Colors.transparent, // 移除焦点效果
        ),
        title: Text(
          "编辑工友信息",
          style: TextStyle(
            fontSize: 20.sp,
            color: ColorsUtil.black85,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: false,
        titleSpacing: 0.w,
        // 设置标题与leading之间的间距
        actions: [
          _buildSaveButton(),
          SizedBox(width: 16.w),
        ],
      ),
      body: Column(
        children: [
          // 分割线
          Container(
            height: 8.w,
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              color: ColorsUtil.ypBgColor,
            ),
          ),

          // 表单区域
          _buildFormSection(),

          // 分享按钮区域（当没有手机号时显示）
          if (_vm.us.isShowShareWorkerBtn) _buildShareSection(),
        ],
      ),
    );
  }

  /// 构建表单区域
  Widget _buildFormSection() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          // 工友姓名输入框
          _buildInputField(
            label: "工友姓名",
            controller: _nameController,
            focusNode: _nameFocusNode,
            placeholder: "请输入姓名(必填)",
            maxLength: 20,
            autofocus: false,
          ),

          // 手机号码输入框
          _buildPhoneField(),
        ],
      ),
    );
  }

  /// 构建手机号码字段
  Widget _buildPhoneField() {
    return Container(
      height: 57.h,
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: ColorsUtil.ypGreyColor,
            width: 0.5.w,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            "手机号码",
            style: TextStyle(
              fontSize: 17.sp,
              color: Color(0xFF333333),
            ),
          ),
          SizedBox(width: 24.w),
          Expanded(
            child: _isSelfCreated
                ? Text(
                    _originalPhone,
                    style: TextStyle(
                      fontSize: 17.sp,
                      color: Color(0xFF333333),
                      height: 57.h / 17.sp, // 对应RN的height: 114 / 2
                    ),
                  )
                : TextField(
                    controller: _phoneController,
                    focusNode: _phoneFocusNode,
                    keyboardType: TextInputType.phone,
                    // 使用手机号码专用键盘
                    maxLength: 11,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    style: TextStyle(
                      fontSize: 17.sp,
                      color: Color(0xFF333333),
                    ),
                    decoration: InputDecoration(
                      hintText: "请输入真实手机号码",
                      hintStyle: TextStyle(
                        color: ColorsUtil.hintFontColor,
                        fontSize: 17.sp,
                      ),
                      border: InputBorder.none,
                      counterText: '',
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  /// 构建输入字段
  Widget _buildInputField({
    required String label,
    required TextEditingController controller,
    required FocusNode focusNode,
    required String placeholder,
    int? maxLength,
    bool autofocus = false,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
  }) {
    return Container(
      height: 57.h,
      padding: EdgeInsets.only(left: 16.w, right: 16.w),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: ColorsUtil.ypGreyColor,
            width: 0.5.w,
          ),
        ),
      ),
      child: Row(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 17.sp,
              color: Color(0xFF323232),
            ),
          ),
          SizedBox(width: 24.w),
          Expanded(
            child: TextField(
              controller: controller,
              focusNode: focusNode,
              autofocus: autofocus,
              keyboardType: keyboardType,
              maxLength: maxLength,
              inputFormatters: inputFormatters,
              style: TextStyle(
                fontSize: 17.sp,
                color: Color(0xFF333333),
              ),
              decoration: InputDecoration(
                hintText: placeholder,
                hintStyle: TextStyle(
                  color: ColorsUtil.hintFontColor,
                  fontSize: 17.sp,
                ),
                border: InputBorder.none,
                counterText: '',
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分享区域（当没有手机号时显示）
  Widget _buildShareSection() {
    return Container(
      margin: EdgeInsets.only(top: 8.h),
      color: Colors.white,
      child: Container(
        width: 280.w, // 对应RN的width: 560 / 2
        margin: EdgeInsets.symmetric(horizontal: (375.w - 280.w) / 2), // 居中
        padding: EdgeInsets.only(top: 32.h), // 对应RN的marginTop: 64 / 2
        child: Column(
          children: [
            // 分享按钮
            _buildShareButton(),

            SizedBox(height: 5.h), // 对应RN的paddingTop: 10 / 2

            // 提示文字
            _buildShareTips(),

            SizedBox(height: 32.h), // 底部间距
          ],
        ),
      ),
    );
  }

  /// 构建分享按钮
  Widget _buildShareButton() {
    return InkWell(
      onTap: _onShareTap,
      child: Container(
        width: double.infinity,
        height: 44.h,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          border: Border.all(
            color: ColorsUtil.primaryColor,
            width: 1.w,
          ),
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Text(
          "分享给工友",
          style: TextStyle(
            fontSize: 16.sp, // 对应RN的fontSize: 32 / 2
            color: ColorsUtil.primaryColor,
          ),
        ),
      ),
    );
  }

  /// 构建分享提示文字
  Widget _buildShareTips() {
    return Text(
      "不知道工人电话？分享给工人自己填写工友填写完手机号后，将自动修改现有手机号",
      style: TextStyle(
        fontSize: 14.sp,
        color: ColorsUtil.primaryColor,
        height: 1.4,
      ),
      textAlign: TextAlign.left,
    );
  }

  /// 分享按钮点击事件
  void _onShareTap() {
    // TODO: 实现分享功能
    print("分享给工友");
  }

  /// 检查是否有变更
  bool _hasChanges() {
    return _vm.hasChanges(_originalName, _originalPhone);
  }

  /// 构建右侧保存按钮（在AppBar中使用）
  Widget _buildSaveButton() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: _hasChanges() ? _vm.onSaveTap : null,
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: _hasChanges() ? ColorsUtil.primaryColor : Color(0xFFF0F0F0),
        ),
        height: 38.w,
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        child: Text(
          "保存",
          style: TextStyle(
            fontSize: 16.sp,
            color: _hasChanges() ? Colors.white : ColorsUtil.black25,
          ),
        ),
      ),
    );
  }
}
