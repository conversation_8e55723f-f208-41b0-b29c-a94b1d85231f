import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/vm/group_project_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/create_group_project_dialog.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/group_project_item_card.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_setting/entity/worker_setting_props.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_tel_modify/entity/modify_worker_tel_props.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/feature/group/common_page/select_type_page/entity/select_type_props.dart';
import 'package:gdjg_pure_flutter/data/utils_data/course/repo/model/course_net_model_biz_model.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/widget/label/text_label.dart';
import 'package:gdjg_pure_flutter/widget/network_carousel_widget.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

/// 班组项目页面
class GroupProjectPage extends BaseFulPage {
  const GroupProjectPage({super.key}) : super(appBar: null);

  @override
  State<GroupProjectPage> createState() => _GroupProjectPageState();
}

class _GroupProjectPageState extends BaseFulPageState<GroupProjectPage> {
  late GroupProjectVM _viewModel;
  late RefreshController _refreshController;

  @override
  void onPageCreate() {
    super.onPageCreate();
    _viewModel = GroupProjectVM();
    _refreshController = RefreshController(initialRefresh: false);
    _viewModel.queryProjectList();
    _viewModel.getCourseList();
  }

  @override
  void onPageShow() {
    super.onPageShow();
    // 页面显示时刷新数据
    _viewModel.onRefresh();
  }

  @override
  void onPageDestroy() {
    _refreshController.dispose();
    super.onPageDestroy();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: SmartRefresher(
          controller: _refreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: _onRefresh,
          child: CustomScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              // 轮播图
              SliverToBoxAdapter(
                child: _buildBannerSection(),
              ),

              // 新建项目按钮
              SliverToBoxAdapter(
                child: _buildCreateProjectSection(),
              ),

              // 项目列表
              _buildProjectListSliver(),

              // 教程区域
              SliverToBoxAdapter(
                child: _buildTutorialSection(),
              ),

              // 测试按钮
              SliverToBoxAdapter(
                child: _buildTestButtonsSection(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 刷新回调
  Future<void> _onRefresh() async {
    await _viewModel.onRefresh();
    _refreshController.refreshCompleted();
  }

  /// 轮播图
  Widget _buildBannerSection() {
    return Container(
      padding: EdgeInsets.only(top: 14.h, left: 16.w, right: 16.w, bottom: 2.h),
      child: NetworkCarouselWidget(
        code: 'JGJZ_HOME_GROUP_BANER',
        height: 48.h,
      ),
    );
  }

  /// 新建项目按钮行
  Widget _buildCreateProjectSection() {
    return Container(
      padding:
          EdgeInsets.only(left: 16.w, top: 10.h, bottom: 10.h, right: 16.w),
      child: Row(
        children: [
          _buildNewProjectButton(),
          const Spacer(),
          Obx(() => _buildIgnoredProjectsEntry()),
        ],
      ),
    );
  }

  /// 新建项目按钮
  Widget _buildNewProjectButton() {
    return GestureDetector(
      onTap: _showCreateProjectDialog,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(5.w),
          border: Border.all(
            color: ColorsUtil.primaryColor,
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              color: ColorsUtil.primaryColor,
              size: 20.w,
            ),
            SizedBox(width: 4.w),
            Text(
              '新建项目',
              style: TextStyle(
                color: ColorsUtil.primaryColor,
                fontSize: 14.w,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 创建项目弹窗
  void _showCreateProjectDialog() {
    SmartDialog.show(
      builder: (context) => CreateGroupProjectDialog(
        viewModel: _viewModel,
      ),
    );
  }

  /// 已结清入口
  Widget _buildIgnoredProjectsEntry() {
    final ignoredNum = _viewModel.us.ignoredNum.value;
    final ignoredCount = ignoredNum.toInt();

    return GestureDetector(
      onTap: _onIgnoredProjectsTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            ignoredCount > 0 ? '已结清（$ignoredCount）' : '已结清',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF030303),
              fontWeight: FontWeight.w400,
              height: 1.0,
            ),
          ),
          SizedBox(width: 4.w),
          Icon(
            Icons.arrow_forward_ios,
            size: 16.w,
            color: const Color(0xFF030303),
          ),
        ],
      ),
    );
  }

  /// 点击已结清入口
  void _onIgnoredProjectsTap() {
    YPRoute.openPage(RouteNameCollection.settledPage);
  }

  /// 项目列表
  Widget _buildProjectListSliver() {
    return Obx(() {
      // 无数据时显示空状态
      if (_viewModel.us.showProList.isEmpty) {
        return SliverToBoxAdapter(
          child: _buildEmptyState(),
        );
      }

      // 有数据时显示项目列表
      return SliverPadding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        sliver: SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final project = _viewModel.us.showProList[index];
              return GroupProjectItemCard(item: project);
            },
            childCount: _viewModel.us.showProList.length,
          ),
        ),
      );
    });
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: 70.h, bottom: 50.h),
      child: Column(
        children: [
          Image.asset(
            Assets.commonIconEmptyTeamProject,
            width: 120.w,
            height: 120.w,
            fit: BoxFit.contain,
          ),
          Text(
            '暂未创建项目',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF8A8A99),
            ),
          ),
        ],
      ),
    );
  }

  /// 教程
  Widget _buildTutorialSection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      padding: EdgeInsets.only(bottom: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 2.w,
                height: 20.h,
                decoration: BoxDecoration(
                  color: const Color(0xFF1984FF),
                  borderRadius: BorderRadius.circular(2.w),
                ),
              ),

              SizedBox(width: 8.w),

              Text(
                '这些教程轻松帮你解决记工问题',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF333333),
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // 教程视频
          _buildTutorialGrid(),
        ],
      ),
    );
  }

  /// 教程视频网格
  Widget _buildTutorialGrid() {
    return Obx(() {
      final courseList = _viewModel.us.courseList;

      if (courseList.isEmpty) {
        return Container();
      }

      return GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 12.h,
          childAspectRatio: 0.85,
        ),
        itemCount: courseList.length,
        itemBuilder: (context, index) {
          return _buildTutorialItem(courseList[index]);
        },
      );
    });
  }

  /// 构建教程项目
  Widget _buildTutorialItem(CourseNetModelABizModel course) {
    return GestureDetector(
      onTap: () => _onTutorialTap(course),
      child: Container(
        height: 150.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6.r),
        ),
        child: Column(
          children: [
            // 封面图片
            Container(
              height: 120.h,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(6.r),
                  topRight: Radius.circular(6.r),
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(6.r),
                  topRight: Radius.circular(6.r),
                ),
                child: course.coverPic.isNotEmpty
                    ? Image.network(
                        course.coverPic,
                        width: double.infinity,
                        height: double.infinity,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey[200],
                            child: Center(
                              child: Icon(
                                Icons.play_circle_outline,
                                size: 40.w,
                                color: Colors.grey[400],
                              ),
                            ),
                          );
                        },
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Container(
                            color: Colors.grey[200],
                            child: Center(
                              child: CircularProgressIndicator(
                                strokeWidth: 2.w,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.grey[400]!),
                              ),
                            ),
                          );
                        },
                      )
                    : Container(
                        color: Colors.grey[200],
                        child: Center(
                          child: Icon(
                            Icons.play_circle_outline,
                            size: 40.w,
                            color: Colors.grey[400],
                          ),
                        ),
                      ),
              ),
            ),

            // 标题
            Expanded(
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                child: Center(
                  child: Text(
                    course.name,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF333333),
                      fontWeight: FontWeight.w400,
                      height: 1.2,
                    ),
                    textAlign: TextAlign.start,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 教程点击
  void _onTutorialTap(CourseNetModelABizModel course) async {
    await _viewModel.onTutorialTap(course);
  }

  /// 测试按钮
  Widget _buildTestButtonsSection() {
    return Container(
      color: const Color(0xFFFFFFFF),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      child: Center(
        child: Column(
          spacing: 16,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              '项目',
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF222222),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                // 可选：构建页面参数
                final SelectTypeProps props = SelectTypeProps(
                    isShowExpense: true,
                    recordTypeList: [RwaRecordType.workDays]);
                YPRoute.openPage(RouteNameCollection.selectRecordType,
                        params: props)
                    ?.then((res) {
                  if (res != null) {}
                });
              },
              child: Text(
                '类型选择',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF222222),
                ),
              ),
            ),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.inviteWorker);
                },
                child: Text('跳转到微信邀请页')),

            // 测试不同source的工友设置页跳转
            ElevatedButton(
                onPressed: () {
                  _navigateToWorkerSetting(WorkerSettingSource.projectWorker);
                },
                child: Text('项目在场工友：跳转到工友设置页')),
            ElevatedButton(
                onPressed: () {
                  _navigateToWorkerSetting(WorkerSettingSource.defaultPage);
                },
                child: Text('默认页面：跳转到工友设置页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.workerResume);
                },
                child: Text('跳转到工友名片页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.workerSelector);
                },
                child: Text('跳转到选择工友页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.modifyWorkerTel,
                      params: ModifyWorkerTelProps(
                        workerId: 1,
                        workNoteId: '1',
                        name: '张三',
                        tel: '13998184246',
                        deptId: '1',
                      ));
                },
                child: Text('跳转到修改手机号')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.phoneContact);
                },
                child: Text('跳转到手机联系人页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.groupRecordWork,
                      params: GroupRecordWorkProps(
                          from: GroupRecordWorkFrom.projectList,
                          deptId: 41679));
                },
                child: Text('跳转到班组记工页')),
            ElevatedButton(
                onPressed: () {
                  YPRoute.openPage(RouteNameCollection.groupAccountWork,
                      params: GroupRecordWorkProps(
                          from: GroupRecordWorkFrom.projectList,
                          deptId: 41979));
                },
                child: Text('跳转到班组记账页')),
            TextLabel(
              id: '001',
              label: '在建项目',
              labelType: TextLabelType.blue,
              isBold: true,
              onCloseTap: (id) => {ToastUtil.showToast('关闭标签$id')},
              onLabelTap: (id) => {ToastUtil.showToast('点击标签$id')},
            ),
          ],
        ),
      ),
    );
  }

  /// 跳转到工友设置页，传递不同的source参 数
  void _navigateToWorkerSetting(WorkerSettingSource source) {
    // 创建不同的参数配置ddd
    WorkerSettingProps props;

    switch (source) {
      case WorkerSettingSource.projectWorker:
        props = WorkerSettingProps(
          source: source,
          workNoteName: '恒大天府文化旅游城2期', // 项目名称
          workerId: 3174276,
          isSelf: false,
        );
        break;
      default:
        props = WorkerSettingProps(
          source: source,
          workerId: 3174276,
          isSelf: false,
        );
        break;
    }

    // 跳转到工友设置页，传递参数
    YPRoute.openPage(RouteNameCollection.workerSetting, params: props);
  }
}
