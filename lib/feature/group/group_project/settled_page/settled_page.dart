import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/settled_page/vm/settled_page_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/settled_page/view/settled_project_item_card.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

/// 已结清项目页面
class SettledPage extends BaseFulPage {
  const SettledPage({super.key}) : super(appBar: null);

  @override
  State<SettledPage> createState() => _SettledPageState();
}

class _SettledPageState extends BaseFulPageState<SettledPage> {
  late SettledPageVM _viewModel;
  late RefreshController _refreshController;
  late TextEditingController _searchController;

  @override
  void onPageCreate() {
    super.onPageCreate();
    _viewModel = SettledPageVM();
    _refreshController = RefreshController(initialRefresh: false);
    _searchController = TextEditingController();
    _viewModel.loadSettledProjects();
  }

  @override
  void onPageDestroy() {
    _viewModel.dispose();
    _refreshController.dispose();
    _searchController.dispose();
    super.onPageDestroy();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildCommonAppBar(title: '已结清'),
      backgroundColor: const Color(0xFFF5F5F5),
      body: Column(
        children: [
          // 搜索框
          _buildSearchSection(),

          // 项目列表
          Expanded(
            child: SmartRefresher(
              controller: _refreshController,
              enablePullDown: true,
              enablePullUp: false,
              onRefresh: _onRefresh,
              child: Obx(() => _buildProjectList()),
            ),
          ),
        ],
      ),
    );
  }

  /// 刷新回调
  Future<void> _onRefresh() async {
    await _viewModel.onRefresh();
    _refreshController.refreshCompleted();
  }

  /// 搜索
  Widget _buildSearchSection() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(16.w, 6.h, 16.w, 10.h),
      child: Container(
        height: 40.h,
        decoration: BoxDecoration(
          color: const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(2.r),
        ),
        child: TextField(
          controller: _searchController,
          onChanged: _viewModel.onSearchChanged,
          decoration: InputDecoration(
            hintText: '请输入项目名称',
            hintStyle: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF999999),
            ),
            prefixIcon: Icon(
              Icons.search,
              size: 20.w,
              color: const Color(0xFF030303),
            ),
            border: InputBorder.none,
            contentPadding: EdgeInsets.symmetric(vertical: 10.h),
          ),
        ),
      ),
    );
  }

  /// 项目列表
  Widget _buildProjectList() {
    final filteredProjects = _viewModel.us.filteredProjects;
    
    if (filteredProjects.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      itemCount: filteredProjects.length,
      itemBuilder: (context, index) {
        final project = filteredProjects[index];
        return SettledProjectItemCard(
          item: project,
          onDeleteProject: _viewModel.onDeleteProject,
          onRecoverProject: _viewModel.onRecoverProject,
        );
      },
    );
  }

  /// 空状态
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(top: 80.h),
      child: Column(
        children: [
          Image.asset(
            Assets.commonIconEmptyTeamProject,
            width: 140.w,
            height: 140.w,
            fit: BoxFit.contain,
          ),
          Text(
            '暂无数据',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF8A8A99),
            ),
          ),
        ],
      ),
    );
  }
}
