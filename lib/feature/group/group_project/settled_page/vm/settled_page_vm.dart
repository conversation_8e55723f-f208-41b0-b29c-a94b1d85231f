import 'package:gdjg_pure_flutter/feature/group/group_project/settled_page/vm/settled_page_us.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_project/repo/group_project_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/repo/corp_select_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_put_away_dept_param_model.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';

/// 已结清项目页面ViewModel
class SettledPageVM {
  final SettledPageUS us = SettledPageUS();
  final GroupProjectRepo _groupProjectRepo = GroupProjectRepo();
  final WorkerProjectRepo _workerProjectRepo = WorkerProjectRepo();
  final CorpSelectRepo _corpSelectRepo = CorpSelectRepo();

  /// 获取已结清项目列表
  Future<void> loadSettledProjects() async {
    final result = await _groupProjectRepo.getDeptListGroup(
      isIgnore: "1",
      type: "created",
    );

    if (result.isOK()) {
      final projects = result.getSucData()?.list ?? [];
      us.setSettledProjects(projects);
    } else {
      ToastUtil.showToast('获取已结清项目失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }

  /// 刷新数据
  Future<void> onRefresh() async {
    await loadSettledProjects();
  }

  /// 搜索项目
  void onSearchChanged(String query) {
    us.setSearchQuery(query);
  }

  /// 删除项目
  Future<void> onDeleteProject(String projectId, String corpId) async {
    // 企业切换
    if (corpId.isNotEmpty) {
      final corpSelectParam = CorpSelectParamModel(
        corp_id: corpId,
      );
      await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
    }

    final result = await _workerProjectRepo.deleteProject(projectId);

    if (result.isOK()) {
      ToastUtil.showToast('删除项目成功');
      EventBusUtil.emit<String>('project_delete');
      await loadSettledProjects();
    } else {
      ToastUtil.showToast('删除项目失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }

  /// 恢复在建
  Future<void> onRecoverProject(String projectId, String corpId) async {
    // 企业切换
    if (corpId.isNotEmpty) {
      final corpSelectParam = CorpSelectParamModel(
        corp_id: corpId,
      );
      await _corpSelectRepo.fetchCorpSelect(corpSelectParam);
    }

    // 恢复在建
    final param = DeptPutAwayDeptParamModel(
      dept_id: projectId,
      is_ignored: '0',
    );

    final result = await _workerProjectRepo.putAwayDept(param);

    if (result.isOK()) {
      ToastUtil.showToast('恢复在建成功');
      EventBusUtil.emit<String>('project_recover');
      await loadSettledProjects();
    } else {
      ToastUtil.showToast('恢复在建失败：${result.fail?.errorMsg ?? "未知错误"}');
    }
  }

  /// 清理资源
  void dispose() {
    us.clear();
  }
}
