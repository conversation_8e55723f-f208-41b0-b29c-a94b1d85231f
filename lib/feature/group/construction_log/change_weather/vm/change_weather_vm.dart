import 'package:gdjg_pure_flutter/feature/group/construction_log/change_weather/entity/change_weather_props.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/change_weather/vm/change_weather_us.dart';
import 'package:gdjg_pure_flutter/widget/weather_selector/weather_selector_dialog.dart';
import 'package:gdjg_pure_flutter/utils/regex/regex_utils.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 修改天气页面视图模型
class ChangeWeatherViewModel {
  final ChangeWeatherUS us = ChangeWeatherUS();
  ChangeWeatherProps? _props;

  /// 初始化
  void init(ChangeWeatherProps? props) {
    _props = props;
    if (props != null) {
      us.initData(
        date: props.recordDate,
        morningWeatherValue: props.morningWeather,
        afternoonWeatherValue: props.afternoonWeather,
        morningTempValue: props.morningTemp,
        afternoonTempValue: props.afternoonTemp,
      );
    }
  }

  /// 销毁
  void dispose() {

  }

  /// 点击上午天气
  void onMorningWeatherTap() {
    showWeatherSelectorDialog(
      currentWeather: us.morningWeather.value,
      onSelected: (weather) {
        us.setMorningWeather(weather);
      },
    );
  }

  /// 点击下午天气
  void onAfternoonWeatherTap() {
    showWeatherSelectorDialog(
      currentWeather: us.afternoonWeather.value,
      onSelected: (weather) {
        us.setAfternoonWeather(weather);
      },
    );
  }

  /// 上午温度改变
  void onMorningTempChanged(String temp) {
    us.setMorningTemp(temp);
  }

  /// 下午温度改变
  void onAfternoonTempChanged(String temp) {
    us.setAfternoonTemp(temp);
  }

  /// 保存天气信息
  void onSaveTap() {
    // 验证数据
    if (us.morningWeather.value.isEmpty) {
      ToastUtil.showToast('请选择上午天气');
      return;
    }

    if (us.afternoonWeather.value.isEmpty) {
      ToastUtil.showToast('请选择下午天气');
      return;
    }

    if (us.morningTemp.value.isEmpty) {
      ToastUtil.showToast('请输入上午温度');
      return;
    }

    if (us.afternoonTemp.value.isEmpty) {
      ToastUtil.showToast('请输入下午温度');
      return;
    }

    // 验证温度范围
    if (!RegexUtils.isValidTemperature(us.morningTemp.value) ||
        !RegexUtils.isValidTemperature(us.afternoonTemp.value)) {
      ToastUtil.showToast('请控制温度在-50摄氏度~50摄氏度');
      return;
    }

    // 调用保存回调
    _props?.onSave(
      us.morningWeather.value,
      us.afternoonWeather.value,
      us.morningTemp.value,
      us.afternoonTemp.value,
    );

    YPRoute.closePage();
  }
}
