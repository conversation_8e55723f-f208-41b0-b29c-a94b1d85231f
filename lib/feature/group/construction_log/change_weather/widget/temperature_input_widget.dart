import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:get/get.dart';

/// 温度输入组件
class TemperatureInputWidget extends StatefulWidget {
  final String title;
  final RxString value;
  final Function(String) onChanged;

  const TemperatureInputWidget({
    super.key,
    required this.title,
    required this.value,
    required this.onChanged,
  });

  @override
  State<TemperatureInputWidget> createState() => _TemperatureInputWidgetState();
}

class _TemperatureInputWidgetState extends State<TemperatureInputWidget> {
  late TextEditingController _controller;
  bool _isInternalUpdate = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.value.value.trimTrailingZeros());
    
    // 监听外部值变化
    widget.value.listen((newValue) {
      if (!_isInternalUpdate) {
        final displayValue = newValue.trimTrailingZeros();
        if (_controller.text != displayValue) {
          _controller.text = displayValue;
          _controller.selection = TextSelection.fromPosition(
            TextPosition(offset: _controller.text.length),
          );
        }
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Text(
            widget.title,
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF333333),
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 60.w,
                child: TextField(
                  controller: _controller,
                  keyboardType: const TextInputType.numberWithOptions(
                    signed: true,
                    decimal: true,
                  ),
                  inputFormatters: [
                    _createTemperatureFormatter(),
                  ],
                  textAlign: TextAlign.right,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: const Color(0xFF666666),
                  ),
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    hintText: '请输入',
                    hintStyle: TextStyle(
                      fontSize: 16.sp,
                      color: const Color(0xFF999999),
                    ),
                    contentPadding: EdgeInsets.zero,
                  ),
                  onChanged: (value) {
                    _isInternalUpdate = true;
                    widget.onChanged(value);
                    _isInternalUpdate = false;
                  },
                ),
              ),
              SizedBox(width: 4.w),
              Text(
                '°C',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF666666),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 创建温度输入格式化器
  TextInputFormatter _createTemperatureFormatter() {
    return TextInputFormatter.withFunction((oldValue, newValue) {
      final text = newValue.text;

      // 空字符串允许
      if (text.isEmpty) {
        return newValue;
      }

      // 总字符数限制为6个
      if (text.length > 6) {
        return oldValue;
      }

      // 只有负号也允许
      if (text == '-') {
        return newValue;
      }

      // 检查是否符合温度格式：可选负号 + 数字 + 可选小数点 + 最多2位小数
      final regex = RegExp(r'^-?\d*(\.\d{0,2})?$');

      if (regex.hasMatch(text)) {
        return newValue;
      }

      // 不符合格式，返回旧值
      return oldValue;
    });
  }
}
