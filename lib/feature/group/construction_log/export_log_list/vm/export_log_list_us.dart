import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/task_list_biz_model.dart';

/// 导出日志列表页面UI状态
class ExportLogListUS {
  /// 任务列表
  final RxList<TaskListABizModel> _taskList = <TaskListABizModel>[].obs;

  /// 当前页码
  final RxInt _currentPage = 1.obs;

  /// 是否有更多数据
  final RxBool _hasMore = true.obs;

  /// 是否处于删除模式
  final RxBool _isDeleteMode = false.obs;

  /// 选中的任务ID列表
  final RxList<double> _selectedTaskIds = <double>[].obs;

  /// 获取任务列表
  List<TaskListABizModel> get taskList => _taskList;

  /// 获取当前页码
  int get currentPage => _currentPage.value;

  /// 是否有更多数据
  bool get hasMore => _hasMore.value;

  /// 是否处于删除模式
  bool get isDeleteMode => _isDeleteMode.value;

  /// 选中的任务ID列表
  List<double> get selectedTaskIds => _selectedTaskIds;

  /// 是否全选 - 计算属性，基于选中列表自动计算
  bool get isAllSelected => _selectedTaskIds.length == _taskList.length && _taskList.isNotEmpty;

  /// 是否有未完成的任务
  bool get hasUnfinishedTasks {
    return _taskList.any((task) => task.isUnfinished);
  }

  /// 设置任务列表
  void setTaskList(List<TaskListABizModel> list) {
    _taskList.assignAll(list);
  }
  
  /// 添加任务列表
  void addTaskList(List<TaskListABizModel> list) {
    _taskList.addAll(list);
  }

  /// 增加页码
  void incrementPage() {
    _currentPage.value++;
  }

  /// 设置是否有更多数据
  void setHasMore(bool hasMore) {
    _hasMore.value = hasMore;
  }

  /// 重置分页状态
  void resetPaging() {
    _currentPage.value = 1;
    _hasMore.value = true;
  }

  /// 设置删除模式
  void setDeleteMode(bool isDeleteMode) {
    _isDeleteMode.value = isDeleteMode;
    if (!isDeleteMode) {
      _selectedTaskIds.clear();
    }
  }

  /// 切换任务选中状态
  void toggleTaskSelection(double taskId) {
    if (_selectedTaskIds.contains(taskId)) {
      _selectedTaskIds.remove(taskId);
    } else {
      _selectedTaskIds.add(taskId);
    }
  }

  /// 设置全选状态
  void setAllSelected(bool isAllSelected) {
    if (isAllSelected) {
      // 全选
      _selectedTaskIds.assignAll(_taskList.map((task) => task.id));
    } else {
      // 取消全选
      _selectedTaskIds.clear();
    }
  }

  /// 检查任务是否被选中
  bool isTaskSelected(double taskId) {
    return _selectedTaskIds.contains(taskId);
  }

  /// 获取选中任务数量
  int get selectedCount => _selectedTaskIds.length;
}
