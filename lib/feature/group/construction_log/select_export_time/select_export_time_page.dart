import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/select_export_time/entity/select_export_time_props.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/select_export_time/vm/select_export_time_vm.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:get/get.dart';

class SelectExportTimePage extends BaseFulPage {
  const SelectExportTimePage({super.key}) : super(appBar: null);

  @override
  createState() => _SelectExportTimePageState();
}

class _SelectExportTimePageState extends BaseFulPageState<SelectExportTimePage> {
  final SelectExportTimeViewModel _viewModel = SelectExportTimeViewModel();

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var props = routeParams as SelectExportTimeProps?;
    _viewModel.init(props);
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _viewModel.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F6FA),
      appBar: _buildCustomAppBar(),
      body: _buildBody(),
    );
  }

  /// AppBar
  AppBar _buildCustomAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      leading: GestureDetector(
        onTap: () => YPRoute.closePage(),
        child: Container(
          padding: const EdgeInsets.only(left: 8.0),
          child: Image(
            image: AssetImage(Assets.commonIconArrowBack),
          ),
        ),
      ),
      leadingWidth: 38,
      titleSpacing: 08,
      title: Text(
        "选择导出时间",
        style: const TextStyle(
          color: Colors.black,
          fontSize: 22,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: false,
      actions: [
        GestureDetector(
          onTap: () {
            // 跳转到日志导出列表页面
            YPRoute.openPage(RouteNameCollection.exportLogList);
          },
          child: Container(
            margin: const EdgeInsets.only(right: 16.0),
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: Center(
              child: Text(
                "日志导出列表",
                style: TextStyle(
                  color: ColorsUtil.primaryColor,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        SizedBox(height: 10.h),
        _buildTimeSelectionSection(),
        SizedBox(height: 25.h),
        _buildExportButton(),
      ],
    );
  }

  /// 时间选择
  Widget _buildTimeSelectionSection() {
    return Column(
      children: [
        _buildTimeSelectionRow(
          title: "开始时间",
          onTap: _viewModel.showStartTimePicker,
          timeObservable: () => _viewModel.us.startTimeText,
        ),
        _buildDivider(),
        _buildTimeSelectionRow(
          title: "结束时间",
          onTap: _viewModel.showEndTimePicker,
          timeObservable: () => _viewModel.us.endTimeText,
        ),
      ],
    );
  }

  /// 时间选择行
  Widget _buildTimeSelectionRow({
    required String title,
    required VoidCallback onTap,
    required String Function() timeObservable,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        color: Colors.white,
        height: 56.h,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Row(
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF323232),
                fontWeight: FontWeight.w400,
              ),
            ),
            const Spacer(),
            Obx(() => Text(
              timeObservable(),
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF8A8A99),
                fontWeight: FontWeight.w400,
              ),
            )),
            SizedBox(width: 8.w),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.w,
              color: const Color(0xFFCCCCCC),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 1.h,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      color: const Color(0xFFF0F0F0),
    );
  }

  /// 导出按钮
  Widget _buildExportButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: GestureDetector(
        onTap: _viewModel.exportConstructionLog,
        child: Container(
          width: double.infinity,
          height: 44.h,
          decoration: BoxDecoration(
            color: ColorsUtil.primaryColor,
            borderRadius: BorderRadius.circular(4.r),
          ),
          child: Center(
            child: Text(
              "导出施工日志",
              style: TextStyle(
                color: Colors.white,
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
