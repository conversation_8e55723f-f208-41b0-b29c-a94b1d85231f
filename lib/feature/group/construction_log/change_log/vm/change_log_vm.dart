import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/logs_edit_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/logs_create_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/report_create_task_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/group_construction_log_repo.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/logs_getdetail_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/change_log/vm/change_log_us.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/photo_picker_util.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/change_weather/entity/change_weather_props.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/event_bus/construction_log_event_bus_model.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/log_content/entity/log_content_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/select_record_date_view.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/dialog_util.dart';

import 'package:gdjg_pure_flutter/feature/common_page/photo_viewer/photo_viewer_props.dart';
import 'package:intl/intl.dart';

/// 修改日志页面参数
class ChangeLogProps {
  /// 日志ID（0表示新建模式）
  final double? logId;

  /// 班组名称
  final String? groupName;

  /// 部门ID
  final String? deptId;

  const ChangeLogProps({
    this.logId,
    this.groupName,
    this.deptId,
  });
}

/// 修改日志页面ViewModel
class ChangeLogViewModel extends GetxController {
  final ChangeLogUS us = ChangeLogUS();
  final GroupConstructionLogRepo _groupConstructionLogRepo = GroupConstructionLogRepo();
  ChangeLogProps? _props;

  /// 施工人数输入控制器
  final TextEditingController _workerCountController = TextEditingController();

  /// 获取施工人数输入控制器
  TextEditingController get workerCountController => _workerCountController;

  @override
  void onInit() {
    super.onInit();

    // 监听输入变化
    _workerCountController.addListener(() {
      String value = _workerCountController.text;
      // 1-999的正整数
      if (value.isNotEmpty) {
        int? count = int.tryParse(value);
        if (count != null && count >= 1 && count <= 999) {
          us.setWorkerCount(value);
        }
      } else {
        us.setWorkerCount('');
      }
    });
  }

  void initialize(ChangeLogProps props) async {
    _props = props;
    final logId = props.logId ?? 0.0;
    us.logId.value = logId;

    if (logId > 0) {
      // 编辑模式：获取日志详情
      final result = await _groupConstructionLogRepo.getLogDetail(logId);
      if (result.isOK()) {
        final logData = result.getSucData();
        if (logData != null) {
          _initializeWithLogData(logData);
        } else {
          ToastUtil.showToast("获取日志详情失败");
        }
      } else {
        ToastUtil.showToast(result.fail?.errorMsg ?? "获取日志详情失败");
      }
    } else {
      // 新建模式
      _initializeForNewLog(props.groupName ?? '');
    }
  }

  /// 新建日志初始化
  void _initializeForNewLog(String groupName) {
    // 重置状态
    us.reset();

    // 设置默认值
    us.recordDate.value = DateTime.now();
    us.groupName.value = groupName;
    us.recorderName.value = '默认工人';
  }

  /// 数据初始化
  void _initializeWithLogData(LogsGetdetailBizModel logData) {
    // 保存原始数据
    us.originalData = logData;

    // 解析时间戳为日期
    DateTime recordDate = DateTime.now();
    if (logData.editTime > 0) {
      recordDate = DateTime.fromMillisecondsSinceEpoch((logData.editTime * 1000).toInt());
    }

    // 初始化状态数据
    us.logId.value = logData.id;
    us.recordDate.value = recordDate;
    us.groupName.value = logData.source;
    us.morningWeather.value = logData.dayweather;
    us.afternoonWeather.value = logData.nightweather;
    us.morningTemp.value = logData.daytemp;
    us.afternoonTemp.value = logData.nighttemp;
    us.recorderName.value = logData.username?.toString() ?? '记录员';

    // 设置施工人数，如果为0则保持空值以显示占位符
    final workerCountInt = logData.constructionPeoNum.toInt();
    if (workerCountInt > 0) {
      us.workerCount.value = workerCountInt.toString();
      _workerCountController.text = us.workerCount.value;
    } else {
      us.workerCount.value = '';
      _workerCountController.text = '';
    }

    us.logContent.value = logData.contents;
    us.images.value = logData.imgs.map((img) => ImgBizModel(imgUrl: img)).toList();

    // 记录原有图片数量
    us.originalImageCount = us.images.length;
  }

  /// 删除照片
  /// [index] 照片索引
  void deleteImage(int index) {
    if (index >= 0 && index < us.images.length) {
      us.images.removeAt(index);
    }
  }

  /// 上传单个图片
  /// [localPath] 本地路径
  /// [tempId] 临时ID
  Future<void> _uploadSingleImage(String localPath, String tempId) async {
    try {
      String uploadedUrl = await PhotoPickerUtil.uploadFile(localPath);
      if (uploadedUrl.isNotEmpty) {
        // 上传成功，移除占位符，添加到正式列表
        us.uploadingImages.removeWhere((item) => item.tempId == tempId);
        us.images.add(ImgBizModel(imgUrl: uploadedUrl));
      } else {
        // 上传失败，移除占位符
        us.uploadingImages.removeWhere((item) => item.tempId == tempId);
        ToastUtil.showToast("图片上传失败");
      }
    } catch (e) {
      // 上传异常，移除占位符
      us.uploadingImages.removeWhere((item) => item.tempId == tempId);
      ToastUtil.showToast("图片上传失败");
    }
  }

  /// 显示照片选择弹窗
  void showPhotoSelectionDialog(BuildContext context) {
    // 剩余可上传数量
    int remainingPhotoCount = 9 - us.images.length - us.uploadingImages.length;

    // 显示选择弹窗，处理本地路径
    DialogUtil.showUploadImageBottomSheet(
      context,
      onTap: (localPaths) {
        // 立即为每个选择的图片添加占位符
        for (String localPath in localPaths) {
          if (localPath.isNotEmpty) {
            String tempId = DateTime.now().millisecondsSinceEpoch.toString() + localPath.hashCode.toString();
            us.uploadingImages.add(UploadingImageState(
              localPath: localPath,
              tempId: tempId,
            ));

            // 异步上传
            _uploadSingleImage(localPath, tempId);
          }
        }
      },
      showVideoOption: false,
      maxPhotoCount: remainingPhotoCount,
      allowVideoInPicker: false,
      returnLocalPaths: true, // 返回本地路径
    );
  }

  /// 跳转到日志内容编辑页面
  void onLogContentEditTap() {
    YPRoute.openPage(
      RouteNameCollection.logContentEdit,
      params: LogContentProps(
        currentContent: us.logContent.value,
        onContentChanged: (String newContent) {
          // 更新日志内容
          us.logContent.value = newContent;
        },
      ),
    );
  }

  /// 天气修改按钮点击事件
  void onWeatherModifyTap() {
    YPRoute.openPage(
      RouteNameCollection.changeWeather,
      params: ChangeWeatherProps(
        recordDate: us.recordDate.value,
        morningWeather: us.morningWeather.value,
        afternoonWeather: us.afternoonWeather.value,
        morningTemp: us.morningTemp.value,
        afternoonTemp: us.afternoonTemp.value,
        onSave: (morningWeather, afternoonWeather, morningTemp, afternoonTemp) {
          // 更新天气数据
          us.setMorningWeather(morningWeather);
          us.setAfternoonWeather(afternoonWeather);
          us.setMorningTemp(morningTemp);
          us.setAfternoonTemp(afternoonTemp);
        },
      ),
    );
  }

  /// 显示日期选择弹窗
  void showSelectRecordDate() {
    YPRoute.openDialog(
      builder: (context) => SelectRecordDateView(
        isRecordWorkPoints: false,
        dateList: [],
        isMultiple: false,
        isChangeChoice: false,
        onSelect: (dateList) {
          if (dateList.isNotEmpty) {
            us.setRecordDate(dateList.first);
          }
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  /// 保存日志
  void saveLog() async {
    // 在保存前手动同步控制器的值到US状态
    us.setWorkerCount(_workerCountController.text);

    final isNewMode = us.logId.value <= 0;

    if (isNewMode) {
      await _createNewLog();
    } else {
      await _editExistingLog();
    }
  }

  /// 创建新日志
  Future<void> _createNewLog() async {
    // 获取部门ID
    final deptId = _props?.deptId ?? '';

    // 格式化当前时间
    final now = DateTime.now();
    final editTime = DateFormat('yyyy-MM-dd HH:mm').format(now);

    // 处理图片数据
    String imgUrl = '';
    List<Map<String, dynamic>> resourceExt = [];

    if (us.images.isNotEmpty) {
      // 所有图片URL用逗号分隔作为img_url
      imgUrl = us.images.map((img) => img.imgUrl).join(',');

      // 构建resource_ext数组
      for (final img in us.images) {
        resourceExt.add({
          'url': img.imgUrl,
          'resourceId': _extractResourceIdFromUrl(img.imgUrl),
          'resourceType': 0,
          'takeTime': editTime,
        });
      }
    }

    // 参数
    final params = LogsCreateParamModel(
      dept_id: deptId,
      dayweather: us.morningWeather.value,
      nightweather: us.afternoonWeather.value,
      daytemp: us.morningTemp.value,
      nighttemp: us.afternoonTemp.value,
      contents: us.logContent.value.trim(),
      edit_time: editTime,
      construction_peo_num: us.workerCount.value.trim(),
      img_url: imgUrl,
      resource_ext: resourceExt.isNotEmpty ? resourceExt : null,
    );

    final result = await _groupConstructionLogRepo.createLog(params);

    if (result.isOK()) {
      ToastUtil.showToast('日志创建成功');
      EventBusUtil.emit(ConstructionLogEventBusModel(action: 'create'));
      YPRoute.closePage();
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '创建失败');
    }
  }

  /// 编辑现有日志
  Future<void> _editExistingLog() async {
    // 获取部门ID（编辑模式从原始数据获取）
    final deptId = us.originalData?.deptId ?? 0.0;

    // 格式化当前时间
    final now = DateTime.now();
    final editTime = DateFormat('yyyy-MM-dd HH:mm').format(now);

    // 处理图片数据
    String imgUrl = '';
    List<Map<String, dynamic>> resourceExt = [];

    if (us.images.isNotEmpty) {
      // 所有图片URL用逗号分隔作为img_url
      imgUrl = us.images.map((img) => img.imgUrl).join(',');

      // 构建resource_ext数组（只包含新添加的图片）
      for (int i = us.originalImageCount; i < us.images.length; i++) {
        final img = us.images[i];
        resourceExt.add({
          'url': img.imgUrl,
          'resourceId': _extractResourceIdFromUrl(img.imgUrl),
          'resourceType': 0,
          'takeTime': editTime,
        });
      }
    }

    // 参数
    final params = LogsEditParamModel(
      dept_id: deptId,
      dayweather: us.morningWeather.value,
      nightweather: us.afternoonWeather.value,
      daytemp: us.morningTemp.value,
      nighttemp: us.afternoonTemp.value,
      contents: us.logContent.value.trim(),
      edit_time: editTime,
      log_id: us.logId.value,
      img_url: imgUrl,
      resource_ext: resourceExt.isNotEmpty ? resourceExt : null,
      recorder_user_id: us.originalData?.recorderUserId ?? 0.0,
      construction_peo_num: us.workerCount.value.trim(),
    );

    final result = await _groupConstructionLogRepo.editLog(params);

    if (result.isOK()) {
      ToastUtil.showToast('保存成功');
      EventBusUtil.emit(ConstructionLogEventBusModel(action: 'edit'));
      YPRoute.closePage();
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '保存失败');
    }
  }

  /// 从图片URL中提取resourceId
  String _extractResourceIdFromUrl(String url) {
    try {
      // 获取URL的最后一部分（文件名）
      final fileName = url.split('/').last;
      // 移除文件扩展名，获取resourceId
      final resourceId = fileName.split('.').first;
      return resourceId;
    } catch (e) {
      // 如果提取失败，返回当前时间戳作为备用
      return DateTime.now().millisecondsSinceEpoch.toString();
    }
  }

  /// 删除日志
  void deleteLog() async {
    final logId = us.logId.value;
    if (logId <= 0) {
      ToastUtil.showToast('日志ID无效');
      return;
    }

    // 调用删除接口
    final result = await _groupConstructionLogRepo.deleteLog(logId);

    if (result.isOK()) {
      ToastUtil.showToast('删除成功');
      // 发送事件通知刷新
      EventBusUtil.emit(ConstructionLogEventBusModel(action: 'delete'));
      YPRoute.closePage();
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '删除失败');
    }
  }

  /// 下载日志
  void downloadLog() async {
    if (us.originalData == null) {
      ToastUtil.showToast('日志数据未加载完成');
      return;
    }

    // 获取部门ID和日志ID
    final deptId = us.originalData!.deptId;
    final logId = us.logId.value;

    // 检查参数
    if (deptId <= 0) {
      ToastUtil.showToast('部门ID无效');
      return;
    }

    if (logId <= 0) {
      ToastUtil.showToast('日志ID无效');
      return;
    }

    final params = ReportCreateTaskParamModel(
      dept_id: deptId.toString(),
      log_id: logId.toString(),
    );

    // 调用接口
    final result = await _groupConstructionLogRepo.createExportTask(params);

    if (result.isOK()) {
      final bizModel = result.getSucData();
      if (bizModel != null && bizModel.scalar == true) {
        // 跳转到导出日志页面
        YPRoute.openPage(RouteNameCollection.exportLogList);
      } else {
        ToastUtil.showToast('创建导出任务失败');
      }
    } else {
      ToastUtil.showToast(result.fail?.errorMsg ?? '创建导出任务失败');
    }
  }

  /// 图片点击事件
  void onImageTap(String imageUrl, List<String> allImages) {
    int initialIndex = allImages.indexOf(imageUrl);
    if (initialIndex == -1) initialIndex = 0;

    // 跳转到图片查看器
    YPRoute.openPage(
      RouteNameCollection.photoViewer,
      params: PhotoViewerProps(
        imageUrls: allImages,
        initialIndex: initialIndex,
      ),
    );
  }

  /// 销毁资源
  @override
  void dispose() {
    _workerCountController.dispose();
    // 清理上传中的图片状态
    us.uploadingImages.clear();
    super.dispose();
  }

}
