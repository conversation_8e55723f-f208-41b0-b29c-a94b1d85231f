import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/search_log/entity/search_log_props.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/search_log/vm/search_log_vm.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/view/simple_construction_log_item_view.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/select_export_time/entity/select_export_time_props.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:get/get.dart';

/// 搜索日志页面
class SearchLogPage extends BaseFulPage {
  const SearchLogPage({super.key}) : super(appBar: null);

  @override
  createState() => _SearchLogPageState();
}

class _SearchLogPageState extends BaseFulPageState<SearchLogPage> {
  final SearchLogViewModel _viewModel = SearchLogViewModel();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    var props = routeParams as SearchLogProps?;
    _viewModel.init(props);
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _viewModel.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F6FA),
      appBar: _buildCustomAppBar(),
      body: _buildBody(),
    );
  }

  AppBar _buildCustomAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      leading: GestureDetector(
        onTap: () => YPRoute.closePage(),
        child: Container(
          padding: const EdgeInsets.only(left: 8.0),
          child: Image(
            image: AssetImage(Assets.commonIconArrowBack),
          ),
        ),
      ),
      leadingWidth: 38,
      titleSpacing: 08,
      title: Text(
        "返回",
        style: const TextStyle(
          color: Colors.black,
          fontSize: 22,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        GestureDetector(
          onTap: () {
            // 跳转到选择导出时间页面
            YPRoute.openPage(
              RouteNameCollection.selectExportTime,
              params: SelectExportTimeProps(
                deptId: _viewModel.props?.deptId,
              ),
            );
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: [
                Icon(
                  Icons.download,
                  color: ColorsUtil.primaryColor,
                  size: 20.w,
                ),
                SizedBox(width: 4.w),
                Text(
                  "下载",
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: ColorsUtil.primaryColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    return GestureDetector(
      onTap: () {
        //取消焦点
        FocusScope.of(context).unfocus();
      },
      child: Column(
        children: [
          // 搜索框
          _buildSearchSection(),

          Expanded(
            child: Obx(() => _buildContentArea()),
          ),
        ],
      ),
    );
  }

  /// 搜索框
  Widget _buildSearchSection() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 8.h, top: 2.h),
      child: Row(
        children: [
          Container(
            height: 36.h,
            padding: EdgeInsets.only(left: 12.w, right: 10.w),
            decoration: BoxDecoration(
              color: const Color(0xFFF0F0F0),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(4.r),
                bottomLeft: Radius.circular(4.r),
              ),
            ),
            child: Center(
              child: Image.asset(
                Assets.commonIconSearch,
                width: 16.w,
                height: 16.w,
                color: const Color(0xFF999999),
              ),
            ),
          ),
          Expanded(
            child: Container(
              height: 36.h,
              decoration: BoxDecoration(
                color: const Color(0xFFF0F0F0),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(4.r),
                  bottomRight: Radius.circular(4.r),
                ),
              ),
              child: _buildTextField(),
            ),
          ),
          SizedBox(width: 20.w),
          GestureDetector(
            onTap: _onCancelSearch,
            child: Text(
              '取消',
              style: TextStyle(
                fontSize: 16.sp,
                color: ColorsUtil.primaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 输入框
  Widget _buildTextField() {
    return Obx(() => TextField(
      controller: _searchController,
      focusNode: _searchFocusNode,
      onTap: _onSearchTap,
      onChanged: (value) => _viewModel.searchLogs(value),
      style: TextStyle(
        fontSize: 14.sp,
        color: const Color(0xFF999999),
      ),
      decoration: InputDecoration(
        border: InputBorder.none,
        contentPadding: EdgeInsets.only(top: 4.h, bottom: 10.h),
        hintText: '请输入日志内容',
        hintStyle: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF999999),
        ),
        suffixIcon: _viewModel.us.searchKeyword.isNotEmpty
            ? GestureDetector(
                onTap: _onClearSearch,
                child: Container(
                  padding: EdgeInsets.all(10.w),
                  child: Image.asset(
                    Assets.commonIconClose,
                    width: 18.w,
                    height: 18.w,
                    color: const Color(0xFF999999),
                  ),
                ),
              )
            : null,
      ),
    ));
  }

  Widget _buildContentArea() {
    if (_viewModel.us.isEmpty) {
      return _buildEmptyStateWidget(message: '请输入搜索内容');
    } else if (_viewModel.us.hasNoResult) {
      return _buildEmptyStateWidget(
        message: '暂无搜索结果',
        onRefresh: () async => _viewModel.refreshSearch(),
      );
    } else {
      return _buildSearchResultList();
    }
  }

  /// 空状态组件
  Widget _buildEmptyStateWidget({
    required String message,
    Future<void> Function()? onRefresh,
  }) {
    return RefreshIndicator(
      onRefresh: onRefresh ?? () async {},
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Container(
          width: double.infinity,
          height: MediaQuery.of(context).size.height * 0.7,
          padding: EdgeInsets.only(top: 80.h),
          child: Column(
            children: [
              Image.asset(
                Assets.commonIconEmptyTeamProject,
                width: 120.w,
                height: 120.w,
                fit: BoxFit.contain,
              ),
              Text(
                message,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: const Color(0xFF8A8A99),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 日志列表
  Widget _buildSearchResultList() {
    return Obx(() => RefreshIndicator(
      onRefresh: () async {
        _viewModel.refreshSearch();
      },
      child: ListView.builder(
        padding: EdgeInsets.symmetric(vertical: 4.h),
        itemCount: _viewModel.us.searchResultList.length,
        itemBuilder: (context, index) {
          final item = _viewModel.us.searchResultList[index];
          return Container(
            margin: EdgeInsets.only(bottom: 1.h),
            child: SimpleConstructionLogItemView(
              item: item,
              onItemTap: _viewModel.onLogItemTap,
            ),
          );
        },
      ),
    ));
  }

  /// 搜索框点击
  void _onSearchTap() {
    _searchFocusNode.requestFocus();
  }

  /// 取消按钮
  void _onCancelSearch() {
    YPRoute.closePage();
  }

  /// 清空搜索
  void _onClearSearch() {
    _searchController.clear();
    _viewModel.searchLogs('');
  }
}
