import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_event_bus/group_edit_wage_event_bus_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/entity/group_liquidated_detail_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/view/header_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/view/list_item_borrow_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/view/list_item_reporter_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/view/list_item_settled_liquidated_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/view/un_liquidated_detail_footer_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/view/list_item_un_liquidated_wages_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/vm/group_liquidated_detail_viewmodel.dart';
import 'package:gdjg_pure_flutter/feature/group/group_statistics/view/empty_view.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

///启动当前页面
///[workerId] 工友id
///[workNoteName] 项目名称
///[workNote] 项目id
///[isJoin] 是否加入不知道啥意思原来也没有注释
///[identity] 1进入班组本，2进入个人本
///[isAgent] 是否是带班 0-否 1-是
Future<Object?> startPageGroupUnLiquidatedDetail(
    {required List<WorkerModel> workers,
    required String? workNoteName,
    required String workNote,
    required double? deptId,
    required bool? isUnLiquidated,
    required double? isAgent,
    required bool? isJoin}) async {
  return await YPRoute.openPage(RouteNameCollection.liquidatedDetail,
      params: GroupLiquidatedDetailProps(
          workers: workers,
          workNoteName: workNoteName,
          workNote: workNote,
          deptId: deptId,
          isUnLiquidated: isUnLiquidated,
          isAgent: isAgent,
          isJoin: isJoin));
}

/// @date 2025/07/01
/// @param props 页面路由参数
/// @returns
/// @description 未结详情
class GroupLiquidatedDetailPage extends BaseFulPage {
  GroupLiquidatedDetailPage({super.key}) : super(appBar: YPAppBar());

  @override
  createState() => _GroupUnLiquidatedPageState();
}

///班组长未结页面状态
class _GroupUnLiquidatedPageState
    extends BaseFulPageState<GroupLiquidatedDetailPage> {
  final GroupLiquidatedDetailViewmodel viewModel =
      GroupLiquidatedDetailViewmodel();
  late GroupLiquidatedDetailProps? props;
  StreamSubscription? _stream;

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as GroupLiquidatedDetailProps?;
    dynamicTitle = props?.workNoteName ?? "";
    viewModel.initParam(props);
    viewModel.init();
  }

  @override
  void onPageCreate() {
    super.onPageCreate();
    _stream = EventBusUtil.collect<GroupEditWageEventBusModel>((data) {
      viewModel.fetchData();
    });
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _stream?.cancel();
  }

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(body: contentView());
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    return Column(
      children: [
        Container(
          color: ColorsUtil.f5f5f5,
          height: 8.h,
        ),
        Expanded(
          child: Obx(() {
            final listIsNotEmpty = viewModel.uiState.billList.isNotEmpty;
            if (listIsNotEmpty) {
              return Column(
                children: [
                  HeaderView(viewModel: viewModel),
                  Expanded(
                    child: Container(
                      color: Colors.white, // 👈 加这个
                      child: listWidget(),
                    ),
                  ),
                  if (viewModel.isShowUnSettledFooterUI())
                    UnLiquidatedDetailFooter(viewModel: viewModel),
                ],
              );
            }
            return EmptyView(subtitle: '暂无数据');
          }),
        ),
      ],
    );
  }

  ///列表数据
  Widget listWidget() {
    return ListView.builder(
      itemCount: viewModel.uiState.billList.length,
      itemBuilder: (context, index) {
        final uiState = viewModel.uiState.billList[index];
        if (uiState.type == UnliquidatedDetailType.report) {
          return ListItemReporterWidget(uiState: uiState, viewModel: viewModel);
        } else if (uiState.type == UnliquidatedDetailType.borrow) {
          return ListItemBorrow(uiState: uiState, viewModel: viewModel);
        }
        //已结ui
        if (!viewModel.isUnLiquidated()) {
          return SettledLiquidatedView(viewModel: viewModel);
        }
        //未结ui
        return ListItemUnLiquidatedDetailWages(
          haveBillList: viewModel.uiState.billList.isEmpty,
          uiState: uiState,
        );
      },
    );
  }
}
