import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import '../vm/group_liquidated_detail_viewmodel.dart';
import '../vm/protocol/group_liquidated_group_business_count_ui_state.dart';

///记工类型列表组件
class ListItemReporterWidget extends StatelessWidget {
  ///ui数据
  final GroupLiquidatedGroupBusinessCountListUiState uiState;

  ///viewModel
  final GroupLiquidatedDetailViewmodel viewModel;

  const ListItemReporterWidget({
    super.key,
    required this.uiState,
    required this.viewModel,
  });

  ///数据
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 5.h),
      child: Column(
        children: [
          // 内容区域
          InkWell(
            onTap: () {
              viewModel.onItemClickTap(uiState);
            },
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 8.w),
              padding: EdgeInsets.symmetric(vertical: 8.h),
              decoration: BoxDecoration(
                color: Color(0XFFF2F9FF),
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Row(
                children: [
                  // 左侧内容
                  leftWidget(uiState),
                  // 右侧金额 + 箭头
                  rightWidget(uiState)
                ],
              ),
            ),
          ),
          // 底部设置按钮区域
          bottomWidget(uiState)
        ],
      ),
    );
  }

  ///左侧内容
  Widget leftWidget(GroupLiquidatedGroupBusinessCountListUiState? uiState) {
    return Expanded(
      child: Row(
        children: [
          SizedBox(width: 8.w),
          // 标题 + sub
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                uiState?.name ?? "",
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF323233),
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (uiState?.nameSub?.isNotEmpty == true)
                Text(
                  uiState?.nameSub ?? "",
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: const Color(0xFF9D9DB3),
                  ),
                ),
            ],
          ),
          // 内容描述
          if (uiState?.content?.isNotEmpty == true)
            Container(
              margin: EdgeInsets.only(left: 8.w),
              child: Text(
                uiState?.content ?? "",
                style: TextStyle(
                  fontSize: 15.sp,
                  color: const Color(0xFF323233),
                ),
              ),
            ),
        ],
      ),
    );
  }

  ///右侧金额 + 箭头
  Widget rightWidget(GroupLiquidatedGroupBusinessCountListUiState? uiState) {
    return Padding(
      padding: EdgeInsets.only(right: 8.w),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                uiState?.money ?? "",
                style: TextStyle(
                  fontSize: 22.sp,
                  color: ColorsUtil.primaryColor,
                  fontFamily: FontUtil.fontCondMedium,
                ),
              ),
              if (uiState?.moneySub?.isNotEmpty == true)
                Text(
                  uiState?.moneySub ?? "",
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: const Color(0xFF323233),
                  ),
                ),
            ],
          ),
          IconFont(
            IconNames.saasArrowRight,
            size: 15,
            color: '#8A8A99',
          ),
        ],
      ),
    );
  }

  ///底部
  Widget bottomWidget(GroupLiquidatedGroupBusinessCountListUiState? data) {
    final isShow = data?.foot?.isNotEmpty == true && !viewModel.isJoin();
    return Visibility(
      visible: isShow,
      child: InkWell(
        onTap: () {
          viewModel.onSetClickTap(uiState);
        },
        child: Container(
          margin: EdgeInsets.only(left: 8.w, right: 8.w, bottom: 2.5.h),
          child: Row(
            children: [
              IconFont(
                IconNames.saasWarningCircleFill,
                size: 15,
                color: '#5290FD',
              ),
              Container(
                margin: EdgeInsets.only(left: 2.5.w, top: 5.w, bottom: 5.h),
                child: Text(
                  data?.foot ?? "",
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF323233),
                  ),
                ),
              ),
              const Spacer(),
              Text(
                '去设置',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ColorsUtil.primaryColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
