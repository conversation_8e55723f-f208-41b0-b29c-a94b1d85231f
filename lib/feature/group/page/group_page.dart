import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gdjg_pure_flutter/feature/feedback/util/feedback_config_util.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/feature/tabbar/view/role_switch_bar_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_mine/service/mine_route_service.dart';
import 'package:gdjg_pure_flutter/feature/group/group_mine/vm/group_mine_vm.dart';
import 'package:get/get.dart';
import '../group_mine/mine_page.dart';
import '../group_not_settled/group_not_settled_page.dart';
import '../group_project/group_project_page.dart';
import '../group_statistics/group_statistics_page.dart';
import '../us/group_us.dart';

class GroupPage extends GetView<GroupUS> {
  const GroupPage({super.key});

  @override
  Widget build(BuildContext context) {
    final List<Widget> pages = [
      const GroupProjectPage(),
      GroupStatisticsPage(),
      const GroupNotSettledPage(),
      const MinePage(),
    ];

    final GroupMineVm groupMineVm = GroupMineVm();
    groupMineVm.initData();

    var titleBarText = [
      "拍证据工资有保障",
      "视频教程",
      "视频教程",
      "客服",
    ];
    var titleBarIconAssets = [
      "",
      "assets/images/common/ic_take_phone_small.webp",
      "assets/images/common/ic_take_phone_small.webp",
      Assets.workerIconCustomerService,
    ];
    var titleBarClicks = [
      () {},
      () => MineRouteService.openVideoTutorials(groupMineVm.us.videoUrl),
      () => MineRouteService.openVideoTutorials(groupMineVm.us.videoUrl),
      () => _onConnectServiceClick(),
    ];

    return Scaffold(
      appBar: AppBar(
        titleSpacing: 0,
        title: Obx(
          () {
            return RoleSwitchBar(
              rightText: titleBarText[controller.currentIndex.value],
              rightIconAsset: titleBarIconAssets[controller.currentIndex.value],
              backgroundColor: Colors.white,
              onRightTap: titleBarClicks[controller.currentIndex.value],
            );
          },
        ),
        centerTitle: false,
        backgroundColor: Colors.white,
        elevation: 0,
      ),
      body: Obx(() => IndexedStack(
            index: controller.currentIndex.value,
            children: pages,
          )),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(
                color: Color(0x80CCCCCC),
                width: 0.5,
              ),
            )
        ),
        child: Obx(
          () => BottomNavigationBar(
            backgroundColor: Colors.white,
            type: BottomNavigationBarType.fixed,
            currentIndex: controller.currentIndex.value,
            onTap: controller.changeTabIndex,
            selectedItemColor: ColorsUtil.primaryColor,
            unselectedItemColor: Color(0xFF5A5A66),
            selectedFontSize: 14,
            unselectedFontSize: 14,
            items: [
              BottomNavigationBarItem(
                label: '项目',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 22,
                      height: 22,
                      image: controller.currentIndex.value == 0
                          ? const AssetImage('assets/images/tabbar/icon_tabbar_project_selected.webp')
                          : const AssetImage('assets/images/tabbar/icon_tabbar_project_nomal.webp'),
                    )),
              ),
               BottomNavigationBarItem(
                label: '统计',
                 icon: Padding(
                     padding: const EdgeInsets.only(bottom: 4),
                     child: Image(
                       fit: BoxFit.fill,
                       width: 22,
                       height: 22,
                       image: controller.currentIndex.value == 1
                           ? const AssetImage('assets/images/tabbar/icon_tabbar_statistic_selected.webp')
                           : const AssetImage('assets/images/tabbar/icon_tabbar_statistic_normal.webp'),
                     )),
              ),
               BottomNavigationBarItem(
                label: '未结',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 22,
                      height: 22,
                      image: controller.currentIndex.value == 2
                          ? const AssetImage('assets/images/tabbar/icon_tabbar_not_settled_selected.webp')
                          : const AssetImage('assets/images/tabbar/icon_tabbar_not_settled_normal.webp'),
                    )),
              ),
               BottomNavigationBarItem(
                label: '我的',
                icon: Padding(
                    padding: const EdgeInsets.only(bottom: 4),
                    child: Image(
                      fit: BoxFit.fill,
                      width: 22,
                      height: 22,
                      image: controller.currentIndex.value == 3
                          ? const AssetImage('assets/images/tabbar/icon_tabbar_mine_selected.webp')
                          : const AssetImage('assets/images/tabbar/icon_tabbar_mine_normal.webp'),
                    )),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _onConnectServiceClick() {
    // 获取动态构建的headers
    final headers = FeedbackConfigUtil.getHeaders();
    var url =
        'https://h5hybridtest.yupaowang.com/customer-page?businessService=bkabk-cs&headers=$headers'
        '&uid=${FeedbackConfigUtil.getUserId()}'
        '&session=${FeedbackConfigUtil.getUserToken()}';
    YPRoute.openWebPage(url: url, title: '记工记账');
  }
}