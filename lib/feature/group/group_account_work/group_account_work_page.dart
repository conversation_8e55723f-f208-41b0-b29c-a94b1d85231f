import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_account_work/entity/group_account_view_prop.dart';
import 'package:gdjg_pure_flutter/feature/group/group_account_work/view/group_account_view/group_account_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_account_work/view/group_settlement_view/group_settlement_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_account_work/vm/group_account_work_viewmodel.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/select_record_date_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/dynamic_title_helper.dart';
import 'package:gdjg_pure_flutter/widget/round_tab_indicator_widget.dart';
import 'package:get/get.dart';

class GroupAccountWorkPage extends BaseFulPage {
  GroupAccountWorkPage({super.key}) : super(appBar: YPAppBar(title: '标题'));

  @override
  State<GroupAccountWorkPage> createState() => _GroupAccountWorkPageState();
}

class _GroupAccountWorkPageState extends BaseFulPageState<GroupAccountWorkPage>
    with SingleTickerProviderStateMixin {
  final viewModel = Get.put(GroupAccountWorkViewModel());
  late GroupRecordWorkProps props;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as GroupRecordWorkProps;
    viewModel.onInit(props);
    // 设置项目标题监听
    _setupProjectTitleListener();
  }

  @override
  Widget yBuild(BuildContext context) {
    return _buildContentView();
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    Get.delete<GroupAccountWorkViewModel>();
  }

  /// 设置项目标题监听器
  void _setupProjectTitleListener() {
    // 监听 deptDetailUS 响应式变量的变化
    DynamicTitleHelper.listenAndUpdateTitle(
      this,
      viewModel.us.deptDetailUSObservable, // 监听响应式变量
      (deptDetail) => DynamicTitleHelper.formatProjectTitle(
        deptDetail?.workNoteName, // 使用 workNoteName 属性
        maxLength: 15, // 限制标题长度
      ),
      defaultTitle: "班组借支结算",
    );
  }

  Widget _buildContentView() {
    return Column(
      children: [
        _buildDateView(),
        _buildPageView(),
        _buildBottomView(),
      ],
    );
  }

  /// 项目日期选择
  Widget _buildDateView() {
    return GestureDetector(
      onTap: () {
        showSelectRecordDate();
      },
      child: Container(
        width: double.infinity,
        margin: EdgeInsets.only(top: 8.h),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(width: 0.5.h, color: ColorsUtil.ypBgColor),
          ),
          color: Colors.white,
        ),
        height: 45,
        child: Center(
          child: Row(
            children: [
              Text('日期：',
                  style: TextStyle(fontSize: 17, color: Color(0xFF323233))),
              Obx(() => Text(viewModel.us.getSelectDateDisplay(),
                  style: TextStyle(
                      fontSize: 17,
                      color: Color(0xFF323233),
                      fontWeight: FontWeight.w500))),
              Spacer(),
              if (viewModel.us.selectDates.length > 1)
                Text('可多选',
                    style: TextStyle(fontSize: 14, color: Color(0xFF323233))),
              SizedBox(width: 4),
              Image.asset(Assets.commonIconArrowRightGrey,
                  width: 18, height: 18)
            ],
          ),
        ),
      ),
    );
  }

  /// 选择记工日期
  void showSelectRecordDate() {
    YPRoute.openDialog(
      builder: (context) => SelectRecordDateView(
        note_id: viewModel.us.deptDetailUS.workNoteId,
        isRecordWorkPoints: true,
        dateList: viewModel.us.selectDates,
        isChangeChoice: false,
        isMultiple: true,
        onSelect: (dateList) {
          viewModel.onSelectDateChange(dateList);
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  /// 构建页面主题
  Widget _buildPageView() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildTitleBar(),
          _buildAccountContent(),
        ],
      ),
    );
  }

  Widget _buildTitleBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelStyle: TextStyle(
          fontSize: 17.sp,
        ),
        dividerHeight: 1,
        dividerColor: ColorsUtil.ypBgColor,
        indicator: RoundUnderlineTabIndicator(
          borderSide: BorderSide(width: 3, color: ColorsUtil.ypPrimaryColor),
        ),
        tabs: [
          Tab(
            child: Text(BusinessType.workAndAccountTypeDebt.desc),
          ),
          Tab(
            child: Text(BusinessType.workAndAccountTypeWageLast.desc),
          ),
        ],
      ),
    );
  }

  Widget _buildAccountContent() {
    return Expanded(
      child: Container(
        color: Colors.white,
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildAccountTab(),
            _buildSettlementTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomView() {
    return Container(
      color: Colors.white,
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: ColorsUtil.ypPrimaryColor,
        ),
        child: TextButton(
          onPressed: () {},
          child: Text(
            '确认记账',
            style: TextStyle(
                color: Colors.white,
                fontSize: 17.sp,
                fontWeight: FontWeight.bold),
          ),
        ),
      ),
    );
  }

  Widget _buildAccountTab() {
    return Obx(() {
      return GroupAccountView(
        props: GroupAccountViewProp(
          workers: viewModel.us.onSiteWorkers,
          exitWorkers: viewModel.us.exitWorkers,
          restWorkers: viewModel.us.restWorkers,
          workNoteId: viewModel.us.deptDetailUS.workNoteId,
        ),
      );
    });
  }

  Widget _buildSettlementTab() {
    return Obx(() {
      return GroupSettlementView(
        props: GroupAccountViewProp(
          workers: viewModel.us.onSiteWorkers,
          exitWorkers: viewModel.us.exitWorkers,
          restWorkers: viewModel.us.restWorkers,
          workNoteId: viewModel.us.deptDetailUS.workNoteId,
        ),
      );
    });
  }
}
