import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/group/group_account_work/entity/group_account_view_prop.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/entity/worker_add_select_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/worker_edit_list_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_controller.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_view.dart';

class GroupSettlementView extends StatefulWidget {
  final GroupAccountViewProp props;

  const GroupSettlementView({super.key, required this.props});

  @override
  State<GroupSettlementView> createState() => _GroupSettlementViewState();
}

class _GroupSettlementViewState extends State<GroupSettlementView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late WorkLoadController _workLoadController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _workLoadController = WorkLoadController();
  }

  @override
  Widget build(BuildContext context) {
    return _buildContentView();
  }

  Widget _buildContentView() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildView(),
          _buildAccountView(),
        ],
      ),
    );
  }

  Widget _buildView() {
    return Container(
      width: double.infinity,
      color: ColorsUtil.yellowMedium,
      height: 400,
      child: Text('在场, 退场, 休假tab切换 区域'),
    );
  }

  Widget _buildTitleBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        tabs: [
          Tab(
            child: Text('在场'),
          ),
          Tab(
            child: Text('退场'),
          ),
          Tab(
            child: Text('休假'),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Container(
      color: Colors.white,
      child: TabBarView(
        controller: _tabController,
        children: [
          Center(
            child: WorkerAddSelectView(
                props: WorkerAddSelectProps(
              workers: widget.props.workers,
              onSelected: onSelected,
            )),
          ),
          Center(
            child: WorkerAddSelectView(
                props: WorkerAddSelectProps(
              workers: widget.props.exitWorkerList,
              onSelected: onSelected,
            )),
          ),
          Center(
            child: WorkerAddSelectView(
                props: WorkerAddSelectProps(
              workers: widget.props.restWorkerList,
              onSelected: onSelected,
            )),
          ),
        ],
      ),
    );
  }

  void onSelected(List<double> workerIds) {
    yprint("结算tab-选择工友 ==========>> $workerIds");
  }

  Widget _buildAccountView() {
    return SingleChildScrollView(
      child: WorkLoadView(
        controller: _workLoadController,
        noteId: widget.props.workNoteId,
      ),
    );
  }
}
