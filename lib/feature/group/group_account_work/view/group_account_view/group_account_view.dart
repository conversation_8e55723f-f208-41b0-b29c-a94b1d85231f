import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_account_work/entity/group_account_view_prop.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/entity/worker_add_select_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/worker_edit_list_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/round_tab_indicator_widget.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_controller.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_view.dart';

class GroupAccountView extends StatefulWidget {
  final GroupAccountViewProp props;

  const GroupAccountView({super.key, required this.props});

  @override
  State<GroupAccountView> createState() => _GroupAccountViewState();
}

class _GroupAccountViewState extends State<GroupAccountView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late WorkLoadController _workLoadController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _workLoadController = WorkLoadController();
  }

  @override
  Widget build(BuildContext context) {
    return _buildContentView();
  }

  Widget _buildContentView() {
    return CustomScrollView(
      slivers: [
        SliverToBoxAdapter(
          child: _buildTitleBar(),
        ),
        SliverFillRemaining(
          hasScrollBody: false,
          child: Column(
            children: [
              Expanded(child: _buildBody()),
              _buildAccountView(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTitleBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        dividerHeight: 1,
        dividerColor: ColorsUtil.ypBgColor,
        labelStyle: TextStyle(
          fontSize: 16.sp,
        ),
        indicator: RoundUnderlineTabIndicator(
          borderSide: BorderSide(
            color: ColorsUtil.ypPrimaryColor,
            width: 3,
          ),
        ),
        tabs: [
          Tab(
            child: Text('在场'),
          ),
          Tab(
            child: Text('退场'),
          ),
          Tab(
            child: Text('休假'),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return TabBarView(
      controller: _tabController,
      children: [
        SingleChildScrollView(
          child: WorkerAddSelectView(
            props: WorkerAddSelectProps(
              workers: widget.props.workers,
              onSelected: onSelected,
            ),
          ),
        ),
        SingleChildScrollView(
          child: WorkerAddSelectView(
            props: WorkerAddSelectProps(
              workers: widget.props.exitWorkerList,
              onSelected: onSelected,
            ),
          ),
        ),
        SingleChildScrollView(
          child: WorkerAddSelectView(
            props: WorkerAddSelectProps(
              workers: widget.props.restWorkerList,
              onSelected: onSelected,
            ),
          ),
        ),
      ],
    );
  }

  void onSelected(List<double> workerIds) {
    yprint("借支tab-选择工友 ==========>> $workerIds");
  }

  Widget _buildAccountView() {
    return SizedBox(
      height: 200, // 固定高度，避免布局问题
      child: WorkLoadView(
        controller: _workLoadController,
        noteId: widget.props.workNoteId,
      ),
    );
  }
}
