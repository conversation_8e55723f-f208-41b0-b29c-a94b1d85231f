import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_account_work/entity/group_account_view_prop.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/entity/worker_add_select_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/worker_edit_list_view.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/round_tab_indicator_widget.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_controller.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_view.dart';

class GroupAccountView extends StatefulWidget {
  final GroupAccountViewProp props;

  const GroupAccountView({super.key, required this.props});

  @override
  State<GroupAccountView> createState() => _GroupAccountViewState();
}

class _GroupAccountViewState extends State<GroupAccountView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late WorkLoadController _workLoadController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _workLoadController = WorkLoadController();
  }

  @override
  Widget build(BuildContext context) {
    return _buildContentView();
  }

  Widget _buildContentView() {
    return Column(
      children: [
        _buildTitleBar(),
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(
                  height: 300, // 给 TabBarView 一个合适的高度
                  child: _buildBody(),
                ),
                _buildAccountView(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTitleBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        dividerHeight: 1,
        dividerColor: ColorsUtil.ypBgColor,
        labelStyle: TextStyle(
          fontSize: 16.sp,
        ),
        indicator: RoundUnderlineTabIndicator(
          borderSide: BorderSide(
            color: ColorsUtil.ypPrimaryColor,
            width: 3,
          ),
        ),
        tabs: [
          Tab(
            child: Text('在场'),
          ),
          Tab(
            child: Text('退场'),
          ),
          Tab(
            child: Text('休假'),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return TabBarView(
      controller: _tabController,
      children: [
        WorkerAddSelectView(
          props: WorkerAddSelectProps(
            workers: widget.props.workers,
            onSelected: onSelected,
          ),
        ),
        WorkerAddSelectView(
          props: WorkerAddSelectProps(
            workers: widget.props.exitWorkerList,
            onSelected: onSelected,
          ),
        ),
        WorkerAddSelectView(
          props: WorkerAddSelectProps(
            workers: widget.props.restWorkerList,
            onSelected: onSelected,
          ),
        ),
      ],
    );
  }

  void onSelected(List<double> workerIds) {
    yprint("借支tab-选择工友 ==========>> $workerIds");
  }

  Widget _buildAccountView() {
    // 检查 workNoteId 是否为空，避免 null check 错误
    final noteId = widget.props.workNoteId;
    if (noteId.isEmpty) {
      return Container(
        padding: EdgeInsets.all(20.w),
        child: Center(
          child: Text(
            '工作账本ID为空',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      child: WorkLoadView(
        controller: _workLoadController,
        noteId: noteId,
      ),
    );
  }
}
