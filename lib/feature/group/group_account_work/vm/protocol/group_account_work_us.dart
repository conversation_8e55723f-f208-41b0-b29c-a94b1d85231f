import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/net_model_dept_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/vm/protocol/dept_detail_us.dart';
import 'package:gdjg_pure_flutter/utils/system_util/date_util.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';

class GroupAccountWorkUs {
  /// 选择记工单的日期集合
  final _selectDates = <DateTime>[].obs;

  /// 项目详情
  final _deptDetailUS = DeptDetailUS(workNoteName: '', workNoteId: '').obs;

  /// 用户是否记过工
  final _hasBusiness = false.obs;

  /// 出勤工友
  final _onSiteWorkers = <WorkerBizModel>[].obs;

  /// 退场工友
  final _exitWorkers = <NoteWorkerBizModel>[].obs;

  /// 休假工友
  final _restWorkers = <NoteWorkerBizModel>[].obs;

  List<DateTime> get selectDates => _selectDates.value;

  DeptDetailUS get deptDetailUS => _deptDetailUS.value;

  /// 获取响应式的项目详情对象，用于监听变化
  Rx<DeptDetailUS> get deptDetailUSObservable => _deptDetailUS;

  bool get hasBusiness => _hasBusiness.value;

  List<WorkerBizModel> get onSiteWorkers => _onSiteWorkers.value;

  List<NoteWorkerBizModel> get exitWorkers => _exitWorkers.value;

  List<NoteWorkerBizModel> get restWorkers => _restWorkers.value;

  void setSelectList(List<DateTime> list) {
    _selectDates.value = list;
  }

  void setDeptDetailUS(DeptDetailUS value) => _deptDetailUS.value = value;

  void setHasBusiness(bool value) => _hasBusiness.value = value;

  void setOnSiteWorkers(List<WorkerBizModel> list) {
    _onSiteWorkers.value = list;
  }

  void setExitWorkers(List<NoteWorkerBizModel> list) {
    _exitWorkers.value = list;
  }

  void setRestWorkers(List<NoteWorkerBizModel> list) {
    _restWorkers.value = list;
  }

  String getSelectDateDisplay() {
    var str = '';
    switch (selectDates.length) {
      case 0:
        str = '请选择日期';
        break;
      case 1:
        var df = DateFormat('yyyy年MM月dd日');
        str = df.format(selectDates.first);
        break;
      default:
        str = '共选择${selectDates.length}天';
    }
    return str;
  }

  List<String> getSelectDateFormat() {
    if (selectDates.isNotEmpty) {
      return selectDates.map((e) => DateUtil.formatDate(e)).toList();
    }
    return [];
  }
}
