import 'dart:math';

import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/net_model_dept_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_detail_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/wage_source_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_account_work/vm/protocol/group_account_work_us.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/ui_rep/group_record_work_ui_rep.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/vm/protocol/dept_detail_us.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:get/get.dart';

class GroupAccountWorkViewModel {
  late GroupRecordWorkProps _props;
  var us = GroupAccountWorkUs();
  var uiRep = GroupRecordWorkUIRep();
  var isLoading = false.obs;
  var isShowError = false.obs;

  /// 出勤工友原始数据
  var originalWorkers = <WorkerBizModel>[];

  /// 退场工友数据
  var exitWorkers = <NoteWorkerBizModel>[];

  /// 休假工友数据
  var restWorkers = <NoteWorkerBizModel>[];

  /// 工资来源数据
  var wageSource = WageSourceBizModel();

  /// 记账页数据初始化
  void onInit(GroupRecordWorkProps props) async {
    _props = props;

    /// 1.1 获取用户是否记过工
    hasBusiness();

    /// 2 获取项目下的退场工友数据
    getGroupExitWorkers(_props.deptId);

    /// 3 获取项目下的休假工友数据
    getGroupRestWorkers(_props.deptId);

    /// 4 根据项目id请求项目详情-dept/detail
    var deptDetail = await fetchDeptDetail(_props.deptId);
    if (deptDetail == null) {
      return;
    }
    us.setDeptDetailUS(DeptDetailUS(
      workNoteId: deptDetail.workNoteId.toString(),
      workNoteName: deptDetail.name,
    ));

    /// 4.1 切换企业
    corpSelect(deptDetail.corpId.toString());

    /// 4.2 如果有项目详情，则获取项目下的工友数据
    final dates = us.getSelectDateFormat();
    fetchWorkerData(dates, deptDetail.workNoteId.toString());

    /// 4.3 获取工资来源
    getWageSource(deptDetail.workNoteId.toString());
  }

  void onSelectDateChange(List<DateTime> date) {
    yprint('选择日期 ========> $date');
    us.setSelectList(date);

    /// 1. 从新获取，该日期下的工友数据
    /// 2. 获取改日期的流水数据
  }

  /// 获取用户是否记过工
  void hasBusiness() async {
    final result = await uiRep.hasBusiness();
    yprint('用户是否记过工 ========> $result');
    us.setHasBusiness(result);
  }

  /// 获取项目详情
  Future<DeptDetailBizModel?> fetchDeptDetail(int deptId) async {
    isLoading.value = true;
    try {
      // 获取班组详情
      return await uiRep.getDeptDetail(deptId);
    } catch (e) {
      isShowError.value = true;
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// 获取项目下的退场工友数据
  void getGroupExitWorkers(int? deptId) async {
    if (deptId == null) {
      yprint('项目id不能为空1');
      return;
    }
    var workers = await uiRep.getGroupExitWorker(deptId.toString());
    exitWorkers = workers;
    us.setExitWorkers(workers);
  }

  /// 获取项目下的休假工友数据
  void getGroupRestWorkers(int? deptId) async {
    if (deptId == null) {
      yprint('项目id不能为空1');
      return;
    }
    var workers = await uiRep.getGroupRestWorker(deptId.toString());
    restWorkers = workers;
    us.setRestWorkers(workers);
  }

  /// 获取并记录工资来源数据
  void getWageSource(String workNoteId) async {
    var source = await uiRep.getWageSource(workNoteId);
    if (source == null) {
      return;
    }
    wageSource = source;
  }

  /// 切换企业
  void corpSelect(String corpId) async {
    uiRep.getCorpSelect(corpId);
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchWorkerData(List<String> date, String workerNoteId) async {
    yprint('当前日期 YYYY-MM-DD, 可多选$date');
    isLoading.value = true;
    try {
      // 获取工友数据
      var result = await uiRep.getWorkerAtWork(date, workerNoteId);
      originalWorkers = result?.workers ?? [];
      yprint('获取工友数据 ========> ${originalWorkers.length}');
      us.setOnSiteWorkers(originalWorkers);
    } catch (e) {
      isShowError.value = true;
    } finally {
      isLoading.value = false;
    }
  }
}
