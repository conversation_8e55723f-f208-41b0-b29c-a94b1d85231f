import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/net_model_dept_biz_model.dart';

class GroupAccountViewProp {
  /// 在场工友集
  final List<WorkerBizModel> workers;

  /// 退场工友集
  final List<NoteWorkerBizModel> exitWorkers;

  /// 休假工友集
  final List<NoteWorkerBizModel> restWorkers;

  /// 账本id
  final String workNoteId;

  GroupAccountViewProp({
    required this.workers,
    required this.exitWorkers,
    required this.restWorkers,
    required this.workNoteId,
  });

  List<WorkerBizModel> get exitWorkerList {
    return exitWorkers
        .map((e) => WorkerBizModel(
              id: e.workerId.toDouble(),
              name: e.name,
              isSelf: e.isSelf,
              memberId: e.memberId,
              avatar: e.avatar,
            ))
        .toList();
  }

  List<WorkerBizModel> get restWorkerList {
    return restWorkers
        .map((e) => WorkerBizModel(
              id: e.workerId.toDouble(),
              name: e.name,
              isSelf: e.isSelf,
              memberId: e.memberId,
              avatar: e.avatar,
            ))
        .toList();
  }

  @override
  String toString() {
    return "GroupAccountViewProp{workers: ${workers.toString()}, exitWorkers: ${exitWorkers.toString()}, restWorkers: ${restWorkers.toString()}}";
  }
}
