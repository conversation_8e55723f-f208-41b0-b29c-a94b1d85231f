import 'package:get/get.dart';

/// @date 2025/08/02
/// @description 班组未结工友UI状态
class GroupNotSettledUS {
  /// 工友列表数据
  final _workerList = <GroupNotSettledWorkerUIState>[].obs;

  /// 总未结金额
  final _totalMoney = ''.obs;

  /// 金额是否可见
  final _isAmountVisible = true.obs;

  /// 已选择的工友名称列表
  final _selectedWorkerNames = <String>[].obs;

  /// 已选择的工友ID列表
  final _selectedWorkerIds = <String>[].obs;

  /// 获取工友列表
  RxList<GroupNotSettledWorkerUIState> get workerList => _workerList;

  /// 获取总未结金额
  String get totalMoney => _totalMoney.value;

  /// 获取工友总数
  int get totalCount => _workerList.length;

  /// 获取金额是否可见
  bool get isAmountVisible => _isAmountVisible.value;

  /// 获取已选择的工友名称列表
  RxList<String> get selectedWorkerNames => _selectedWorkerNames;

  /// 获取已选择的工友ID列表
  RxList<String> get selectedWorkerIds => _selectedWorkerIds;

  /// 获取是否有筛选条件
  bool get hasFilter => _selectedWorkerIds.isNotEmpty;

  /// 设置工友列表
  void setWorkerList(List<GroupNotSettledWorkerUIState> list) {
    _workerList.assignAll(list);
  }

  /// 设置总未结金额
  void setTotalMoney(String money) {
    _totalMoney.value = money;
  }

  /// 切换金额可见性
  void toggleAmountVisibility() {
    _isAmountVisible.value = !_isAmountVisible.value;
  }

  /// 设置选中的工友
  void setSelectedWorkers(List<String> workerNames, List<String> workerIds) {
    _selectedWorkerNames.assignAll(workerNames);
    _selectedWorkerIds.assignAll(workerIds);
  }

  /// 清除筛选条件
  void clearFilter() {
    _selectedWorkerNames.clear();
    _selectedWorkerIds.clear();
  }

  /// 格式化金额显示
  String formatAmount(String amount) {
    return _isAmountVisible.value ? amount : '****';
  }
}

/// 工友UI状态数据
class GroupNotSettledWorkerUIState {
  /// 工友ID
  final String workerId;

  /// 工友姓名
  final String name;

  /// 未结金额（格式化后的字符串）
  final String unSettledMoney;

  GroupNotSettledWorkerUIState({
    this.workerId = '',
    this.name = '',
    this.unSettledMoney = '0.00',
  });
}
