import "package:flutter/material.dart";
import "package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/net/cors_note_group_record_work_statistics_v2_biz_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/net/cors_note_worker_in_group_note_biz_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/param/cors_note_group_record_work_statistics_v2_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_statistics/ds/model/param/cors_note_worker_in_group_note_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_statistics/repo/group_statistics_list_repo.dart";
import "package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart";
import "package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_com_uistate/group_worker_statistics_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_statistics/view/group_project_select_dialog.dart";
import "package:gdjg_pure_flutter/feature/group/group_statistics/vm/protocol/group_statistics_us.dart";
import "package:gdjg_pure_flutter/feature/group/group_statistics/vm/protocol/worker_in_project_us.dart";
import "package:gdjg_pure_flutter/feature/group/worker_manager/worker_selector/vm/protocol/worker_selector_ui_state.dart";
import "package:gdjg_pure_flutter/init_module/init_route.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:gdjg_pure_flutter/utils/system_util/string_util.dart";
import "package:gdjg_pure_flutter/utils/system_util/yprint.dart";
import "package:gdjg_pure_flutter/utils/ui_util/toast_util.dart";

/// @date 2025/06/21
/// @description GroupStatistics页ViewModel
/// 参照LoginVM模式重构，废弃UIRep
class GroupStatisticsVM {
  final _repo = GroupStatisticsListRepo();
  final us = GroupStatisticsUS();
  final _groupStatisticsParams =
      CorsNoteGroupRecordWorkStatisticsV2ParamModel();
  final _workerInProjectParams = CorsNoteWorkerInGroupNoteParamModel();
  bool _isInitialized = false;

  // 防抖相关
  bool _isShowingDialog = false;

  GroupStatisticsVM() {
    // 设置默认时间参数
    _groupStatisticsParams
      ..start_business_time = "${DateTime.now().year - 1}-01-01"
      ..end_business_time = _formatDate(DateTime.now());

    // 初始加载数据
    fetchGroupStatisticsData();
  }

  /// 触发初始化回调（仅在页面首次加载时调用）
  void triggerInitialCallback(Function(DateTime, DateTime) callback) {
    if (!_isInitialized) {
      _isInitialized = true;
      final startDate = DateTime(DateTime.now().year - 1, 1, 1);
      final endDate = DateTime.now();
      callback(startDate, endDate);
    }
  }

  /// 更新开始和结束时间
  void updateDateRange(DateTime startTime, DateTime endTime) {
    String startDate = _formatDate(startTime);
    String endDate = _formatDate(endTime);
    _groupStatisticsParams
      ..start_business_time = startDate
      ..end_business_time = endDate;
    fetchGroupStatisticsData();
  }

  /// 更新选择类型
  void updateSelectType(List<RwaRecordType> type) {
    _groupStatisticsParams.business_type =
        type.map((type) => type.code.value.toString()).join(',') ?? '';
    fetchGroupStatisticsData();
  }

  /// 更新选择的工友
  void updateWorkerSelected(List<WorkerContactUIState> selectedWorkers) {
    final workerIds = selectedWorkers
        .where((worker) => worker.id?.isNotEmpty == true)
        .map((worker) => worker.id!.trimTrailingZeros())
        .join(',');

    _groupStatisticsParams.worker_id = workerIds;
    fetchGroupStatisticsData();
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  /// 获取被点击项的业务类型列表
  List<RwaRecordType> _getClickedItemBusinessType(StatisticsItemUIState item) {
    // 这里未接也默认选中全部
    if (item.typeName == "未结") {
      return [];
    }
    // 否则返回该项的具体类型
    return [item.recordType];
  }

  /// 统计项点击事件
  Future<void> onStatisticsItemTap(BuildContext context,
      StatisticsItemUIState item, String workerName, double workerId) async {
    print(
        "=======点击项数据===11=${item}====2222=====${workerName}===333==${workerId}");
    // 防抖
    if (_isShowingDialog) return;
    _isShowingDialog = true;
    try {
      _workerInProjectParams
        ..worker_id = workerId.toInt().toString()
        ..no_fee = "2"
        ..business_type = item.recordType.code.value.toString() == "9"
            ? ""
            : item.recordType.code.value.toString();
      // 先获取工人项目数据
      await fetchWorkerInProjectData();
      // 将项目数据转换为弹窗需要的格式
      final List<ProjectSelectItem> dialogData =
          us.workerProjects.map((project) {
        return ProjectSelectItem(
          tag: "班组",
          name: project.projectName ?? "",
          deptId: project.deptId ?? 0,
          workNoteId: project.workNoteId?.toInt().toString() ?? "",
        );
      }).toList();
      // 显示弹窗
      if (dialogData.isNotEmpty) {
        if (dialogData.length > 1) {
          GroupProjectSelectDialog.show(
            context,
            data: dialogData,
            onSelected: (selectedItem) {
              GroupProBillProps params = GroupProBillProps()
                ..workNoteId = selectedItem.workNoteId
                ..workNoteName = selectedItem.name
                ..startTime =
                    DateTime.parse(_groupStatisticsParams.start_business_time)
                ..endTime =
                    DateTime.parse(_groupStatisticsParams.end_business_time)
                ..deptId = selectedItem.deptId
                ..businessType = _getClickedItemBusinessType(item)
                ..workers = [
                  WorkerModel(workerId: workerId, workerName: workerName)
                ];
              YPRoute.openPage(RouteNameCollection.groupProBill,
                  params: params);
            },
            onDismiss: () {
              _isShowingDialog = false;
            },
          );
        } else {
          _isShowingDialog = false;
          GroupProBillProps params = GroupProBillProps()
            ..workNoteId =
                us.workerProjects.first.workNoteId?.toInt().toString() ?? ""
            ..workNoteName = us.workerProjects.first.projectName ?? ""
            ..startTime =
                DateTime.parse(_groupStatisticsParams.start_business_time)
            ..endTime = DateTime.parse(_groupStatisticsParams.end_business_time)
            ..deptId = us.workerProjects.first.deptId
            ..businessType = _getClickedItemBusinessType(item)
            ..workers = [
              WorkerModel(workerId: workerId, workerName: workerName)
            ];
          YPRoute.openPage(RouteNameCollection.groupProBill, params: params);
        }
      } else {
        _isShowingDialog = false;
      }
    } catch (e) {
      _isShowingDialog = false;
    }
  }

  /// 获取统计数据
  Future<void> fetchGroupStatisticsData() async {
    us.setShowError(false);
    try {
      final result =
          await _repo.queryGroupStatisticsList(_groupStatisticsParams);
      if (result.isOK()) {
        final bizData = result.getSucData();
        if (bizData != null) {
          final uiStateList = _convertToGroupStatisticsUS(bizData);
          us.setGroupStatisticsData(uiStateList);
        }
      } else {
        us.setShowError(true);
        ToastUtil.showToast(result.fail?.errorMsg ?? "获取数据失败");
      }
    } catch (e) {
      us.setShowError(true);
    }
  }

  ///获取工人项目数据
  Future<void> fetchWorkerInProjectData() async {
    us.setShowError(false);
    try {
      final result = await _repo.queryWorkerInGroupNote(_workerInProjectParams);
      if (result.isOK()) {
        final bizData = result.getSucData();
        if (bizData != null) {
          final uiStateList = _convertToWorkerProjectUS(bizData);
          us.setWorkerProjects(uiStateList);
        }
      } else {
        us.setShowError(true);
      }
    } catch (e) {
      us.setShowError(true);
    }
  }

  /// 将数据转成工人项目US
  List<WorkerInProjectUS> _convertToWorkerProjectUS(
      CorsNoteWorkerInGroupNoteBizModel? bizData) {
    if (bizData == null || bizData.list.isEmpty) return [];
    final List<WorkerInProjectUS> workerInProjectList = [];
    for (var projectData in bizData.list) {
      workerInProjectList.add(WorkerInProjectUS(
        workNoteId: projectData.id,
        projectName: projectData.name,
        identity: projectData.identity,
        deptId: projectData.deptId,
      ));
    }
    return workerInProjectList;
  }

  /// 数据转换成班组列表数据US
  List<GroupWorkerStatisticsUIState> _convertToGroupStatisticsUS(
      CorsNoteGroupRecordWorkStatisticsV2BizModel? bizData) {
    if (bizData == null || bizData.list.isEmpty) return [];

    final List<GroupWorkerStatisticsUIState> workerStatisticsList = [];
    for (var workerData in bizData.list) {
      final List<StatisticsItemUIState> statisticsItemList = [];

      // 1. 点工
      if (workerData.spotWork != null && workerData.spotWorkNum > 0) {
        final detail = _buildWorkDetailString(
          workTime: _formatWorkTime(workerData.spotWork?.workTime ?? ""),
          workTimeHour: workerData.spotWork?.workTimeHour ?? 0.0,
          overtimeWork:
              _formatWorkTime(workerData.spotWork?.overtimeWork ?? ""),
          overtime: workerData.spotWork?.overtime ?? 0.0,
        );
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.workDays,
          typeName: "点工",
          detail: detail,
          feeMoney: workerData.spotWork?.feeMoney,
          isRecordWorkType: true,
        ));
      }

      // 2. 包工
      if (workerData.contractorWork != null &&
          workerData.contractorWorkNum > 0) {
        final detail = _buildWorkDetailString(
            workTime:
                _formatWorkTime(workerData.contractorWork?.workTime ?? ""),
            workTimeHour: workerData.contractorWork?.workTimeHour ?? 0.0,
            overtimeWork: _formatWorkTime(
                workerData.contractorWork?.overtimeWork.toString() ?? ""),
            overtime: workerData.contractorWork?.overtime ?? 0.0);
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.packageWork,
          typeName: "包工",
          detail: detail,
          feeMoney: workerData.contractorWork?.feeMoney,
          isRecordWorkType: true,
        ));
      }

      // 3. 工量
      if (workerData.unitCount.isNotEmpty) {
        for (var unit in workerData.unitCount) {
          statisticsItemList.add(StatisticsItemUIState(
            recordType: RwaRecordType.workLoad,
            typeName: "工量",
            unitWorkTypeName: unit.unitWorkTypeName ?? "",
            detail: "",
            workNum: unit.num > 0 ? "${unit.num.toInt()}笔" : "",
            feeMoney: unit.unitMoney ?? "",
            total: (int.parse(unit.count) > 0)
                ? "总计:${unit.count}${unit.unitWorkTypeUnit}"
                : "",
            isRecordWorkType: true,
          ));
        }
      }

      // 4. 短工
      if (workerData.money != null && workerData.moneyNum > 0) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.dailyWages,
          typeName: "短工",
          feeMoney: workerData.money?.money,
          isRecordWorkType: true,
        ));
      }

      // 5. 借支
      if (workerData.borrowMoneyNum > 0) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.debt,
          typeName: "借支",
          detail: "",
          feeMoney: workerData.borrowMoney,
          isRecordWorkType: false,
        ));
      }

      // 6. 未结
      if (workerData.spotWorkNoFeeNum > 0) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.wageLast,
          typeName: "未结",
          detail: "",
          feeMoney: workerData.spotWorkNoFeeMoney,
          isRecordWorkType: false,
        ));
      }

      // 7. 结算
      if (workerData.wageNum > 0) {
        statisticsItemList.add(StatisticsItemUIState(
          recordType: RwaRecordType.wageLast,
          typeName: "结算",
          detail: "",
          feeMoney: workerData.wage,
          isRecordWorkType: false,
        ));
      }

      workerStatisticsList.add(GroupWorkerStatisticsUIState(
        workerName: workerData.name,
        workerId: workerData.workerId,
        statisticsItemList: statisticsItemList,
      ));
    }

    return workerStatisticsList;
  }

  /// 构建点工和包工详情
  String _buildWorkDetailString({
    required String workTime,
    required double workTimeHour,
    required String overtimeWork,
    required double overtime,
  }) {
    final List<String> workParts = [];
    if (double.tryParse(workTime) != null && double.parse(workTime) > 0) {
      workParts.add("$workTime个工");
    }
    if (workTimeHour > 0) {
      workParts.add("${workTimeHour.toInt()}小时");
    }
    final workDetail = workParts.join('+');

    final List<String> overtimeParts = [];
    if (double.tryParse(overtimeWork) != null &&
        double.parse(overtimeWork) > 0) {
      overtimeParts.add("$overtimeWork个工");
    }
    if (overtime > 0) {
      overtimeParts.add("${overtime.toInt()}小时");
    }
    final overtimeDetail = overtimeParts.join('+');

    final List<String> finalParts = [];
    if (workDetail.isNotEmpty) {
      finalParts.add("上班:$workDetail");
    }
    if (overtimeDetail.isNotEmpty) {
      finalParts.add("加班:$overtimeDetail");
    }

    return finalParts.join('\n');
  }

  // 格式化工时
  String _formatWorkTime(String workTime) {
    if (workTime.isEmpty) return "";

    final numValue = num.tryParse(workTime);
    if (numValue == null) return workTime; // return original if not a number

    if (numValue % 1 == 0) {
      return numValue.toInt().toString(); // integer - no decimal
    } else {
      return numValue.toStringAsFixed(1); // decimal - keep 1 digit
    }
  }
}
