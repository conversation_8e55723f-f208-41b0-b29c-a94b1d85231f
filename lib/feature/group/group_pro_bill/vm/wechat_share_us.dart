import 'package:get/get.dart';

/// 微信分享US
class WechatShareUS {
  /// 是否展示工人电话
  final _isShowWorkerPhone = false.obs;

  /// 工人电话文案
  final _workerPhoneText = ''.obs;

  /// 是否展示工人确认按钮
  final _isShowWorkerConfirmBtn = false.obs;

  /// 工友姓名
  final _workerName = ''.obs;

  /// 是否展示分享工友按钮
  final _isShowShareWorkerBtn = false.obs;

  /// 工友id
  final _workerId = ''.obs;

  /// 是否展示工资
  final _isShowSalary = false.obs;

  /// 是否工人确认
  final _isWorkerConfirm = true.obs;

  /// 项目id
  final _workNote = ''.obs;

  bool get isShowWorkerPhone => _isShowWorkerPhone.value;

  String get workerPhoneText => _workerPhoneText.value;

  bool get isShowWorkerConfirmBtn => _isShowWorkerConfirmBtn.value;

  String get workerName => _workerName.value;

  bool get isShowShareWorkerBtn => _isShowShareWorkerBtn.value;

  String get workerId => _workerId.value;

  bool get isShowSalary => _isShowSalary.value;

  bool get isWorkerConfirm => _isWorkerConfirm.value;

  String get workNote => _workNote.value;

  void setShowWorkerPhone(bool isShowWorkerPhone) {
    _isShowWorkerPhone.value = isShowWorkerPhone;
  }

  void setWorkerPhoneText(String text) {
    _workerPhoneText.value = text;
  }

  void setWorkerConfirmBtn(bool isShowConfirm) {
    _isShowWorkerConfirmBtn.value = isShowConfirm;
  }

  void setWorkerName(String name) {
    _workerName.value = name;
  }

  void setShareWorkerBtn(bool isShowShareWorkerBtn) {
    _isShowShareWorkerBtn.value = isShowShareWorkerBtn;
  }

  void setWorkerId(String id) {
    _workerId.value = id;
  }

  void setShowSalary(bool isShowSalary) {
    _isShowSalary.value = isShowSalary;
  }

  void setWorkerConfirm(bool isWorkerConfirm) {
    _isWorkerConfirm.value = isWorkerConfirm;
  }

  void setWorkNote(String workNote) {
    _workNote.value = workNote;
  }
}
