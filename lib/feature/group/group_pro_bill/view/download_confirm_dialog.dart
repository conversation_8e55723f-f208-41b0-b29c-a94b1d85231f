import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/group_pro_bill_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';

/// 下载确认弹窗
class DownloadConfirmDialog extends StatefulWidget {
  final GroupProBillViewModel vm;

  const DownloadConfirmDialog({
    super.key,
    required this.vm,
  });

  /// 显示弹窗
  static void show(
    BuildContext context, {
    required GroupProBillViewModel vm,
  }) {
    YPRoute.openDialog(
      alignment: Alignment.center,
      clickMaskDismiss: false,
      builder: (context) => DownloadConfirmDialog(
        vm: vm,
      ),
    );
  }

  @override
  State<DownloadConfirmDialog> createState() => _DownloadConfirmDialogState();
}

class _DownloadConfirmDialogState extends State<DownloadConfirmDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _closeDialog() {
    _animationController.reverse().then((_) {
      if (mounted) {
        YPRoute.closeDialog();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: 335.w,
              margin: EdgeInsets.only(top: 120.h), // 添加顶部距离
              decoration: BoxDecoration(
                color: Colors.transparent,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  _buildContent(),
                  SizedBox(height: 56.h),
                  _buildHeader(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建头部（关闭按钮）
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: _closeDialog,
          child: IconFont(
            IconNames.saasClean,
            size: 33.sp,
            color: '#ccc',
          ),
        ),
      ],
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return SizedBox(
      width: 335.w,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 展示工资卡片
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              _closeDialog();
              widget.vm.onDownloadExcelToWechatTap(true);
            },
            child: Image.network(
              'https://cdn.yupaowang.com/jgjz/show-salary.png',
              width: 160.w,
              height: 160.w,
            ),
          ),
          // 不展示工资卡片
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              _closeDialog();
              widget.vm.onDownloadExcelToWechatTap(false);
            },
            child: Image.network(
              'https://cdn.yupaowang.com/jgjz/not-show-salary.png',
              width: 160.w,
              height: 160.w,
            ),
          ),
        ],
      ),
    );
  }
}
