import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/edit_worker_info/entity/edit_worker_info_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/view/image_and_share_pop_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/vm/group_pro_bill_vm.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/debounce_util.dart';

/// 微信对公-前置确认弹窗
class WechatConfirmDialog extends StatefulWidget {
  final String buttonText;
  final GroupProBillViewModel vm;
  final String workNote;

  const WechatConfirmDialog({
    super.key,
    this.buttonText = '前往微信分享',
    required this.vm,
    required this.workNote,
  });

  /// 显示弹窗
  static void show(
    BuildContext context, {
    required GroupProBillViewModel viewModel,
    bool showSalary = true,
    String buttonText = '前往微信分享',
    VoidCallback? onShare,
    VoidCallback? onClose, String workNote = '',
  }) {
    YPRoute.openDialog(
      alignment: Alignment.center,
      clickMaskDismiss: false,
      builder: (context) => WechatConfirmDialog(
        vm: viewModel,
        buttonText: buttonText,
        workNote: workNote,
      ),
    );
  }

  @override
  State<WechatConfirmDialog> createState() => _WechatConfirmDialogState();
}

class _WechatConfirmDialogState extends State<WechatConfirmDialog> {
  bool isShow = false;
  bool isWorkerConfirm = true; // 是否工人确认
  final _debounce = DebounceUtil();

  @override
  void initState() {
    super.initState();
    // 从ViewModel中初始化状态
    isShow = widget.vm.wechatShareUS.isShowSalary;
    isWorkerConfirm = widget.vm.wechatShareUS.isWorkerConfirm;
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: 320.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题和关闭按钮
              _buildHeader(),

              // 工人电话
              if (widget.vm.wechatShareUS.isShowWorkerPhone)
                _buildWorkerPhone(),

              // 内容区域
              _buildContent(),

              // 底部按钮
              _buildFooter(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建头部（标题 + 关闭按钮）
  Widget _buildHeader() {
    return Stack(
      children: [
        // 标题
        Container(
          width: double.infinity,
          padding: EdgeInsets.only(top: 15.h, bottom: 12.h),
          child: Text(
            '分享确认',
            style: TextStyle(
              color: const Color(0xFF323233),
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ),

        // 关闭按钮
        Positioned(
          top: 8.h,
          right: 8.w,
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              YPRoute.closeDialog();
            },
            child: Container(
              padding: EdgeInsets.all(8.w),
              child: IconFont(IconNames.saasClose, size: 20),
            ),
          ),
        ),
      ],
    );
  }

  /// 工人电话栏
  Widget _buildWorkerPhone() {
    return Container(
        padding: EdgeInsets.only(left: 32.w, right: 32.w, bottom: 16.h),
        child: Column(children: [
          // 工人电话
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '工人电话：',
                style: TextStyle(
                  fontSize: 17.sp,
                  color: const Color(0xFF333333),
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  EditWorkerInfoProps params = EditWorkerInfoProps()
                    ..workerName = widget.vm.wechatShareUS.workerName
                    ..workerId = widget.vm.wechatShareUS.workerId
                    ..workerPhone = widget.vm.wechatShareUS.workerPhoneText
                    ..showSalary = widget.vm.wechatShareUS.isShowSalary
                    ..workerConfirm = widget.vm.wechatShareUS.isWorkerConfirm
                    ..workerNote = widget.workNote;
                  YPRoute.closeDialog();
                  YPRoute.openPage(RouteNameCollection.editWorkerInfo,
                          params: params)
                      ?.then((result) {
                    if (result != null && result is EditWorkerInfoProps) {
                      // 更新UI状态
                      widget.vm.wechatShareUS.setWorkerName(result.workerName);
                      widget.vm.wechatShareUS
                          .setWorkerPhoneText(result.workerPhone);
                      widget.vm.wechatShareUS.setWorkerId(result.workerId);
                      widget.vm.wechatShareUS.setShowSalary(result.showSalary);
                      widget.vm.wechatShareUS.setWorkerConfirm(result.workerConfirm);

                      // 延迟一帧后显示弹窗，确保页面完全返回
                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        YPRoute.openDialog(
                          alignment: Alignment.center,
                          clickMaskDismiss: false,
                          builder: (context) => WechatConfirmDialog(
                            vm: widget.vm,
                            buttonText: '前往微信分享',
                            workNote: widget.workNote,
                          ),
                        );
                      });
                    }
                  });
                },
                child: Row(
                  children: [
                    Text(
                      widget.vm.wechatShareUS.workerPhoneText.isNullOrEmpty() ==
                              true
                          ? '未填写工人信息'
                          : widget.vm.wechatShareUS.workerPhoneText,
                      style: TextStyle(
                        fontSize: 17.sp,
                        color: const Color(0xFF8a8a99),
                      ),
                    ),
                    IconFont(
                      IconNames.saasArrowRight,
                      size: 17.h,
                      color: "#8a8a99",
                    ),
                  ],
                ),
              ),
            ],
          )
        ]));
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Container(
      padding: EdgeInsets.only(left: 32.w, right: 16.w, bottom: 16.h),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            width: 1.w,
            color: const Color(0xFFE6E6E6),
          ),
        ),
      ),
      child: Column(
        children: [
          // 是否展示工资
          Row(
            children: [
              Text(
                '是否展示工资：',
                style: TextStyle(
                  fontSize: 17.sp,
                  color: const Color(0xFF333333),
                ),
              ),
              _buildYesNoButtons(isShow, (value) {
                setState(() {
                  isShow = value;
                  widget.vm.wechatShareUS.setShowSalary(isShow);
                });
              }),
            ],
          ),
          SizedBox(
              height:
                  widget.vm.wechatShareUS.isShowWorkerConfirmBtn ? 16.h : 0),
          // 是否工人确认
          Visibility(
            visible: widget.vm.wechatShareUS.isShowWorkerConfirmBtn,
            child: Row(
              children: [
                Text(
                  '是否工人确认：',
                  style: TextStyle(
                    fontSize: 17.sp,
                    color: const Color(0xFF333333),
                  ),
                ),
                _buildYesNoButtons(isWorkerConfirm, (value) {
                  setState(() {
                    isWorkerConfirm = value;
                    widget.vm.wechatShareUS.setWorkerConfirm(isWorkerConfirm);
                  });
                }),
              ],
            ),
          )
        ],
      ),
    );
  }

  /// 构建是否按钮组（带滑动动画）
  Widget _buildYesNoButtons(bool currentValue, Function(bool) onChanged) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: 136.w,
      height: 40.h,
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        border: Border.all(
          width: 1.w,
          color: Colors.black.withOpacity(0.4),
        ),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Stack(
        children: [
          // 滑动的背景
          AnimatedPositioned(
            duration: const Duration(milliseconds: 200),
            curve: Curves.easeInOut,
            left: currentValue ? 0 : 64.w,
            top: 0,
            child: Container(
              width: 64.w,
              height: 32.h,
              decoration: BoxDecoration(
                color: ColorsUtil.primaryColor,
                borderRadius: BorderRadius.circular(4.r),
              ),
            ),
          ),

          // 按钮文字层
          Row(
            children: [
              // "是" 按钮
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  onChanged(true);
                },
                child: Container(
                  width: 64.w, // 128 / 2 = 64
                  height: 32.h, // 64 / 2 = 32
                  alignment: Alignment.center,
                  child: Text(
                    '是',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: currentValue
                          ? Colors.white
                          : Colors.black.withOpacity(0.4),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

              // "否" 按钮
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  onChanged(false);
                },
                child: Container(
                  width: 64.w,
                  height: 32.h,
                  alignment: Alignment.center,
                  child: Text(
                    '否',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: !currentValue
                          ? Colors.white
                          : Colors.black.withOpacity(0.4),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建底部按钮
  Widget _buildFooter() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        _debounce.run(context, () async {
          // 参数常量定义
          const String shareChannel = "1"; // 班组流水统计页
          const String shareType = "2"; // 图片
          const String identity = "1"; // 班组长

          await widget.vm.getGenerateShareImageParam(
              isShowSalary: isShow,
              shareChannel: shareChannel,
              shareType: shareType,
              identity: identity);
          YPRoute.closeDialog();
          if (widget.vm.imageAndShareUS.isShowPopView == true) {
            if (mounted && !YPRoute.isDialogShowing()) {
              ImageAndSharePopView.show(
                context,
                viewModel: widget.vm,
                isShowSalary: isShow,
              );
            }
          }
        });
      },
      child: Container(
        height: 53.h,
        width: double.infinity,
        alignment: Alignment.center,
        child: Text(
          widget.buttonText,
          style: TextStyle(
            fontSize: 16.sp,
            color: ColorsUtil.primaryColor,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
