import "package:flutter/material.dart";
import "package:gdjg_pure_flutter/data/group_data/group_calendar/ds/model/param/group_calendar_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_calendar/repo/group_repo.dart";
import "package:gdjg_pure_flutter/data/group_data/group_calendar/repo/model/group_calendar_biz_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_guide_lds.dart";
import "package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart";
import "package:gdjg_pure_flutter/data/worker_data/corp_select/repo/corp_select_repo.dart";
import "package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart";
import "package:gdjg_pure_flutter/feature/group/common_uistate/statistice_helper.dart";
import "package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_liquidated/entity/group_liquidated_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_liquidated_detail/entity/group_liquidated_detail_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_calendar/entity/group_pro_calendar_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_calendar/vm/group_pro_calendar_us.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_statistics/entity/group_pro_statistics_props.dart";
import "package:gdjg_pure_flutter/init_module/init_route.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:gdjg_pure_flutter/utils/system_util/date_util.dart";
import "package:gdjg_pure_flutter/utils/ui_util/calendar_uistate_helper.dart";
import "package:pull_to_refresh/pull_to_refresh.dart";

/// @date 2025/06/19
/// @description GroupProCalendar页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupProCalendarViewModel {
  final RefreshController refreshController =
      RefreshController(initialRefresh: false);
  final _groupRepo = GroupRepo();
  final _corpSelectRepo = CorpSelectRepo();
  final _workerProjectRepo = WorkerProjectRepo();
  var us = GroupProCalendarUS();
  var _dateTime = DateTime.now();
  GroupProCalendarProps? _props;

  // 部门详情数据
  String _workNoteId = '';
  String _workNoteName = '';
  double _isSelfCreated = 0;
  double _isAgent = 0;
  double _corpId = 0;

  String get title => _workNoteName;

  bool get isSelfCreatedOrAgent => _isSelfCreated == 1 || _isAgent == 1;

  double? get deptId => _props?.deptId;

  String get workNoteId => _workNoteId;

  final GroupGuideLds _groupGuideLds = GroupGuideLds();

  init(GroupProCalendarProps? props) async {
    _props = props;
    if (_props?.deptId != null) {
      await _fetchDeptDetail(_props!.deptId!.toString());
      await _switchCorp(); // 切换企业
      _fetchData(_dateTime); // 获取日历数据
    }
  }

  void onRefresh() {
    debugPrint('GroupProCalendarViewModel: onRefresh called');
    _fetchData(_dateTime);
  }

  void updateDateRange(DateTime dateTime) {
    _dateTime = dateTime;
    us.setCurrentDate(dateTime);
    _fetchData(dateTime); // 请求新数据
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void _fetchData(DateTime dateTime) async {
    // 确保workNoteId已经获取到才进行数据请求
    if (_workNoteId.isEmpty) {
      return;
    }

    try {
      debugPrint('GroupProCalendarViewModel: _fetchData started');
      GroupCalendarParamModel params = GroupCalendarParamModel();
      params.work_note = _workNoteId;
      params.start_time = DateUtil.formatStartDate(dateTime);
      params.end_time = DateUtil.formatEndDate(dateTime);
      var result = await _groupRepo.getGroupCalendar(params);
      if (result.isOK()) {
        debugPrint('GroupProCalendarViewModel: data fetch successful');
        _convertEntityToUIState(result.getSucData());
        // 将日历实体数据转换为UIState
        var events = CalendarUIStateHelper.convertEntityToCalendarUIState(
            result.getSucData()?.calendar ?? []);
        us.setEvents(events);
        refreshController.refreshCompleted();
        debugPrint('GroupProCalendarViewModel: refreshCompleted called');
      } else {
        debugPrint('GroupProCalendarViewModel: data fetch failed');
        refreshController.refreshFailed();
      }
    } catch (e) {
      debugPrint('GroupProCalendarViewModel: exception occurred: $e');
      refreshController.refreshFailed();
    }
  }

  /// 将实体数据转换为UIState
  /// @param entity
  /// @returns
  void _convertEntityToUIState(GroupCalendarBizModel? data) {
    List<StatisticsItemUIState> list =
        StatisticsUIStateHelper.buildStatisticsItem(data?.count);
    us.setStatisticsList(list);
  }

  /// 获取部门详情
  Future<void> _fetchDeptDetail(String deptId) async {
    final result = await _workerProjectRepo.getDeptDetail(deptId);
    if (result.isOK()) {
      final deptData = result.getSucData();
      if (deptData != null) {
        _workNoteId = deptData.workNoteId.toString();
        _workNoteName = deptData.name;
        _isSelfCreated = deptData.isSelfCreated;
        _isAgent = deptData.isAgent;
        _corpId = deptData.corpId;
      }
    }
  }

  ///跳转统计页面
  void onJumpToProStatisticsTap({int? index}) {
    DateTime endTime =
        DateTime(_dateTime.year, _dateTime.month + 1, 0); // 获取该月最后一天
    if (index == null) {
      GroupProStatisticsProps params = GroupProStatisticsProps()
        ..workNoteId = _workNoteId
        ..workNoteName = _workNoteName
        ..endTime = endTime;
      YPRoute.openPage(RouteNameCollection.groupProStatistics, params: params);
      return;
    }

    var uiState = us.statisticsList[index];
    List<RwaRecordType> businessType = [];
    if (uiState.recordType != RwaRecordType.wageLast) {
      businessType.add(uiState.recordType);
    }
    // 获取该月最后一天
    DateTime startTime = DateTime(_dateTime.year, _dateTime.month, 1);
    if (isSelfCreatedOrAgent) {
      GroupProStatisticsProps params = GroupProStatisticsProps()
        ..workNoteId = _workNoteId
        ..workNoteName = _workNoteName
        ..deptId = _props?.deptId
        ..startTime = startTime
        ..endTime = endTime
        ..businessType = businessType;
      YPRoute.openPage(RouteNameCollection.groupProStatistics, params: params);
    } else {
      ///跳转流水页面
    }
  }

  ///未结统计
  void onGroupUnLiquidatedTap({int? index}) {
    if (isSelfCreatedOrAgent) {
      YPRoute.openPage(RouteNameCollection.liquidated,
          params: GroupLiquidatedProps(
            workNote: _workNoteId,
            workNoteName: _workNoteName,
            deptId: _props?.deptId ?? 0,
            isAgent: _isAgent,
            isJoin: _isSelfCreated != 1,
          ));
    } else {
      YPRoute.openPage(RouteNameCollection.liquidatedDetail,
          params: GroupLiquidatedDetailProps(
            workNoteName: _workNoteName,
            workNote: _workNoteId,
            deptId: _props?.deptId,
            isAgent: _isAgent,
            isJoin: _isSelfCreated != 1,
            isUnLiquidated: false,
          ));
    }
  }

  /// 切换企业
  Future<void> _switchCorp() async {
    final params = CorpSelectParamModel(
      corp_id: _corpId.toString(),
    );
    await _corpSelectRepo.fetchCorpSelect(params);
  }

  Future<bool> checkGuideStep1() async =>
      !await _groupGuideLds.fetchGuided(GroupGuideLds.maskStep1);

  Future<bool> completeGuideStep1() async =>
      await _groupGuideLds.updateGuided(GroupGuideLds.maskStep1, true);

  /// 释放资源
  void dispose() {
    refreshController.dispose();
  }
}
