import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/group/group_event_bus/group_edit_wage_event_bus_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/entity/group_pro_calendar_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_pro_bench_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_pro_calendar_bottom_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/view/group_statistics_list_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/vm/group_pro_calendar_viewmodel.dart';
import 'package:gdjg_pure_flutter/feature/group/guide/group_guide_provider.dart';
import 'package:gdjg_pure_flutter/utils/event_bus_util/event_bus_util.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/appbar_util.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_week.dart';
import 'package:gdjg_pure_flutter/widget/calendar/dynamic_height_calendar.dart';
import 'package:gdjg_pure_flutter/widget/month_switcher_view.dart';
import 'package:gdjg_pure_flutter/widget/network_carousel_widget.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../../../init_module/init_route.dart';
import '../../../utils/ui_util/widget_util.dart';

import '../group_record_work/entity/group_record_work_props.dart';

/// @date 2025/06/19
/// @param props 页面路由参数
/// @returns
/// @description GroupProCalendar页面入口 班组项目日历页
class GroupProCalendarPage extends BaseFulPage {
  const GroupProCalendarPage({super.key}) : super(appBar: null, canBack: false);

  @override
  createState() => _GroupProCalendarPageState();
}

class _GroupProCalendarPageState<GroupProCalendarPage>
    extends BaseFulPageState {
  final GlobalKey<DynamicHeightCalendarState> _calendarKey =
      GlobalKey<DynamicHeightCalendarState>();
  final GroupProCalendarViewModel _viewModel = GroupProCalendarViewModel();

  StreamSubscription? _stream;

  TutorialCoachMark? _tutorialCoachMarkStep1;

  final GlobalKey _weekKey = GlobalKey();
  final GlobalKey _calendarRenderBoxKey = GlobalKey();

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    GroupProCalendarProps? props = routeParams as GroupProCalendarProps?;
    _viewModel.init(props);
  }

  @override
  void onPageCreate() {
    super.onPageCreate();
    _stream = EventBusUtil.collect<GroupEditWageEventBusModel>((data) {
      _viewModel.onRefresh();
    });
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    _stream?.cancel();
    _viewModel.dispose();
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) => _checkGuideStep1());
  }

  @override
  bool? callbackIntercept() => _tutorialCoachMarkStep1?.isShowing != true;

  @override
  Widget yBuild(BuildContext context) {
    return Scaffold(
      appBar: AppBarUtil.buildWithResourceWidget(
          title: _viewModel.title,
          resourceText: '拍证据工资有保障',
          resourceIcon: "assets/images/common/ic_take_phone_small.webp",
          onBackTap: () => YPRoute.closePage()),
      body: contentView(),
    );
  }

  Future<void> _checkGuideStep1() async {
    if (await _viewModel.checkGuideStep1()) {
      _showTutorialStep1();
    }
  }

  void _refreshGuideStep1() {
    if (_tutorialCoachMarkStep1?.isShowing == true) {
      WidgetsBinding.instance.addPostFrameCallback((_) => _showTutorialStep1());
    }
  }

  void _completeGuideStep1() {
    _viewModel.completeGuideStep1();
  }

  void _showTutorialStep1() {
    final Rect? rect =
        WidgetUtils.calculateBounding([_weekKey, _calendarRenderBoxKey]);
    if (rect == null) {
      return;
    }
    _tutorialCoachMarkStep1?.finish();
    _tutorialCoachMarkStep1 = GroupGuideProvider.showStep1(
      context,
      rect,
      () {
        _tutorialCoachMarkStep1?.finish();
        YPRoute.openPage(
          RouteNameCollection.groupRecordWork,
          params: GroupRecordWorkProps(
            from: GroupRecordWorkFrom.projectList,
            deptId: _viewModel.deptId?.toInt() ?? 0,
          ),
        );
      },
    );
    _completeGuideStep1();
  }

  /// 实际展示的视图，需要处理：
  /// 1. 画出UI
  /// 2. 绑定UIState
  /// 3. 处理事件监听
  /// 4. 向VM传递来自View层的事件
  Widget contentView() {
    // 进行事件处理
    // handleGroupProCalendarVMEvent(props.vm)
    return Container(
      color: Colors.white,
      height: double.infinity,
      child: Column(
        children: [
          Expanded(
            child: SmartRefresher(
              controller: _viewModel.refreshController,
              onRefresh: _viewModel.onRefresh,
              enablePullDown: true,
              enablePullUp: false,
              child: CustomScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                slivers: [
                  SliverPersistentHeader(
                    pinned: true, // 固定在顶部
                    delegate: _StickyHeaderDelegate(
                      child: Container(
                        color: Colors.white,
                        child: Column(
                          children: [
                            _buildBannerView(),
                            Divider(
                                height: 6.h,
                                color: Color(0xFFF0F0F0),
                                thickness: 6.h),
                            _buildMonthSwitchView(),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SliverToBoxAdapter(
                    child: _buildStatisticsView(),
                  ),
                  SliverToBoxAdapter(
                    child: _buildWeekView(),
                  ),
                  SliverToBoxAdapter(
                    child: SizedBox(height: 2.h),
                  ),
                  _buildCalenderView(),
                  SliverToBoxAdapter(
                    child: _buildProBenchView(),
                  ),
                ],
              ),
            ),
          ),
          _buildBottomView(),
        ],
      ),
    );
  }

  ///统计布局
  Widget _buildStatisticsView() {
    return Obx(() {
      _refreshGuideStep1();
      if (_viewModel.us.isStatisticsEmpty) {
        return SizedBox(
          width: double.infinity,
          height: 56.h,
          child: Center(
            child: Text(
              "当月无记工",
              style: TextStyle(color: Color(0xFF323232), fontSize: 16.sp),
            ),
          ),
        );
      }
      return GroupStatisticsView(
        items: _viewModel.us.statisticsList,
        initialVisibleCount: 1,
        onItemTap: (index) {
          _viewModel.onJumpToProStatisticsTap(index: index);
        },
      );
    });
  }

  ///顶部banner
  Widget _buildBannerView() {
    return Container(
      color: Color(0xFFF0F0F0),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 6.h),
      child: NetworkCarouselWidget(
        height: 48.h,
        code: 'JGJZ_HOME_GROUP_BANER',
      ),
    );
  }

  ///月份切换控件
  Widget _buildMonthSwitchView() {
    return Obx(
      () => MonthSwitcherView(
        key: Key("MonthSwitcherView"),
        initialTime: _viewModel.us.dataTime,
        onYearMonthSelected: (selectTime) {
          _viewModel.updateDateRange(selectTime);
        },
      ),
    );
  }

  Widget _buildWeekView() {
    return Container(
        key: _weekKey,
        color: Color(0xFFF5F6FA),
        height: 28.h,
        padding: EdgeInsets.symmetric(horizontal: 10.w),
        child: CalendarWeek());
  }

  ///大日历
  Widget _buildCalenderView() {
    return Obx(
      () => DynamicHeightCalendar(
        renderBoxKey: _calendarRenderBoxKey,
        key: ValueKey(
            'calendar_${_viewModel.us.dataTime.year}_${_viewModel.us.dataTime.month}'),
        onValueChange: (date) {
          _viewModel.updateDateRange(date);
        },
        events: _viewModel.us.events,
        onDayTap: (v) {},
      ),
    );
  }

  ///底部静态功能区域
  Widget _buildProBenchView() {
    return GroupProBenchView(viewModel: _viewModel);
  }

  ///底部布局
  Widget _buildBottomView() {
    return GroupProCalendarBottomView(
      onLeftCallback: () {},
      onRightCallback: () {},
      isCreateByMySelfOrAgent: _viewModel.isSelfCreatedOrAgent,
    );
  }
}

/// 固定头部的委托类
class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _StickyHeaderDelegate({required this.child});

  @override
  double get minExtent {
    return 48.h + 12.h + 6.h + 40.h; // 根据实际组件高度调整
  }

  @override
  double get maxExtent {
    // 最大高度与最小高度相同，保持固定高度
    return minExtent;
  }

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return oldDelegate != this;
  }
}
