import "package:gdjg_pure_flutter/data/common_data/biz/count_biz_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_statistics/ds/model/param/group_project_get_group_worker_count_param_model.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_statistics/repo/group_pro_repo.dart";
import "package:gdjg_pure_flutter/data/group_data/group_pro_statistics/repo/model/group_project_get_group_worker_count_biz_model.dart";
import "package:gdjg_pure_flutter/data/note_time_data/note_time_repo.dart";
import "package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart";
import "package:gdjg_pure_flutter/feature/group/common_uistate/statistice_helper.dart";
import "package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_com_uistate/group_worker_statistics_ui_state.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_bill/entity/group_pro_bill_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_statistics/entity/group_pro_statistics_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_pro_statistics/vm/group_pro_statistics_us.dart";
import "package:gdjg_pure_flutter/feature/group/worker_manager/worker_selector/vm/protocol/worker_selector_ui_state.dart";
import "package:gdjg_pure_flutter/init_module/init_route.dart";
import "package:gdjg_pure_flutter/model/RecordType.dart";
import "package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart";
import "package:gdjg_pure_flutter/utils/system_util/date_util.dart";
import "package:gdjg_pure_flutter/utils/system_util/string_util.dart";
import "package:pull_to_refresh/pull_to_refresh.dart";

/// @date 2025/06/21
/// @description GroupFlowDetail页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupProStatisticsViewModel {
  final _groupProRepo = GroupProRepo();
  final _noteTimeRepo = NoteTimeRepo();
  final RefreshController workerRefreshController = RefreshController(initialRefresh: false);
  final RefreshController totalRefreshController = RefreshController(initialRefresh: false);
  var us = GroupProStatisticsUS();
  List<RwaRecordType> _businessType = [];
  DateTime? _netStartTime;
  DateTime? _startTime;
  DateTime _endTime = DateTime.now();

  GroupProStatisticsProps? _props;

  String get title => _props?.workNoteName ?? '';

  init(GroupProStatisticsProps? props) {
    _props = props;
    var businessType = props?.businessType ?? [];
    _businessType = businessType;
    _startTime = props?.startTime;
    _endTime = props?.endTime ?? _endTime;
    us.setStartTime(_startTime);
    us.setEndTime(_endTime);
    us.setRwaRecordTypeList(businessType);
    fetchData();
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchData() async {
    try {
      if (_startTime == null) {
        await fetchFirstTime();
      }
      var businessType =
          _businessType.map((type) => type.code.value.toString()).join(',');

      // 处理选中的工友ID
      final workerIds = us.selectedWorkers
          .where((worker) => worker.id?.isNotEmpty == true)
          .map((worker) => worker.id!.trimTrailingZeros())
          .join(',');

      var params = GroupProjectGetGroupWorkerCountParamModel()
        ..work_note = _props?.workNoteId ?? ''
        ..start_time = DateUtil.formatStartDate(_startTime)
        ..end_time = DateUtil.formatEndDate(_endTime)
        ..business_type = businessType
        ..worker_ids = workerIds;
      var result = await _groupProRepo.getGroupWorkerCount(params);
      if (result.isOK()) {
        convertEntityToUIState(result.getSucData());
      }
      // 这里的result仅做弹窗之类的处理，不在此处转为UI实体
      workerRefreshController.refreshCompleted();
      totalRefreshController.refreshCompleted();
    } catch (e) {
      workerRefreshController.refreshFailed();
      totalRefreshController.refreshFailed();
    }
  }

  ///获取开始时间和结束时间
  Future<void> fetchFirstTime() async {
    DateTime endTime = DateTime.now();
    var resultTime =
        await _noteTimeRepo.getNoteFirstTime(_props?.workNoteId ?? "");
    if (resultTime.isOK()) {
      var time = resultTime.getSucData()?.date ?? "2020-01-01";
      var startTime = DateTime.parse(time);
      _startTime = startTime;
      _netStartTime = startTime;
      _endTime = endTime;
      us.setStartTime(startTime);
      us.setEndTime(endTime);
    }
  }

  /// 将实体数据转换为UIState
  void convertEntityToUIState(GroupProjectGetGroupWorkerCountBizModel? data) {
    if (data == null) return;
    _convertWorkerStatistics(data.worker);
    _convertTotalStatistics(data.all);
  }

  ///将实体数据转换为工友统计列表UIState
  void _convertWorkerStatistics(List<CountBizModel>? workers) {
    List<GroupWorkerStatisticsUIState> workerStatisticsList = [];
    workers?.forEach((element) {
      workerStatisticsList.add(GroupWorkerStatisticsUIState(
          workerName: element.name,
          workerId: element.id,
          statisticsItemList: StatisticsUIStateHelper.buildStatisticsItem(
              element,
              isShowUnsettle: false,
              isShowDailyWages: false)));
    });
    us.setWorkerStatisticsList(workerStatisticsList);
  }

  ///将实体数据转换为总计列表UIState
  void _convertTotalStatistics(CountBizModel? total) {
    List<StatisticsItemUIState> totalStatisticsList = [];
    if (total != null) {
      totalStatisticsList = StatisticsUIStateHelper.buildStatisticsItem(total,
          isShowDailyWages: false);
    }
    us.setTotalStatisticsList(totalStatisticsList);
  }

  void onJumpToProBillTap({GroupWorkerStatisticsUIState? item}) {
    GroupProBillProps params = GroupProBillProps()
      ..workNoteId = _props?.workNoteId ?? ""
      ..workNoteName = _props?.workNoteName ?? ""
      ..startTime = _startTime
      ..endTime = _endTime
      ..deptId = _props?.deptId
      ..workers = item != null
          ? [WorkerModel(workerId: item.workerId, workerName: item.workerName)]
          : [];
    YPRoute.openPage(RouteNameCollection.groupProBill, params: params);
  }

  ///更新开始和结束时间
  void onUpdateDateTimeTap(
      DateTime startTime, DateTime endTime, List<RwaRecordType>? businessType) {
    _startTime = startTime;
    _endTime = endTime;
    _businessType = businessType ?? [];
    fetchData();
  }

  /// 更新选择的工友
  void updateWorkerSelected(List<WorkerContactUIState> selectedWorkers) {
    us.setSelectedWorkers(selectedWorkers);
    fetchData();
  }

  onSelectAllTap() async {
    if (_netStartTime == null) {
      await fetchFirstTime();
    } else {
      DateTime endTime = DateTime.now();
      _startTime = _netStartTime;
      _endTime = endTime;
      us.setStartTime(_netStartTime);
      us.setEndTime(endTime);
    }
    fetchData();
  }
}
