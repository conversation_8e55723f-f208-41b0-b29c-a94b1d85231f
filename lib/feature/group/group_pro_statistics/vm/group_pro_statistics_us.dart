import 'package:gdjg_pure_flutter/feature/group/common_uistate/statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/group_com_uistate/group_worker_statistics_ui_state.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_selector/vm/protocol/worker_selector_ui_state.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:get/get.dart';

class GroupProStatisticsUS {
  final _totalStatisticsList = <StatisticsItemUIState>[].obs;
  final _isTotalStatisticsEmpty = false.obs;

  final _workerStatisticsList = <GroupWorkerStatisticsUIState>[].obs;
  final _isWorkerStatisticsEmpty = false.obs;

  final _startTime = DateTime.now().obs;

  final _endTime = DateTime.now().obs;

  final _rwaRecordTypeList = <RwaRecordType>[].obs;

  final _selectedWorkers = <WorkerContactUIState>[].obs;

  get startTime => _startTime.value;

  get endTime => _endTime.value;

  get selectedTypes => _rwaRecordTypeList.value;

  List<WorkerContactUIState> get selectedWorkers => _selectedWorkers;

  bool get isTotalStatisticsEmpty => _isTotalStatisticsEmpty.value;

  List<StatisticsItemUIState> get totalStatisticsList => _totalStatisticsList;

  bool get isWorkerStatisticsEmpty => _isWorkerStatisticsEmpty.value;

  List<GroupWorkerStatisticsUIState> get workerStatisticsList =>
      _workerStatisticsList;

  setTotalStatisticsList(List<StatisticsItemUIState> list) {
    _totalStatisticsList.value = list;
    _isTotalStatisticsEmpty.value = list.isEmpty;
    _totalStatisticsList.refresh();
    _isTotalStatisticsEmpty.refresh();
  }

  setWorkerStatisticsList(List<GroupWorkerStatisticsUIState> list) {
    _workerStatisticsList.value = list;
    _isWorkerStatisticsEmpty.value = list.isEmpty;
    _workerStatisticsList.refresh();
    _isWorkerStatisticsEmpty.refresh();
  }

  setStartTime(DateTime? date) {
    if (date == null) {
      return;
    }
    _startTime.value = date;
    _startTime.refresh();
  }

  setEndTime(DateTime? date) {
    if (date == null) {
      return;
    }
    _endTime.value = date;
    _endTime.refresh();
  }

  setRwaRecordTypeList(List<RwaRecordType> list) {
    _rwaRecordTypeList.value = list;
    _rwaRecordTypeList.refresh();
  }

  setSelectedWorkers(List<WorkerContactUIState> workers) {
    _selectedWorkers.value = workers;
    _selectedWorkers.refresh();
  }
}
