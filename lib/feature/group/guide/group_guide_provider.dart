import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/guide_utils.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../../../widget/bubble.dart';
import '../../../widget/guide_bubble.dart';

class GroupGuideProvider {
  static TutorialCoachMark showStep1(
    final BuildContext context,
    final Rect rect,
    final void Function() onFinish,
  ) =>
      GuideUtils.createSingleFocusTutorialCoachMark(
        rect,
        onFinish,
        [
          TargetContent(
            align: ContentAlign.custom,
            customPosition: CustomTargetContentPosition(
              top: rect.bottom,
              left: rect.left,
            ),
            padding: EdgeInsets.only(top: 4.h, left: 16.w, right: 32.w),
            builder: (context, controller) => GuideBubble(
              voice: "sounds/waa_guide_calendar.MP3",
              step: "1/5",
              content: "点击日历上的日期，开始为工友记工",
              trianglePosition: TrianglePosition.topRight,
              triangleOffset: -32.w,
              nextAction: onFinish,
            ),
          ),
        ],
      )..show(context: context);

  static TutorialCoachMark showStep2(
    final BuildContext context,
    final Rect rect,
    final void Function() onFinish,
  ) =>
      GuideUtils.createSingleFocusTutorialCoachMark(
        rect,
        onFinish,
        [
          TargetContent(
            align: ContentAlign.custom,
            customPosition: CustomTargetContentPosition(
              top: rect.bottom,
              left: rect.left,
            ),
            padding: EdgeInsets.only(top: 4.h, left: 16.w, right: 32.w),
            builder: (context, controller) => GuideBubble(
              voice: "sounds/waa_guide_select_type.MP3",
              step: "2/5",
              content: "选择合适的记工方式",
              trianglePosition: TrianglePosition.topLeft,
              triangleOffset: 32.w,
              nextAction: onFinish,
            ),
          ),
        ],
      )..show(context: context);

  static TutorialCoachMark showStep3(
    final BuildContext context,
    final Rect rect,
    final void Function() onFinish,
  ) =>
      GuideUtils.createSingleFocusTutorialCoachMark(
        rect,
        onFinish,
        [
          TargetContent(
            align: ContentAlign.custom,
            customPosition: CustomTargetContentPosition(
              top: rect.bottom,
              left: rect.left,
            ),
            padding: EdgeInsets.only(top: 4.h, left: 16.w, right: 32.w),
            builder: (context, controller) => GuideBubble(
              voice: "sounds/waa_guide_add_worker.MP3",
              step: "3/5",
              content: "请添加需要被记工的工友",
              trianglePosition: TrianglePosition.topLeft,
              triangleOffset: 32.w,
              nextAction: onFinish,
            ),
          ),
        ],
      )..show(context: context);

  static TutorialCoachMark showStep4(
    final BuildContext context,
    final Rect rect,
    final void Function() onFinish,
  ) =>
      GuideUtils.createSingleFocusTutorialCoachMark(
        rect,
        onFinish,
        [
          TargetContent(
            align: ContentAlign.custom,
            customPosition: CustomTargetContentPosition(
              top: rect.bottom,
              left: rect.left,
            ),
            padding: EdgeInsets.only(top: 4.h, left: 16.w, right: 32.w),
            builder: (context, controller) => GuideBubble(
              voice: "sounds/waa_guide_add_way.MP3",
              step: "4/5",
              content: "请选择以上两个方法添加工友",
              trianglePosition: TrianglePosition.topLeft,
              triangleOffset: 32.w,
              nextAction: onFinish,
            ),
          ),
        ],
      )..show(context: context);

  static TutorialCoachMark showStep5(
    final BuildContext context,
    final Rect rect,
    final void Function() onFinish,
  ) =>
      GuideUtils.createSingleFocusTutorialCoachMark(
        rect,
        onFinish,
        [
          TargetContent(
            align: ContentAlign.top,
            padding: EdgeInsets.only(bottom: 0.h, left: 32.w, right: 32.w),
            builder: (context, controller) => GuideBubble(
              voice: "sounds/waa_guide_select_record_time.MP3",
              step: "5/5",
              next: "开始记工",
              content: "选择工作时长，默认选择一个工",
              trianglePosition: TrianglePosition.bottomCenter,
              triangleOffset: 0.w,
              nextAction: onFinish,
            ),
          ),
        ],
      )..show(context: context);
}
