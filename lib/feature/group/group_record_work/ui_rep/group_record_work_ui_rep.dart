import 'package:gdjg_pure_flutter/data/group_data/group_record/rds/model/param/business_add_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_record/repo/group_record_worker_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/ds/param/corp_select_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/repo/corp_select_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/corp_select/repo/model/corp_select_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/param/attendance_worker_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/ds/param/net_model_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/net_model_dept_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/worker_info_repo.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_detail_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/wage_source_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/worker_project_repo.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// @date 2025/07/29
/// @description GroupRecordWork页UI仓库
/// 1.为vm提供业务数据
/// 2.解耦视图层与业务仓库之间的耦合
class GroupRecordWorkUIRep {
  var workerRep = WorkerInfoRepo();

  var workerProjectRepo = WorkerProjectRepo();

  var corpRepo = CorpSelectRepo();

  /// 班组记工仓库
  var recordRepo = GroupRecordWorkRepo();

  /// 获取出勤工友列表
  Future<WorkersGetWorkersAtWorkBizModel?> getWorkerAtWork(
      List<String> dates, String noteId) async {
    // 调用网络的方法获取数据
    var params = WorkersGetWorkersAtWorkParamModel(
      businessTime: dates.join(','),
      workNote: noteId,
    );
    var result = await workerRep.fetchWorkersAtWork(params);
    if (result.isOK()) {
      return result.getSucData();
    }
    return null;
  }

  /// 获取项目下退场工友列表
  Future<List<NoteWorkerBizModel>> getGroupExitWorker(String deptId) async {
    return getGroupWorkers(deptId, true);
  }

  /// 获取项目下休假的工友列表
  Future<List<NoteWorkerBizModel>> getGroupRestWorker(String deptId) async {
    return getGroupWorkers(deptId, false);
  }

  /// 获取项目下工友列表
  /// isExit: true 退场工友，false 休假工友
  Future<List<NoteWorkerBizModel>> getGroupWorkers(
      String deptId, bool isExit) async {
    // 调用网络的方法获取数据
    final params = NetModelDeptParamModel(
      dept_id: deptId,
      is_deleted: isExit ? '1' : '0',
      is_rest: !isExit ? '1' : '0',
    );
    var result = await workerRep.fetchGroupWorkers(params);
    if (result.isOK()) {
      return result.getSucData() ?? [];
    }
    return [];
  }

  /// 获取单个班组详情
  Future<DeptDetailBizModel?> getDeptDetail(int deptId) async {
    // 调用网络的方法获取数据
    var result = await workerProjectRepo.getDeptDetail(deptId.toString());
    if (result.isOK()) {
      return result.getSucData();
    }
    return null;
  }

  /// 企业切换
  Future<CorpSelectBizModel?> getCorpSelect(String corpId) async {
    // 调用网络的方法获取数据
    var result =
        await corpRepo.fetchCorpSelect(CorpSelectParamModel(corp_id: corpId));
    if (result.isOK()) {
      return result.getSucData();
    }
    // 返回成功的
    return null;
  }

  /// 获取用户是否记过工
  Future<bool> hasBusiness() async {
    var result = await workerRep.fetchHasBusiness();
    // 返回成功的
    return result.getSucData() == true;
  }

  /// 添加记工
  Future<bool> addRecordWork(BusinessAddParamModel params) async {
    var result = await recordRepo.addRecordWork(params);
    if (result.isOK()) {
      ToastUtil.showToast("记工成功！");
      return true;
    }
    return false;
  }

  /// 获取工资来源
  /// @param workNoteId 账本id
  Future<WageSourceBizModel?> getWageSource(String workNoteId) async {
    var result = await workerProjectRepo.getWageSource(workNoteId);
    if (result.isOK()) {
      return result.getSucData();
    }
    return null;
  }
}
