import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/worker_edit_list_view.dart';

class WorkerAddSelectProps {
  /// 工友集
  final List<WorkerBizModel> workers;

  /// 已经记工过的工友id集
  final List<double>? recordedIds;

  /// 未记工的工友id集
  final List<double>? noRecordedIds;

  /// 选中工友回调
  final OnWorkerSelected onSelected;

  WorkerAddSelectProps(
      {required this.workers,
      this.noRecordedIds,
      this.recordedIds,
      required this.onSelected});

  @override
  String toString() {
    return "WorkerAddSelectProps{recordedIds: $recordedIds, noRecordedIds: $noRecordedIds}";
  }
}
