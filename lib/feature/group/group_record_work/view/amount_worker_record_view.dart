import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/repo/model/group_business_get_group_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_record/rds/model/param/business_add_param_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/entity/record_account_list_pops.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_account_view/record_account_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/record_and_account_title.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/entity/worker_add_select_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/worker_add_select_view/worker_edit_list_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/point_work/point_work_controller.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_controller.dart';
import 'package:gdjg_pure_flutter/widget/work_load_view/work_load_view.dart';

/// 工量记工子页面
class AmountWorkerRecordView extends StatefulWidget {
  final WorkerRecordProps props;

  const AmountWorkerRecordView({super.key, required this.props});

  @override
  State<AmountWorkerRecordView> createState() => _AmountWorkerRecordViewState();
}

class _AmountWorkerRecordViewState extends State<AmountWorkerRecordView>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  late TabController _tabController;

  late WorkLoadController _workLoadController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _workLoadController = WorkLoadController();

    // 恢复之前的Tab状态
    _restoreTabState();

    // 添加TabController监听器
    _tabController.addListener(_onTabChanged);
  }

  /// 恢复Tab状态
  void _restoreTabState() {
    // 从props中获取初始Tab状态
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 延迟执行，确保TabController已经完全初始化
      final initialTab = widget.props.initialSubTab ?? RecordWorkTabType.record;
      final tabIndex = initialTab == RecordWorkTabType.record ? 0 : 1;
      _tabController.animateTo(tabIndex);
    });
  }

  /// Tab切换监听方法
  void _onTabChanged() {
    if (!_tabController.indexIsChanging) {
      // 只在切换完成时触发，避免切换过程中的重复调用
      final tabIndex = _tabController.index == 0
          ? RecordWorkTabType.record
          : RecordWorkTabType.account;
      print('当前Tab索引: ${_tabController.index}');
      widget.props.statusCallBack
          ?.call(BusinessType.workAndAccountTypeDays, tabIndex);
      if (tabIndex == RecordWorkTabType.record) {
        widget.props.dataCallBack?.call(
            BusinessType.workAndAccountTypeDays,
            BusinessAddParamModel(
              worker_id: '123,235',
            ));
      }
    }
  }

  @override
  void dispose() {
    // 移除监听器，避免内存泄漏
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true; // 保持页面状态

  @override
  Widget build(BuildContext context) {
    return Container(
      color: ColorsUtil.ypBgColor,
      child: NestedScrollView(
        headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
          return <Widget>[
            SliverToBoxAdapter(
              child: Column(
                children: [
                  Text('点工记工子页面'),
                  WorkerAddSelectView(
                      props: WorkerAddSelectProps(
                    workers: widget.props.workers,
                    recordedIds: widget.props.recordedIds,
                    noRecordedIds: widget.props.noRecordedIds,
                    onSelected: onSelected,
                  )),
                  const SizedBox(
                    height: 10,
                  ),
                  _buildTitleBar(),
                ],
              ),
            ),
          ];
        },
        body: _buildRecordAndAccounts(),
      ),
    );
  }

  void onSelected(List<double> workerIds) {
    yprint('点工选择记工的工友----$workerIds');
  }

  /// 构建记工-记工流水 titleBar
  Widget _buildTitleBar() {
    return RecordAndAccountsView(tabController: _tabController);
  }

  /// 记工和记工流水视图
  Widget _buildRecordAndAccounts() {
    return Container(
      color: ColorsUtil.ypBgColor,
      child: TabBarView(
        controller: _tabController,
        physics: const NeverScrollableScrollPhysics(), // 禁止左右滑动切换
        children: [
          _buildRecordView(),
          _buildAccountView(),
        ],
      ),
    );
  }

  Widget _buildRecordView() {
    return SingleChildScrollView(
      child: WorkLoadView(
        controller: _workLoadController,
        noteId: widget.props.deptDetailUS.workNoteId,
      ),
    );
  }

  Widget _buildAccountView() {
    final mockData = GroupBusinessGetGroupBusinessListBizModel(list: [
      GroupBusinessGetGroupBusinessListABizModel(date: '2023-07-01', list: [
        GroupBusinessGetGroupBusinessListBBizModel(
          id: 1,
          note: '婆婆给丁敏楼哦破婆婆坡起外婆给定于，哦破婆婆想去在亲我一个哥哥婆媳上午甜蜜蜜女',
          hasVideo: 1,
          hasImg: 1,
          businessType: 1,
          money: '500',
          feeMoney: 0,
          feeStandardId: 0,
          workNote: 1,
          workerId: 1,
          workerName: '阿西吧1',
          unitNum: '10',
          unitWorkTypeName: '分项1',
          unitWorkTypeUnit: '',
          confirm: 0,
        ),
      ])
    ]);

    return RecordAccountView(props: RecordAccountListProps(business: mockData));
  }
}
