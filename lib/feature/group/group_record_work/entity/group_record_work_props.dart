import 'dart:convert';

import 'package:gdjg_pure_flutter/data/group_data/group_record/rds/model/param/business_add_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/controller/record_work_detail_controller.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/vm/protocol/dept_detail_us.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';

/// 记工状态回调
typedef RecordWorkStatusCallBack = void Function(
    BusinessType tab, RecordWorkTabType subTab);

/// 记工数据回调
typedef RecordWorkDataCallBack = void Function(
    BusinessType tab, BusinessAddParamModel param);

/// @date 2025/07/29
/// @description GroupRecordWork页入参
class GroupRecordWorkProps {
  /// 页面来源
  /// projectList : 项目列表
  /// workSheet : 考勤表
  GroupRecordWorkFrom? from;

  /// 记工的时间
  String? date;

  /// 记工类型
  BusinessType? businessType;

  /// 项目 dept_id，用于切企业接口，从跳转处传过来，有就请求接口，切换企业
  int deptId;

  GroupRecordWorkProps({
    this.from,
    this.date,
    this.businessType,
    required this.deptId,
  });

  @override
  String toString() {
    return "GroupRecordWorkProps{from: $from, date: $date, businessType: $businessType, deptId: $deptId}";
  }
}

enum RecordWorkTabType {
  /// 记工编辑视图
  record(1),

  /// 记工流水列表
  account(2);

  final int value;

  const RecordWorkTabType(this.value);
}

/// 页面来源
enum GroupRecordWorkFrom {
  /// 项目列表
  projectList('projectList'),

  /// 考勤表
  workSheet('workSheet');

  final String value;

  const GroupRecordWorkFrom(this.value);

  @override
  toString() => value;
}

class WorkerRecordProps {
  /// 工友集
  final List<WorkerBizModel> workers;

  /// 项目详情
  final DeptDetailUS deptDetailUS;

  /// 已经记工过的工友id集
  final List<double>? recordedIds;

  /// 未记工的工友id集
  final List<double>? noRecordedIds;

  /// 记工状态回调
  final RecordWorkStatusCallBack? statusCallBack;

  /// 初始子Tab状态
  final RecordWorkTabType? initialSubTab;

  /// 记工控制器
  final RecordWorkDetailController? recordWorkController;

  WorkerRecordProps({
    required this.workers,
    required this.deptDetailUS,
    this.recordedIds,
    this.noRecordedIds,
    this.statusCallBack,
    this.initialSubTab,
    this.recordWorkController,
  });

  @override
  String toString() {
    return "WorkerRecordProps{workers: ${workers.toString()}, deptDetailUS: ${deptDetailUS.toString()}, recordedIds: $recordedIds, noRecordedIds: $noRecordedIds}";
  }
}
