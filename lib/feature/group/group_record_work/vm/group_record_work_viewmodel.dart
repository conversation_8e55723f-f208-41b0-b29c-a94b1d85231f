import "package:gdjg_pure_flutter/data/group_data/group_record/rds/model/param/business_add_param_model.dart";
import "package:gdjg_pure_flutter/data/worker_data/corp_select/repo/model/corp_select_biz_model.dart";
import "package:gdjg_pure_flutter/data/worker_data/worker_info/repo/model/attendance_worker_biz_model.dart";
import "package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_detail_biz_model.dart";
import "package:gdjg_pure_flutter/feature/group/group_record_work/controller/record_work_detail_controller.dart";
import "package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart";
import "package:gdjg_pure_flutter/feature/group/group_record_work/vm/protocol/dept_detail_us.dart";
import "package:gdjg_pure_flutter/feature/group/group_record_work/vm/protocol/group_record_work_us.dart";
import "package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart";
import "package:gdjg_pure_flutter/utils/system_util/string_util.dart";
import "package:gdjg_pure_flutter/utils/system_util/yprint.dart";
import "package:gdjg_pure_flutter/utils/ui_util/toast_util.dart";
import "package:get/get.dart";

import "../../../../data/group_data/group_guide_lds.dart";
import "../ui_rep/group_record_work_ui_rep.dart";

/// @date 2025/07/29
/// @description GroupRecordWork页ViewModel
/// 1. 处理数据的加载状态，如loading和错误视图等
/// 2. 观测UIRep中的业务实体转为UIState
/// 3. 接受来自view的意图并分发事件进行处理（暂无）
/// 4. 对View层暴露事件（操作视图和处理一些与系统权限交互等场景）
class GroupRecordWorkViewModel {
  var isLoading = false.obs;
  var isShowError = false.obs;
  var us = GroupRecordWorkUS();
  var uiRep = GroupRecordWorkUIRep();

  /// 每个tab项，子tab选中状态
  final itemSubTabInfo = <BusinessType, RecordWorkTabType>{
    BusinessType.workAndAccountTypeDays: RecordWorkTabType.record,
    BusinessType.workAndAccountTypePackage: RecordWorkTabType.record,
    BusinessType.workAndAccountTypeWages: RecordWorkTabType.record,
    BusinessType.workAndAccountTypeLoad: RecordWorkTabType.record,
  }.obs;

  /// 出勤工友原始数据
  var originalWorkers = <WorkerBizModel>[];

  /// 记工公共数据
  var recordWorkPublicData = BusinessAddPublicParamModel(
    identity: 1,
  );

  /// 编辑后的记工数据
  final itemRecordWorkData = <BusinessType, BusinessAddParamModel?>{
    BusinessType.workAndAccountTypeDays: null,
    BusinessType.workAndAccountTypePackage: null,
    BusinessType.workAndAccountTypeWages: null,
    BusinessType.workAndAccountTypeLoad: null,
  };

  late GroupRecordWorkProps _props;

  final GroupGuideLds _groupGuideLds = GroupGuideLds();

  GroupRecordWorkViewModel();

  /// 页面初始化
  /// 1.1. 先获取项目数据: api/dept/detail?dept_id=41679
  /// 1.2. 同时获取用户是否记过工：api/member/has_business，没记工退出页面是弹出挽留弹窗
  /// 2.1 再使用项目数据中的corp_id--切换企业: api/corp/select
  /// 2.2 然后获取项目下的工友：api/workers/get-workers-at-work
  void init(GroupRecordWorkProps props) {
    _props = props;
    pageInit();
  }

  /// 初始化数据同步，如果不需要loading，则在这里去掉
  void fetchWorkerData(List<String> date, String workerNoteId) async {
    yprint('当前日期 YYYY-MM-DD, 可多选$date');
    isLoading.value = true;
    try {
      // 获取工友数据
      var result = await uiRep.getWorkerAtWork(date, workerNoteId);
      originalWorkers = result?.workers ?? [];
    } catch (e) {
      isShowError.value = true;
    } finally {
      isLoading.value = false;
    }
  }

  void pageInit() async {
    /// mock数据
    final mockDays = [
      WorkerBizModel(
        id: 1,
        name: "张三",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 2,
        name: "宇文成都",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 3,
        name: "滚",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 4,
        name: "阿坝滋",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 5,
        name: "张三",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 6,
        name: "宇文成都",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 7,
        name: "滚",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 8,
        name: "阿坝滋",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 9,
        name: "张三",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 10,
        name: "宇文成都",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 11,
        name: "滚",
        isSelf: 1,
      ),
      WorkerBizModel(
        id: 12,
        name: "阿坝滋",
        isSelf: 1,
      ),
    ];
    us.setDayWorkers(mockDays);
    us.setPackageWorkers(mockDays);
    us.setShortWorkers(mockDays);
    us.setAmountWorkers(mockDays);
    us.setSelectList([DateTime.now()]);

    /// 1.1 获取用户是否记过工
    hasBusiness();

    /// 1.2 获取项目详情
    var deptDetail = await fetchDeptDetail(_props.deptId);
    yprint('获取项目详情------$deptDetail');
    if (deptDetail == null) {
      return;
    }
    yprint('获取项目详情，项目名称为------${deptDetail.name}');
    recordWorkPublicData.workNoteId = deptDetail.workNoteId.toString();
    us.setDeptDetailUS(DeptDetailUS(
      workNoteId: deptDetail.workNoteId.toString(),
      workNoteName: deptDetail.name,
    ));

    /// 2.1 切换企业
    corpSelect(deptDetail.corpId.toString());

    /// 2.2 如果有项目详情，则获取项目下的工友数据
    final dates = us.getSelectDateFormat();
    fetchWorkerData(dates, deptDetail.workNoteId.toString());
  }

  /// 获取项目详情
  Future<DeptDetailBizModel?> fetchDeptDetail(int deptId) async {
    isLoading.value = true;
    try {
      // 获取班组详情
      return await uiRep.getDeptDetail(deptId);
    } catch (e) {
      isShowError.value = true;
      return null;
    } finally {
      isLoading.value = false;
    }
  }

  /// 切换企业
  void corpSelect(String corpId) async {
    uiRep.getCorpSelect(corpId);
  }

  /// 获取用户是否记过工
  void hasBusiness() async {
    final result = await uiRep.hasBusiness();
    yprint('用户是否记过工 ========> $result');
    us.setHasBusiness(result);
  }

  void recordWorkTabChange(BusinessType tab, RecordWorkTabType subTab) {
    yprint('$tab type 切换 ========> $subTab');
    itemSubTabInfo[tab] = subTab;
    us.setCurrentSubTab(subTab);
    updateRecordWorkCount(tab);
  }

  /// 更新记工数据
  void updateRecordWork(BusinessType tab, BusinessAddParamModel param) {
    yprint('$tab 更新记工数据 ========> ${param.toMap()}');
    itemRecordWorkData[tab] = param;
    updateRecordWorkCount(tab);
  }

  /// 更新 UI数据
  void updateRecordWorkCount(BusinessType tab) {
    final param = itemRecordWorkData[tab];
    if (param == null || param.worker_id.isNullOrEmpty()) {
      yprint('$tab 更新记工 UI 数据 ========> 0');
      us.setConfirmWorkerCount(0);
      return;
    }
    final count = param.worker_id?.split(',').length;
    yprint('$tab 更新记工 UI 数据 ========> $count');
    us.setConfirmWorkerCount(count ?? 0);
  }

  void onTabChange(BusinessType tab) {
    final subTab = itemSubTabInfo[tab];
    yprint('$tab 切换 ========> $subTab');
    us.setCurrentTab(tab);
    us.setCurrentSubTab(subTab ?? RecordWorkTabType.record);
    updateRecordWorkCount(tab);
  }

  void onSelectDateChange(List<DateTime> date) {
    yprint('选择日期 ========> $date');
    us.setSelectList(date);
    recordWorkPublicData.businessTime = us.getSelectDateFormat().join(',');

    /// 1. 从新获取，该日期下的工友数据
    /// 2. 获取改日期的流水数据
  }

  /// 提交记工
  void onSubmitClick(RecordWorkDetailController controller) async {
    /// 根据当前选中的tab提交记工数据
    final data = controller.getBusinessAddParamModel();
    yprint('controller 获取记工数据 XXXXXXXXXX  ===== ${data.toMap()}');
    if (data.worker_id.isNullOrEmpty()) {
      ToastUtil.showToast('请先选择工友');
      return;
    }
  }

  Future<bool> checkGuideStep2() async =>
      !await _groupGuideLds.fetchGuided(GroupGuideLds.maskStep2);

  Future<bool> checkGuideStep3() async =>
      !await _groupGuideLds.fetchGuided(GroupGuideLds.maskStep3);

  Future<bool> checkGuideStep5() async =>
      !await _groupGuideLds.fetchGuided(GroupGuideLds.maskStep5);

  Future<bool> completeGuideStep2() async =>
      await _groupGuideLds.updateGuided(GroupGuideLds.maskStep2, true);

  Future<bool> completeGuideStep3() async =>
      await _groupGuideLds.updateGuided(GroupGuideLds.maskStep3, true);

  Future<bool> completeGuideStep5() async =>
      await _groupGuideLds.updateGuided(GroupGuideLds.maskStep5, true);
}
