import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_record/rds/model/param/business_add_param_model.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/controller/record_work_detail_controller.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/entity/group_record_work_props.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/amout_work_record_view/amount_work_record_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/day_worker_record_view/day_worker_record_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/package_worker_record_view/package_worker_record_view.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/view/short_work_record_view/short_work_record_view.dart';
import 'package:gdjg_pure_flutter/feature/group/guide/group_guide_provider.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/select_record_date_view.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/vm/protocol/worker_flow_us.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/utils/route_util/base_page/base_ful_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/dynamic_title_helper.dart';
import 'package:gdjg_pure_flutter/widget/round_tab_indicator_widget.dart';
import 'package:get/get.dart';
import 'package:tutorial_coach_mark/tutorial_coach_mark.dart';

import '../../../utils/ui_util/widget_util.dart';

import 'vm/group_record_work_viewmodel.dart';

/// 班组记工页
/// @date 2025/07/29
/// @param props 页面路由参数
/// @returns
/// @description GroupRecordWork页面入口
class GroupRecordWorkPage extends BaseFulPage {
  GroupRecordWorkPage({super.key})
      : super(
          appBar: YPAppBar(title: "标题"),
          canBack: false,
        );

  @override
  State<GroupRecordWorkPage> createState() => _GroupRecordWorkPageState();
}

class _GroupRecordWorkPageState extends BaseFulPageState<GroupRecordWorkPage>
    with SingleTickerProviderStateMixin {
  late GroupRecordWorkProps props;
  final GroupRecordWorkViewModel viewModel =
      Get.put(GroupRecordWorkViewModel());
  TabController? _tabController;
  final RecordWorkDetailController _dayController = RecordWorkDetailController(
    WorkRecordParamModel(
        business_type: BusinessType.workAndAccountTypeDays.code.toString()),
  );
  final RecordWorkDetailController _packageController =
      RecordWorkDetailController(
    WorkRecordParamModel(
        business_type: BusinessType.workAndAccountTypePackage.code.toString()),
  );
  final RecordWorkDetailController _shortController =
      RecordWorkDetailController(
    WorkRecordParamModel(
        business_type: BusinessType.workAndAccountTypeWages.code.toString()),
  );
  final RecordWorkDetailController _amountController =
      RecordWorkDetailController(
    WorkRecordParamModel(
        business_type: BusinessType.workAndAccountTypeLoad.code.toString()),
  );

  final GlobalKey _recordClassifyKey = GlobalKey();
  final GlobalKey<DayWorkerRecordViewState> _dayWorkKey =
      GlobalKey<DayWorkerRecordViewState>();

  TutorialCoachMark? _tutorialCoachMarkStep2;
  TutorialCoachMark? _tutorialCoachMarkStep3;
  TutorialCoachMark? _tutorialCoachMarkStep5;

  @override
  onPageRoute(Object? routeParams, bool fromLaunchTask) {
    super.onPageRoute(routeParams, fromLaunchTask);
    props = routeParams as GroupRecordWorkProps;
    // 初始化记工页
    viewModel.init(props);
    // 设置项目标题监听
    _setupProjectTitleListener();
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
        length: viewModel.us.recordWorkBarItems.length, vsync: this);
    _tabController?.addListener(_onTabChange);
    WidgetsBinding.instance.addPostFrameCallback((_) => _checkGuideStep2());
    _dayController.addListener(onDayRecordDataChange);
    _packageController.addListener(onPackageRecordDataChange);
    _shortController.addListener(onShortRecordDataChange);
    _amountController.addListener(onAmountRecordDataChange);
  }

  @override
  bool? callbackIntercept() =>
      _tutorialCoachMarkStep2?.isShowing != true &&
      _tutorialCoachMarkStep3?.isShowing != true &&
      _tutorialCoachMarkStep5?.isShowing != true;

  @override
  dispose() {
    super.dispose();
    _tabController?.removeListener(_onTabChange);
    _dayController.removeListener(onDayRecordDataChange);
    _packageController.removeListener(onPackageRecordDataChange);
    _shortController.removeListener(onShortRecordDataChange);
    _amountController.removeListener(onAmountRecordDataChange);
    _dayController.dispose();
    _packageController.dispose();
    _shortController.dispose();
    _amountController.dispose();
    _tabController?.dispose();
  }

  @override
  void onPageDestroy() {
    super.onPageDestroy();
    Get.delete<GroupRecordWorkViewModel>();
  }

  @override
  Widget yBuild(BuildContext context) {
    return _buildContentView();
  }

  void _onTabChange() {
    final tab = viewModel.us.recordWorkBarItems[_tabController!.index].type;
    print('当前Tab索引: ${tab}');
    viewModel.onTabChange(tab);
  }

  void onDayRecordDataChange() {
    final model = _dayController.getBusinessAddParamModel();
    viewModel.updateRecordWork(BusinessType.workAndAccountTypeDays, model);
  }

  void onPackageRecordDataChange() {
    final model = _packageController.getBusinessAddParamModel();
    viewModel.updateRecordWork(BusinessType.workAndAccountTypePackage, model);
  }

  void onShortRecordDataChange() {
    final model = _shortController.getBusinessAddParamModel();
    viewModel.updateRecordWork(BusinessType.workAndAccountTypeWages, model);
  }

  void onAmountRecordDataChange() {
    final model = _amountController.getBusinessAddParamModel();
    viewModel.updateRecordWork(BusinessType.workAndAccountTypeLoad, model);
  }

  /// 设置项目标题监听器
  void _setupProjectTitleListener() {
    // 监听 deptDetailUS 响应式变量的变化
    DynamicTitleHelper.listenAndUpdateTitle(
      this,
      viewModel.us.deptDetailUSObservable, // 监听响应式变量
      (deptDetail) => DynamicTitleHelper.formatProjectTitle(
        deptDetail?.workNoteName, // 使用 workNoteName 属性
        maxLength: 15, // 限制标题长度
      ),
      defaultTitle: "班组记工",
    );
  }

  Widget _buildContentView() {
    return Column(
      children: [
        _buildDateView(),
        _buildPagerView(),
      ],
    );
  }

  Widget _buildPagerView() {
    return Expanded(
      child: Column(
        children: [
          _buildTitleBar(),
          _buildPageContent(),
          _buildBottom(),
        ],
      ),
    );
  }

  Widget _buildTitleBar() {
    return Container(
      key: _recordClassifyKey,
      color: Colors.white,
      child: TabBar(
          controller: _tabController,
          labelColor: ColorsUtil.ypPrimaryColor,
          unselectedLabelColor: ColorsUtil.black85,
          labelStyle:  TextStyle(
            fontSize: 17.sp,
          ),
          indicator: RoundUnderlineTabIndicator(
            borderSide: BorderSide(width: 3, color: ColorsUtil.ypPrimaryColor),
          ),
          dividerHeight: 1,
          dividerColor: ColorsUtil.ypBgColor,
          tabs: [
            Tab(
              child: Text(BusinessType.workAndAccountTypeDays.desc),
            ),
            Tab(
              child: Text(BusinessType.workAndAccountTypePackage.desc),
            ),
            Tab(
              child: Text(BusinessType.workAndAccountTypeWages.desc),
            ),
            Tab(
              child: Text(BusinessType.workAndAccountTypeLoad.desc),
            ),
          ]),
    );
  }

  Widget _buildPageContent() {
    return Expanded(
      child: Container(
        color: Colors.white,
        child: TabBarView(controller: _tabController, children: [
          _buildDayWorkerPage(),
          _buildPackageWorkerPage(),
          _buildShortWorkPage(),
          _buildAmountWorkPage(),
        ]),
      ),
    );
  }

  Widget _buildBottom() {
    return Obx(() {
      if (viewModel.us.currentSubTab == RecordWorkTabType.account) {
        return Container();
      }
      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(width: 1, color: ColorsUtil.ypBgColor),
          ),
          color: Colors.white,
        ),
        child: Container(
            height: 48,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: ColorsUtil.ypPrimaryColor,
            ),
            child: TextButton(
              onPressed: () {
                onSubmitClick();
              },
              child: Obx(() {
                return Text(
                  "确认记工(${viewModel.us.confirmWorkerCount})",
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 17.sp,
                      fontWeight: FontWeight.bold),
                );
              }),
            )),
      );
    });
  }

  Widget _buildDayWorkerPage() {
    return Obx(() {
      return Container(
        color: Colors.white,
        child: DayWorkerRecordView(
            key: _dayWorkKey,
            props: WorkerRecordProps(
              workers: viewModel.us.dayWorkers,
              deptDetailUS: viewModel.us.deptDetailUS,
              recordedIds: [3, 4],
              initialSubTab:
                  viewModel.itemSubTabInfo[BusinessType.workAndAccountTypeDays],
              // 传递当前子Tab状态
              statusCallBack: (BusinessType tab, RecordWorkTabType subTab) {
                viewModel.recordWorkTabChange(tab, subTab);
              },
              recordWorkController: _dayController,
            )),
      );
    });
  }

  Widget _buildPackageWorkerPage() {
    return Obx(() {
      return Container(
          color: Colors.white,
          child: PackageWorkerRecordView(
            props: WorkerRecordProps(
              workers: viewModel.us.packageWorkers,
              deptDetailUS: viewModel.us.deptDetailUS,
              initialSubTab: viewModel
                  .itemSubTabInfo[BusinessType.workAndAccountTypePackage],
              // 传递当前子Tab状态
              statusCallBack: (BusinessType tab, RecordWorkTabType subTab) {
                viewModel.recordWorkTabChange(tab, subTab);
              },
              recordedIds: [],
              recordWorkController: _packageController,
            ),
          ));
    });
  }

  Widget _buildShortWorkPage() {
    return Container(
        color: Colors.white,
        child: ShortWorkRecordView(
          props: WorkerRecordProps(
            workers: viewModel.us.shortWorkers,
            deptDetailUS: viewModel.us.deptDetailUS,
            initialSubTab:
                viewModel.itemSubTabInfo[BusinessType.workAndAccountTypeWages],
                // 传递当前子Tab状态
            statusCallBack: (BusinessType tab, RecordWorkTabType subTab) {
              viewModel.recordWorkTabChange(tab, subTab);
            },
            recordedIds: [],
            recordWorkController: _shortController,
          ),
        ));
  }

  Widget _buildAmountWorkPage() {
    return Container(
        color: Colors.white,
        child: AmountWorkRecordView(
          props: WorkerRecordProps(
            workers: viewModel.us.amountWorkers,
            deptDetailUS: viewModel.us.deptDetailUS,
            initialSubTab:
                viewModel.itemSubTabInfo[BusinessType.workAndAccountTypeLoad],
                // 传递当前子Tab状态
            statusCallBack: (BusinessType tab, RecordWorkTabType subTab) {
              viewModel.recordWorkTabChange(tab, subTab);
            },
            recordedIds: [],
            recordWorkController: _amountController,
          ),
        ));
  }

  /// 项目日期选择
  Widget _buildDateView() {
    return GestureDetector(
      onTap: () {
        showSelectRecordDate();
      },
      child: Container(
        width: double.infinity,
        margin: EdgeInsets.only(top: 8.h),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(width: 0.5.h, color: ColorsUtil.ypBgColor),
          ),
          color: Colors.white,
        ),
        height: 45,
        child: Center(
          child: Row(
            children: [
              Text('日期：',
                  style: TextStyle(fontSize: 17, color: Color(0xFF323233))),
              Obx(() => Text(viewModel.us.getSelectDateDisplay(),
                  style: TextStyle(
                      fontSize: 17,
                      color: Color(0xFF323233),
                      fontWeight: FontWeight.w500))),
              Spacer(),
              if (viewModel.us.selectDates.length > 1)
                Text('可多选',
                    style: TextStyle(fontSize: 14, color: Color(0xFF323233))),
              SizedBox(width: 4),
              Image.asset(Assets.commonIconArrowRightGrey,
                  width: 18, height: 18)
            ],
          ),
        ),
      ),
    );
  }

  /// 选择记工日期
  void showSelectRecordDate() {
    YPRoute.openDialog(
      builder: (context) => SelectRecordDateView(
        note_id: viewModel.us.deptDetailUS.workNoteId,
        isRecordWorkPoints: true,
        dateList: viewModel.us.selectDates,
        isChangeChoice: false,
        isMultiple: true,
        onSelect: (dateList) {
          viewModel.onSelectDateChange(dateList);
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  void onSubmitClick() {
    viewModel.onSubmitClick(getRecordWorkController());
  }

  RecordWorkDetailController getRecordWorkController() {
    switch (viewModel.us.currentTab) {
      case BusinessType.workAndAccountTypePackage:
        return _packageController;
      case BusinessType.workAndAccountTypeWages:
        return _shortController;
      case BusinessType.workAndAccountTypeLoad:
        return _amountController;
      default:
        return _dayController;
    }
  }

  Future<void> _checkGuideStep2() async {
    if (await viewModel.checkGuideStep2()) {
      _showTutorialStep2();
    } else {
      _checkGuideStep3();
    }
  }

  Future<void> _checkGuideStep3() async {
    if (_tabController?.index == 0 && await viewModel.checkGuideStep3()) {
      _showTutorialStep3();
    } else {
      // 如果第三步引导已完成,直接检查第五步引导
      // 正常的流程是在第三步引导后进入下一个页面,返回后检查第五步引导
      // 这里处理异常情况避免第五步引导不显示
      _checkGuideStep5();
    }
  }

  Future<void> _checkGuideStep5() async {
    if (await viewModel.checkGuideStep5()) {
      _showTutorialStep5();
    }
  }

  void _showTutorialStep2() {
    final Rect? rect = WidgetUtils.calculateBounding([_recordClassifyKey]);
    if (rect == null) {
      return;
    }
    _tutorialCoachMarkStep2?.finish();
    _tutorialCoachMarkStep2 = GroupGuideProvider.showStep2(
      context,
      rect,
      () {
        _tutorialCoachMarkStep2?.finish();
        _checkGuideStep3();
      },
    );
    viewModel.completeGuideStep2();
  }

  void _showTutorialStep3() {
    final Rect? rect = _dayWorkKey.currentState?.findWorkSelectorRect();
    if (rect == null) {
      return;
    }
    _tutorialCoachMarkStep3?.finish();
    _tutorialCoachMarkStep3 = GroupGuideProvider.showStep3(
      context,
      rect,
      () {
        _tutorialCoachMarkStep3?.finish();
        YPRoute.openPage(RouteNameCollection.contact)
            ?.then((_) => _checkGuideStep5());
      },
    );
    viewModel.completeGuideStep3();
  }

  void _showTutorialStep5() {
    final Rect? rect = _dayWorkKey.currentState?.findTimeRect();
    if (rect == null) {
      return;
    }
    _tutorialCoachMarkStep5?.finish();
    _tutorialCoachMarkStep5 = GroupGuideProvider.showStep5(
      context,
      rect,
      () => _tutorialCoachMarkStep5?.finish(),
    );
    viewModel.completeGuideStep5();
  }
}
