import 'dart:math';

import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_record/rds/model/param/business_add_param_model.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';

/// 记工数据控制器
class RecordWorkDetailController extends ValueNotifier<BusinessAddParamModel> {
  RecordWorkDetailController(BusinessAddParamModel param) : super(param) {
    value = param;
  }

  BusinessAddParamModel get param => value;

  updateWorkerIds(String? ids) {
    value.worker_id = ids;
    yprint('更新员工id：$ids-------${value.toMap()}');
    notifyListeners();
  }

  /// 更新记工数据
  updateRecordWorkData(BusinessAddParamModel param) {
    value = param;
    notifyListeners();
  }

  /// 获取记工数据
  BusinessAddParamModel getBusinessAddParamModel() {
    return param;
  }
}
