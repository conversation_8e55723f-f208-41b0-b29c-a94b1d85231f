import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';

import 'bubble.dart';

class GuideBubble extends StatefulWidget {
  final int trianglePosition;
  final double triangleOffset;
  final String voice;
  final bool autoPlay;
  final String step;
  final String content;
  final String next;
  final void Function() nextAction;

  const GuideBubble(
      {super.key,
      this.trianglePosition = TrianglePosition.topLeft,
      this.triangleOffset = -32,
      required this.voice,
      this.autoPlay = true,
      required this.step,
      required this.content,
      this.next = "下一步",
      required this.nextAction});

  @override
  State<StatefulWidget> createState() => _GuideBubbleState();
}

class _GuideBubbleState extends State<GuideBubble> {
  final AudioPlayer _player = AudioPlayer();

  PlayerState _playerState = PlayerState.stopped;

  @override
  void initState() {
    super.initState();
    _player.setReleaseMode(ReleaseMode.loop);
    _player.onPlayerStateChanged.listen((state) {
      if (!mounted) {
        return;
      }
      setState(() => _playerState = state);
    });
    _initializePlayer(widget.voice, widget.autoPlay);
  }

  @override
  void dispose() {
    _player.stop();
    _player.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Bubble(
        padding: EdgeInsets.all(10.w),
        trianglePosition: widget.trianglePosition,
        decoration: BubbleDecoration(
            backgroundColor: Colors.white,
            radius: ContentRadius.all(6.w),
            triangleBase: 16.w,
            triangleHeight: 10.w,
            triangleOffset: widget.triangleOffset),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () {
                    if (_player.state == PlayerState.playing) {
                      _player.stop();
                    } else {
                      _replay();
                    }
                  },
                  child: Image.asset(
                    _playerState == PlayerState.playing
                        ? 'assets/images/worker/waa_ic_guide_voice_on.gif'
                        : 'assets/images/worker/waa_ic_guide_voice_off.webp',
                    height: 16.h,
                  ),
                ),
                Spacer(),
                Text(
                  widget.step,
                  style: TextStyle(
                    color: ColorsUtil.primaryColor,
                    fontSize: 16.sp,
                  ),
                ),
              ],
            ),
            SizedBox(height: 4.h),
            Text(
              widget.content,
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w400,
                fontSize: 18.sp,
              ),
            ),
            SizedBox(height: 4.h),
            Text(
              "(点击小喇叭，可关闭语音)",
              style: TextStyle(
                color: Colors.black,
                fontSize: 14.sp,
              ),
            ),
            SizedBox(height: 6.h),
            Align(
              alignment: Alignment.bottomRight,
              child: ElevatedButton(
                onPressed: widget.nextAction,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorsUtil.primaryColor,
                  foregroundColor: Colors.white,
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.w),
                  ),
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
                  minimumSize: Size(26.w, 26.h),
                  alignment: Alignment.center,
                ).copyWith(
                  elevation: WidgetStateProperty.all(0),
                  overlayColor: WidgetStateProperty.all(Colors.transparent),
                  splashFactory: NoSplash.splashFactory,
                ),
                child: Text(
                  widget.next,
                  style: TextStyle(
                    fontSize: 14.sp,
                    height: 1.0.h,
                  ),
                ),
              ),
            )
          ],
        ));
  }

  void _initializePlayer(String path, bool autoPlay) async {
    try {
      await _player.setSource(AssetSource(path));
    } catch (_) {}
    if (autoPlay) {
      await _player.resume();
    }
  }

  void _replay() async {
    await _player.seek(Duration.zero);
    await _player.resume();
  }
}
