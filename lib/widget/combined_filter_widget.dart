import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/choose_project_filter/entity/choose_project_filter_props.dart';
import 'package:gdjg_pure_flutter/feature/group/common_page/select_type_page/entity/select_type_props.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_selector/vm/protocol/worker_selector_ui_state.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/init_module/init_route.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/button_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/month_select_util.dart';
import 'package:gdjg_pure_flutter/widget/start_end_date_picker_widget.dart';

/// 筛选数据模型
class CombinedFilterData {
  final DateTime startDate;
  final DateTime endDate;
  final List<RwaRecordType> selectedTypes;

  CombinedFilterData({
    DateTime? startDate,
    DateTime? endDate,
    this.selectedTypes = const [],
  })  : startDate = startDate ?? DateTime(DateTime.now().year - 1, 1, 1),
        endDate = endDate ?? DateTime.now();

  @override
  String toString() {
    return 'CombinedFilterData(startDate: $startDate, endDate: $endDate, selectedTypes: $selectedTypes)';
  }
}

/// 组合筛选器
class CombinedFilterWidget extends StatefulWidget {
  /// 筛选条件变化时的回调函数，返回筛选后的数据模型
  final Function(CombinedFilterData)? onFilterChanged;

  /// 点击"全部"按钮时的回调函数
  final Function()? onSelectAll;

  /// 工友选择回调函数
  final Function(List<WorkerContactUIState>)? onWorkerSelected;

  /// 初始开始日期，默认为去年1月1日
  final DateTime? initialStartDate;

  /// 初始结束日期，默认为当前日期
  final DateTime? initialEndDate;

  /// 初始选中的记录类型列表
  final List<RwaRecordType>? initialRecordTypes;

  /// 初始项目列表，用于项目筛选
  final List<ChooseProjectFilterItem>? initialProjectList;

  /// 初始选中的工友列表
  final List<WorkerContactUIState>? initialSelectedWorkers;

  /// 是否显示项目筛选按钮，默认为false
  final bool showProjectFilter;

  /// 是否显示工友筛选按钮，默认为true
  final bool showWorkerFilter;

  /// 是否显示类型筛选按钮，默认为true
  final bool showTypeFilter;

  const CombinedFilterWidget({
    super.key,
    this.onFilterChanged,
    this.initialStartDate,
    this.initialEndDate,
    this.initialRecordTypes,
    this.initialProjectList,
    this.initialSelectedWorkers,
    this.showProjectFilter = false,
    this.showWorkerFilter = true,
    this.showTypeFilter = true,
    this.onSelectAll,
    this.onWorkerSelected,
  });

  @override
  State<CombinedFilterWidget> createState() => _CombinedFilterWidgetState();
}

class _CombinedFilterWidgetState extends State<CombinedFilterWidget> {
  late DateTime _startDate;
  late DateTime _endDate;

  late DateTime _initialStartDate;
  late DateTime _initialEndDate;
  late List<RwaRecordType> _initialRwaRecordTypes;

  int? _selectedMonthValue;
  bool _isMonthSelected = false;
  bool _isAllSelected = false;
  List<RwaRecordType> _selectedTypes = [];

  String? _selectProject;
  List<ChooseProjectFilterItem> _projectList = [];
  List<WorkerContactUIState> _selectedWorkers = [];

  @override
  void initState() {
    super.initState();
    // 初始化时间
    _initialStartDate =
        widget.initialStartDate ?? DateTime(DateTime.now().year - 1, 1, 1);
    _initialEndDate = widget.initialEndDate ?? DateTime.now();
    _initialRwaRecordTypes = widget.initialRecordTypes ?? [];

    // 设置当前时间
    _startDate = _initialStartDate;
    _endDate = _initialEndDate;
    _selectedTypes = _initialRwaRecordTypes;

    _selectProject = widget.initialProjectList?.firstOrNull?.workNoteName ?? '选择项目';
    _projectList = widget.initialProjectList ?? [];
    _selectedWorkers = widget.initialSelectedWorkers ?? [];
  }

  @override
  void didUpdateWidget(covariant CombinedFilterWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 检查新的初始日期和结束日期是否与旧的不同
    if (widget.initialStartDate != oldWidget.initialStartDate ||
        widget.initialEndDate != oldWidget.initialEndDate ||
        widget.initialRecordTypes != oldWidget.initialRecordTypes ||
        widget.initialSelectedWorkers != oldWidget.initialSelectedWorkers) {
      // 更新 _startDate 和 _endDate
      setState(() {
        _initialStartDate =
            widget.initialStartDate ?? DateTime(DateTime.now().year - 1, 1, 1);
        _initialEndDate = widget.initialEndDate ?? DateTime.now();
        _initialRwaRecordTypes = widget.initialRecordTypes ?? [];

        // 设置当前时间
        _startDate = _initialStartDate;
        _endDate = _initialEndDate;
        _selectedTypes = _initialRwaRecordTypes;
        _selectedWorkers = widget.initialSelectedWorkers ?? [];
      });
    }
  }

  /// 重置所有筛选状态
  void _resetFilterStates() {
    _isMonthSelected = false;
    _isAllSelected = false;
    _selectedMonthValue = null;
  }

  /// 设置月份选择状态
  void _setMonthSelectedState(int month) {
    _resetFilterStates();
    _isMonthSelected = true;
    _selectedMonthValue = month;
  }

  /// 设置全部选择状态
  void _setAllSelectedState() {
    _resetFilterStates();
    _isAllSelected = true;
  }

  /// 触发筛选回调
  void _triggerCallback() {
    if (widget.onFilterChanged != null) {
      final filterData = CombinedFilterData(
        startDate: _startDate,
        endDate: _endDate,
        selectedTypes: _selectedTypes,
      );
      widget.onFilterChanged!(filterData);
    }
  }

  /// 清空筛选条件
  void _clearFilter(String filterType) {
    setState(() {
      switch (filterType) {
        case 'project':
          _selectProject = '选择项目';
          break;
        case 'worker':
          _selectedWorkers = [];
          widget.onWorkerSelected?.call([]);
          break;
        case 'type':
          _selectedTypes = [];
          break;
      }
    });
    _triggerCallback();
  }

  /// 检查筛选条件是否有内容
  bool _hasFilterContent(String filterType) {
    switch (filterType) {
      case 'project':
        return _selectProject != null && _selectProject != '选择项目';
      case 'worker':
        return _selectedWorkers.isNotEmpty;
      case 'type':
        return _selectedTypes.isNotEmpty;
      default:
        return false;
    }
  }

  /// 获取筛选按钮显示文本
  String _getFilterButtonText(String filterType) {
    switch (filterType) {
      case 'project':
        return _selectProject ?? '选择项目';
      case 'worker':
        if (_selectedWorkers.isEmpty) {
          return '全部工友';
        } else {
          return _selectedWorkers.map((w) => w.name ?? '').join('|');
        }
      case 'type':
        if (_selectedTypes.isEmpty) {
          return '类型';
        } else if (_selectedTypes.length == 1) {
          return _selectedTypes.first.label;
        } else {
          return _selectedTypes.map((type) => type.label).join('|');
        }
      default:
        return '';
    }
  }

  /// 筛选按钮
  Widget _buildFilterButton({
    required String filterType,
    required VoidCallback onPressed,
    int maxCharsPerLine = 13,
  }) {
    String text = _getFilterButtonText(filterType);
    List<String> lines = _splitTextIntoLines(text, maxCharsPerLine);
    bool hasContent = _hasFilterContent(filterType);

    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: EdgeInsets.only(left: 8.1.w, right: 7.w, top: 6.5.h, bottom: 6.5.h),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.r),
          color: ColorsUtil.inputBgColor,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: lines
                  .map((line) => Text(
                        line,
                        style: TextStyle(fontSize: 13.sp),
                      ))
                  .toList(),
            ),
            SizedBox(width: 2.6.w),
            GestureDetector(
              onTap: hasContent ? () => _clearFilter(filterType) : null,
              child: Image(
                image: AssetImage(hasContent
                    ? 'assets/images/common/icon_close.webp'
                    : Assets.commonIconTagSelect),
                width: 16.w,
                height: 16.h,
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 检查是否有非日期筛选条件
    bool hasNonDateFilters = widget.showProjectFilter ||
        widget.showWorkerFilter ||
        widget.showTypeFilter;

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          _buildDateRangeSection(),
          // 无其他筛选条件去掉分割线
          if (hasNonDateFilters) ...[
            Divider(
              height: 1,
              indent: 16.w,
              endIndent: 16.w,
              color: ColorsUtil.divideLineColor,
            ),
            _buildFilterButtonsSection(),
          ],
        ],
      ),
    );
  }

  /// 构建日期范围选择区域
  Widget _buildDateRangeSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Row(
              children: [
                Flexible(
                  child: _buildDateSelector(_formatDate(_startDate), true),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  size: 18.w,
                ),
                Padding(
                  padding: EdgeInsets.only(right: 5.w),
                  child: Text(
                    '至',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF323233),
                    ),
                  ),
                ),
                Flexible(
                  child: _buildDateSelector(_formatDate(_endDate), false),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  size: 18.w,
                ),
              ],
            ),
          ),
          ButtonUtil.buildCommonButtonWithBorder(
            height: 30.h,
            width: 50.w,
            text: _isMonthSelected
                ? '${_selectedMonthValue.toString().padLeft(2, '0')}月'
                : '月份',
            onPressed: () => _showMonthPicker(),
            selected: _isMonthSelected,
          ),
          SizedBox(width: 8.w),
          ButtonUtil.buildCommonButtonWithBorder(
            height: 30.h,
            width: 50.w,
            text: '全部',
            selected: _isAllSelected,
            onPressed: () {
              if (widget.onSelectAll != null) {
                setState(() {
                  _setAllSelectedState();
                });
                widget.onSelectAll?.call();
                return;
              }
              setState(() {
                _setAllSelectedState();
                _startDate = _initialStartDate;
                _endDate = _initialEndDate;
              });
              _triggerCallback();
            },
          ),
        ],
      ),
    );
  }

  /// 构建筛选按钮区域
  Widget _buildFilterButtonsSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (widget.showProjectFilter) ...[
                _buildFilterButton(
                  filterType: 'project',
                  onPressed: _onProjectButtonPressed,
                ),
                SizedBox(width: 8.w),
              ],
              if (widget.showWorkerFilter) ...[
                _buildFilterButton(
                  filterType: 'worker',
                  onPressed: _onWorkerFilterPressed,
                ),
              ],
              if (widget.showTypeFilter) ...[
                SizedBox(width: 8.w),
                _buildFilterButton(
                  filterType: 'type',
                  onPressed: _onTypeButtonPressed,
                  maxCharsPerLine: 10,
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// 将文本分行
  List<String> _splitTextIntoLines(String text, int maxCharsPerLine) {
    if (text.length <= maxCharsPerLine) {
      return [text];
    }

    List<String> lines = [];
    for (int i = 0; i < text.length; i += maxCharsPerLine) {
      int end = (i + maxCharsPerLine < text.length) ? i + maxCharsPerLine : text.length;
      lines.add(text.substring(i, end));
    }
    return lines;
  }

  /// 处理项目按钮点击
  void _onProjectButtonPressed() {
    final props = ChooseProjectFilterProps(workNoteList: _projectList);
    YPRoute.openPage(RouteNameCollection.chooseProjectFilter, params: props)
        ?.then((res) => {
              res as String?,
              setState(() {
                _selectProject = res;
              })
            });
  }

  /// 处理类型按钮点击
  void _onTypeButtonPressed() {
    final props = SelectTypeProps(
      recordTypeList: _selectedTypes,
      isShowExpense: false,
    );

    YPRoute.openPage(RouteNameCollection.selectRecordType, params: props)
        ?.then((result) {
      if (result != null && result is List<RwaRecordType>) {
        setState(() {
          _selectedTypes = result;
        });
        _triggerCallback();
      }
    });
  }

  /// 处理工友筛选按钮点击
  void _onWorkerFilterPressed() {
    YPRoute.openPage(RouteNameCollection.workerSelector)?.then((result) {
      if (result != null && result is List<WorkerContactUIState>) {
        setState(() {
          _selectedWorkers = result;
        });
        widget.onWorkerSelected?.call(result);
      }
    });
  }

  /// 构建日期选择器
  Widget _buildDateSelector(String dateText, bool isStartDate) {
    return GestureDetector(
      onTap: () => _showDatePicker(isStartDate),
      child: Text(
        dateText,
        style: TextStyle(
          fontSize: 15.sp,
          fontWeight: FontWeight.w800,
          color: const Color(0xFF323233),
        ),
      ),
    );
  }

  /// 格式化日期显示
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 显示日期选择器
  void _showDatePicker(bool isStartDate) {
    YPRoute.openDialog(
        builder: (context) => StartEndDatePickerWidget(
              initialDate: isStartDate ? _startDate : _endDate,
              isStartDate: isStartDate,
              minDate: isStartDate ? null : _startDate, // 结束日期不能早于开始日期
              onConfirm: (selectedDate) {
                setState(() {
                  if (isStartDate) {
                    _startDate = selectedDate;
                  } else {
                    _endDate = selectedDate;
                  }
                  _resetFilterStates();
                });
                _triggerCallback();
              },
            ),
        alignment: Alignment.bottomCenter,
        maskColor: Colors.black.withValues(alpha: 0.5));
  }

  /// 显示月份选择器
  void _showMonthPicker() {
    MonthSelectUtil.show(
      context: context,
      initialYear: _isMonthSelected ? _startDate.year : null,
      initialMonth: _isMonthSelected ? _selectedMonthValue : null,
      onSelected: (year, month) {
        setState(() {
          _startDate = DateTime(year, month, 1); // 月份第一天
          _endDate = DateTime(year, month + 1, 0); // 月份最后一天
          _setMonthSelectedState(month);
        });
        _triggerCallback();
      },
    );
  }
}
