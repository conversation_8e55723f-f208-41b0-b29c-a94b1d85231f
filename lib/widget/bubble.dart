import 'dart:math';
import 'package:flutter/material.dart';

/// 气泡Widget
/// [child] 内部Widget
/// [padding] 内部Widget的边距
/// [trianglePosition] 三角形的位置, see: [TrianglePosition]
/// [decoration] 提供装饰配置
class Bubble extends StatelessWidget {
  final Widget child;
  final EdgeInsets padding;
  final int trianglePosition;
  final BubbleDecoration decoration;

  const Bubble(
      {super.key,
      this.padding = EdgeInsets.zero,
      this.trianglePosition = TrianglePosition.topCenter,
      required this.child,
      this.decoration = const BubbleDecoration()});

  @override
  Widget build(BuildContext context) {
    final EdgeInsets offset;
    switch (trianglePosition & TrianglePosition._direction_mask) {
      case TrianglePosition._top:
        offset = EdgeInsets.only(top: decoration.triangleHeight);
        break;
      case TrianglePosition._right:
        offset = EdgeInsets.only(right: decoration.triangleHeight);
        break;
      case TrianglePosition._bottom:
        offset = EdgeInsets.only(bottom: decoration.triangleHeight);
        break;
      case TrianglePosition._left:
        offset = EdgeInsets.only(left: decoration.triangleHeight);
        break;
      default:
        offset = EdgeInsets.zero;
    }
    return CustomPaint(
      painter: _BubblePainter(decoration, trianglePosition),
      child: Padding(padding: padding + offset, child: child),
    );
  }
}

class _BubblePainter extends CustomPainter {
  final BubbleDecoration decoration;
  final int trianglePosition;

  _BubblePainter(this.decoration, this.trianglePosition);

  @override
  void paint(Canvas canvas, Size size) {
    if (size.isEmpty) {
      return;
    }

    final Color? backgroundColor = decoration.backgroundColor;
    final Color? strokeColor = decoration.strokeColor;
    final double strokeWidth = decoration.strokeWidth;

    if ((backgroundColor == null || backgroundColor == Colors.transparent) &&
        (strokeColor == null ||
            strokeColor == Colors.transparent ||
            strokeWidth <= 0)) {
      return;
    }

    final Path path = Path();

    final double storkOffset = max(0, decoration.strokeWidth / 2);
    final int triangleDirection =
        trianglePosition & TrianglePosition._direction_mask;
    final int triangleAlign = trianglePosition & TrianglePosition._align_mask;
    // 确定内容矩形边界
    final Rect contentRect;
    switch (triangleDirection) {
      case TrianglePosition._left:
        contentRect = Rect.fromLTWH(
          decoration.triangleHeight + 1,
          0,
          size.width - decoration.triangleHeight,
          size.height,
        ).deflate(storkOffset);
      case TrianglePosition._top:
        contentRect = Rect.fromLTWH(
          0,
          decoration.triangleHeight,
          size.width,
          size.height - decoration.triangleHeight,
        ).deflate(storkOffset);
      case TrianglePosition._right:
        contentRect = Rect.fromLTWH(
          0,
          0,
          size.width - decoration.triangleHeight,
          size.height,
        ).deflate(storkOffset);
      case TrianglePosition._bottom:
        contentRect = Rect.fromLTWH(
          0,
          0,
          size.width,
          size.height - decoration.triangleHeight,
        ).deflate(storkOffset);
      default:
        contentRect = Rect.fromLTWH(0, 0, size.width, size.height);
    }

    final double radiusMax = contentRect.shortestSide / 2;
    final double tlRadius = decoration.radius.topLeft.clamp(0, radiusMax);
    final double trRadius = decoration.radius.topRight.clamp(0, radiusMax);
    final double blRadius = decoration.radius.bottomLeft.clamp(0, radiusMax);
    final double brRadius = decoration.radius.bottomRight.clamp(0, radiusMax);

    final double triangleBase;
    switch (triangleDirection) {
      case TrianglePosition._left:
        triangleBase = decoration.triangleBase
            .clamp(0, contentRect.height - tlRadius - blRadius);
      case TrianglePosition._top:
        triangleBase = decoration.triangleBase
            .clamp(0, contentRect.width - trRadius - tlRadius);
      case TrianglePosition._right:
        triangleBase = decoration.triangleBase
            .clamp(0, contentRect.height - trRadius - brRadius);
      case TrianglePosition._bottom:
        triangleBase = decoration.triangleBase
            .clamp(0, contentRect.width - brRadius - blRadius);
      default:
        triangleBase =
            max(0, decoration.triangleBase) - decoration.triangleOffset * 2;
    }
    final double triangleHeight = max(0, decoration.triangleHeight);

    // 控制直线段的坐标, 命名方式 主边 位置 方向
    // ex: lty 左边顶部的y坐标 (这是直线开始的位置)
    final double lty = contentRect.top + tlRadius;
    final double lby = contentRect.bottom - blRadius;
    final double tlx = contentRect.left + tlRadius;
    final double trx = contentRect.right - trRadius;
    final double rty = contentRect.top + trRadius;
    final double rby = contentRect.bottom - brRadius;
    final double blx = contentRect.left + blRadius;
    final double brx = contentRect.right - brRadius;

    // 计算路径
    path.moveTo(tlx, contentRect.top);
    if (triangleDirection == TrianglePosition._top) {
      final double triangleCX;
      switch (triangleAlign) {
        case TrianglePosition._start:
          triangleCX = tlx +
              decoration.triangleOffset
                  .clamp(triangleBase / 2, trx - tlx - triangleBase / 2);
          break;
        case TrianglePosition._center:
          final double tcx = (tlx + trx) / 2;
          triangleCX = tcx +
              decoration.triangleOffset.clamp(
                  tlx - tcx + triangleBase / 2, trx - tcx - triangleBase / 2);
          break;
        case TrianglePosition._end:
          triangleCX = trx +
              decoration.triangleOffset
                  .clamp(tlx - trx + triangleBase / 2, -triangleBase / 2);
          break;
        default:
          triangleCX = (tlx + trx) / 2;
      }
      path.lineTo(triangleCX - triangleBase / 2, contentRect.top);
      path.lineTo(triangleCX, contentRect.top - triangleHeight + storkOffset);
      path.lineTo(triangleCX + triangleBase / 2, contentRect.top);
      path.lineTo(trx, contentRect.top);
    } else {
      path.lineTo(trx, contentRect.top);
    }
    if (trRadius > 0) {
      path.arcTo(
        Rect.fromCircle(center: Offset(trx, rty), radius: trRadius),
        -pi / 2,
        pi / 2,
        false,
      );
    }
    if (triangleDirection == TrianglePosition._right) {
      final double triangleCY;
      switch (triangleAlign) {
        case TrianglePosition._start:
          triangleCY = rty +
              decoration.triangleOffset
                  .clamp(triangleBase / 2, rby - rty - triangleBase / 2);
          break;
        case TrianglePosition._center:
          final double rcy = (rty + rby) / 2;
          triangleCY = rcy +
              decoration.triangleOffset.clamp(
                  rty - rcy + triangleBase / 2, rby - rcy - triangleBase / 2);
          break;
        case TrianglePosition._end:
          triangleCY = rby +
              decoration.triangleOffset
                  .clamp(rty - rby + triangleBase / 2, -triangleBase / 2);
          break;
        default:
          triangleCY = (rty + rby) / 2;
      }
      path.lineTo(contentRect.right, triangleCY - triangleBase / 2);
      path.lineTo(contentRect.right + triangleHeight - storkOffset, triangleCY);
      path.lineTo(contentRect.right, triangleCY + triangleBase / 2);
      path.lineTo(contentRect.right, rby);
    } else {
      path.lineTo(contentRect.right, rby);
    }
    if (brRadius > 0) {
      path.arcTo(
        Rect.fromCircle(center: Offset(brx, rby), radius: brRadius),
        0,
        pi / 2,
        false,
      );
    }
    if (triangleDirection == TrianglePosition._bottom) {
      final double triangleCX;
      switch (triangleAlign) {
        case TrianglePosition._start:
          triangleCX = blx +
              decoration.triangleOffset
                  .clamp(triangleBase / 2, brx - blx - triangleBase / 2);
          break;
        case TrianglePosition._center:
          final double bcx = (blx + brx) / 2;
          triangleCX = bcx +
              decoration.triangleOffset.clamp(
                  blx - bcx + triangleBase / 2, brx - bcx - triangleBase / 2);
          break;
        case TrianglePosition._end:
          triangleCX = brx +
              decoration.triangleOffset
                  .clamp(blx - brx + triangleBase / 2, -triangleBase / 2);
          break;
        default:
          triangleCX = (blx + brx) / 2;
      }
      path.lineTo(triangleCX + triangleBase / 2, contentRect.bottom);
      path.lineTo(
          triangleCX, contentRect.bottom + triangleHeight - storkOffset);
      path.lineTo(triangleCX - triangleBase / 2, contentRect.bottom);
      path.lineTo(blx, contentRect.bottom);
    } else {
      path.lineTo(blx, contentRect.bottom);
    }
    if (blRadius > 0) {
      path.arcTo(
        Rect.fromCircle(center: Offset(blx, lby), radius: blRadius),
        pi / 2,
        pi / 2,
        false,
      );
    }
    if (triangleDirection == TrianglePosition._left) {
      final double triangleCY;
      switch (triangleAlign) {
        case TrianglePosition._start:
          triangleCY = lty +
              decoration.triangleOffset
                  .clamp(triangleBase / 2, lby - lty - triangleBase / 2);
          break;
        case TrianglePosition._center:
          final double lcy = (lty + lby) / 2;
          triangleCY = lcy +
              decoration.triangleOffset.clamp(
                  lty - lcy + triangleBase / 2, lby - lcy - triangleBase / 2);
          break;
        case TrianglePosition._end:
          triangleCY = lby +
              decoration.triangleOffset
                  .clamp(lty - lby + triangleBase / 2, -triangleBase / 2);
          break;
        default:
          triangleCY = (lty + lby) / 2;
      }
      path.lineTo(contentRect.left, triangleCY + triangleBase / 2);
      path.lineTo(contentRect.left - triangleHeight + storkOffset, triangleCY);
      path.lineTo(contentRect.left, triangleCY - triangleBase / 2);
      path.lineTo(contentRect.left, lty);
    } else {
      path.lineTo(contentRect.left, lty);
    }
    if (tlRadius > 0) {
      path.arcTo(
        Rect.fromCircle(center: Offset(tlx, lty), radius: tlRadius),
        pi,
        pi / 2,
        false,
      );
    }
    path.close();

    final Paint paint = Paint();

    if (backgroundColor != null) {
      paint.color = backgroundColor;
      paint.style = PaintingStyle.fill;
      canvas.drawPath(path, paint);
    }

    if (strokeColor != null && strokeWidth >= 0) {
      paint.color = strokeColor;
      paint.style = PaintingStyle.stroke;
      paint.strokeWidth = strokeWidth;
      paint.strokeJoin = StrokeJoin.round;
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _BubblePainter oldDelegate) =>
      oldDelegate.decoration != decoration;
}

/// 气泡widget的装饰
/// [backgroundColor] 背景颜色
/// [radius] 内容区域圆角
/// [triangleBase] 三角形底边
/// [triangleHeight] 三角形高
/// [strokeColor] 边线颜色
/// [strokeWidth] 边线宽度
/// [triangleOffset] 三角形偏移量; 以指定的三角形位置为坐标点原点, 右是x正方向, 下是y正方向 进行偏移
class BubbleDecoration {
  final Color? backgroundColor;
  final ContentRadius radius;
  final double triangleBase;
  final double triangleHeight;
  final Color? strokeColor;
  final double strokeWidth;
  final double triangleOffset;

  const BubbleDecoration(
      {this.backgroundColor,
      this.radius = ContentRadius.zero,
      this.triangleBase = 10,
      this.triangleHeight = 12,
      this.strokeColor,
      this.strokeWidth = 0,
      this.triangleOffset = 0});

  @override
  int get hashCode => Object.hash(
        backgroundColor,
        radius,
        triangleBase,
        triangleHeight,
        strokeColor,
        strokeWidth,
        triangleOffset,
      );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BubbleDecoration &&
        other.backgroundColor == backgroundColor &&
        other.radius == radius &&
        other.triangleBase == triangleBase &&
        other.triangleHeight == triangleHeight &&
        other.strokeColor == strokeColor &&
        other.strokeWidth == strokeWidth &&
        other.triangleOffset == triangleOffset;
  }
}

/// 三角形的位置原点
/// 一个int值; D62 ~ D63 存放位置(开始,中间,结束), D60 ~ D61 存放方向(左,上,右,下)
class TrianglePosition {
  static const int _left = 0x00 << 2;
  static const int _top = 0x01 << 2;
  static const int _right = 0x02 << 2;
  static const int _bottom = 0x03 << 2;

  static const int _start = 0x01;
  static const int _center = 0x2;
  static const int _end = 0x03;

  static const int _direction_mask = 0x03 << 2;
  static const int _align_mask = 0x03;

  static const int topLeft = _top | _start;
  static const int topCenter = _top | _center;
  static const int topRight = _top | _end;
  static const int rightTop = _right | _start;
  static const int rightCenter = _right | _center;
  static const int rightBottom = _right | _end;
  static const int bottomLeft = _bottom | _start;
  static const int bottomCenter = _bottom | _center;
  static const int bottomRight = _bottom | _end;
  static const int leftTop = _left | _start;
  static const int leftCenter = _left | _center;
  static const int leftBottom = _left | _end;
}

class ContentRadius {
  final double topLeft;
  final double topRight;
  final double bottomLeft;
  final double bottomRight;

  const ContentRadius(
      {this.topLeft = 0,
      this.topRight = 0,
      this.bottomLeft = 0,
      this.bottomRight = 0});

  static const zero = ContentRadius();

  static ContentRadius all(double radius) => ContentRadius(
        topLeft: radius,
        topRight: radius,
        bottomLeft: radius,
        bottomRight: radius,
      );
}
