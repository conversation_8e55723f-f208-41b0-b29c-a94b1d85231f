import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/select_record_date_view.dart';
import 'package:gdjg_pure_flutter/generated/assets.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/toast_util.dart';

/// 日期切换控件
class SelectDay extends StatefulWidget {
  final DateTime value;

  final ValueChanged<DateTime> onValueChange;

  const SelectDay({
    super.key,
    required this.value,
    required this.onValueChange,
  });

  @override
  State<SelectDay> createState() => _SelectDayState();
}

class _SelectDayState extends State<SelectDay> {
  late DateTime value;

  @override
  void initState() {
    super.initState();
    setState(() {
      value = widget.value;
    });
  }

  /// 显示月份选择器
  void _showDatePicker() {
    /// 显示月份选择器
    var list = <DateTime>[value];
    YPRoute.openDialog(
      builder: (context) => SelectRecordDateView(
        note_id: '',
        isRecordWorkPoints: true,
        dateList: list,
        isChangeChoice: false,
        isMultiple: false,
        onSelect: (dateList) {
          widget.onValueChange(dateList.first);
          setState(() {
            value = dateList.first;
          });
        },
      ),
      alignment: Alignment.bottomCenter,
    );
  }

  void _changeDay(int delta) {
    DateTime newDate =
        DateTime(value.year, value.month, value.day).add(Duration(days: delta));
    if (delta == 1 && !_isSameOrBeforeCurrentMonth(newDate)) {
      ToastUtil.showToast("明天还没有到哦");
      return;
    }
    setState(() {
      value = newDate;
    });
    widget.onValueChange(newDate);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      width: double.infinity,
      color: const Color(0xFFF0F0F0),
      child: Row(
        children: [
          // 左侧“上一月”按钮
          SizedBox(
            width: 98,
            child: Center(
              child: GestureDetector(
                onTap: () {
                  _changeDay(-1);
                },
                child: Text('前一天', style: _monthButtonTextStyle),
              ),
            ),
          ),

          // 中间区域（带背景图）
          Expanded(
            flex: 1,
            child: GestureDetector(
              onTap: () {
                _showDatePicker();
              },
              child: Container(
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(Assets.groupWaaIcPscaDateBg),
                    fit: BoxFit.fill,
                  ),
                ),
                child: Center(
                  child: _monthDisplayRow(
                      "${value.year}年${value.month}月${value.day}日"),
                ),
              ),
            ),
          ),

          // 右侧按钮
          SizedBox(
            width: 98,
            child: Center(
              child: GestureDetector(
                onTap: () {
                  _changeDay(1);
                },
                child: Text('后一天', style: _monthButtonTextStyle),
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///判断是否到下个月
  bool _isSameOrBeforeCurrentMonth(DateTime date) {
    final now = DateTime.now();
    return date.year < now.year ||
        (date.year == now.year && date.month <= now.month);
  }

  Widget _monthDisplayRow(String monthText) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          monthText,
          style: _monthTextStyle,
        ),
        SizedBox(width: 4), // 更好的间距控制
        Image.asset(
          "assets/images/group/waa_svg_select_time.webp",
          width: 12,
          height: 12,
        ),
      ],
    );
  }

  // --- UI 样式和辅助方法 ---
  TextStyle get _monthTextStyle => const TextStyle(
        fontSize: 16,
        color: Color(0xFF323233),
        fontWeight: FontWeight.bold,
      );

  TextStyle get _monthButtonTextStyle => const TextStyle(
        fontSize: 14,
        color: Color(0xFF323233),
      );
}
