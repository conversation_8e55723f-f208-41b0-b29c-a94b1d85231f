import 'dart:async';
import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar_day.dart';
import 'package:gdjg_pure_flutter/widget/calendar/utils.dart';

class CalendarPagerView extends StatefulWidget {
  final DateTime startDate;
  final DateTime endDate;
  final Widget Function(BuildContext context, DateTime date) itemBuilder;
  final void Function(BuildContext? context, DateTime currentDate)?
      onPageChanged;
  final double viewportFraction;
  final double pageSpacing;
  final DateTime? initialDate;
  PageController? pageController;

  CalendarPagerView(
      {super.key,
      required this.startDate,
      required this.endDate,
      required this.itemBuilder,
      this.onPageChanged,
      this.viewportFraction = 0.9,
      this.pageSpacing = 10,
      this.initialDate,
      this.pageController})
      : assert(viewportFraction > 0 && viewportFraction <= 1),
        assert(
          !endDate.isBefore(startDate),
          'endDate cannot be before startDate',
        );

  @override
  State<CalendarPagerView> createState() => CalendarPagerViewState();
}

class CalendarPagerViewState extends State<CalendarPagerView> {
  late final PageController _pageController;
  late int _currentPage;
  late int _totalPages;
  late List<GlobalKey> _pageKeys;

  // 滑动状态相关变量
  bool _isScrolling = false;
  Timer? _scrollEndTimer;
  int _lastReportedPage = 0;

  @override
  void initState() {
    super.initState();
    _totalPages = _calculateTotalPages();
    _pageKeys = List.generate(_totalPages, (_) => GlobalKey());
    _currentPage = calculatePageIndex(
        initDate: widget.initialDate ?? DateTime.now(),
        startDate: widget.startDate,
        endDate: widget.endDate);
    _pageController = widget.pageController ??
        PageController(
          viewportFraction: widget.viewportFraction,
          initialPage: _currentPage,
        );

    _pageController.addListener(() {
      // 判断是否滚动到整数页面（表示切换完成）
      if (_pageController.page != null && _pageController.page! % 1 == 0) {
        final index = _pageController.page!.round();
        setState(() => _currentPage = index);
        yprint("切换完成，当前页面: ${index}");
        widget.onPageChanged?.call(
          _pageKeys[index].currentContext,
          _dateForIndex(index),
        );
      }
    });
    // 初始加载后计算高度
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onPageChanged?.call(
        _pageKeys[_currentPage].currentContext,
        _dateForIndex(_currentPage),
      );
    });
  }

  int _calculateTotalPages() {
    final yearDiff = widget.endDate.year - widget.startDate.year;
    return yearDiff * 12 + widget.endDate.month - widget.startDate.month + 1;
  }

  DateTime _dateForIndex(int index) {
    final yearOffset = (widget.startDate.month + index - 1) ~/ 12;
    final month = (widget.startDate.month + index - 1) % 12 + 1;
    return DateTime(widget.startDate.year + yearOffset, month);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: PageView.builder(
        controller: _pageController,
        itemCount: _totalPages,
        // onPageChanged: (index) {
        //   setState(() => _currentPage = index);
        //   // widget.onPageChanged?.call(
        //   //   _pageKeys[index].currentContext,
        //   //   _dateForIndex(index),
        //   // );
        // },
        padEnds: false,
        physics: const ClampingScrollPhysics(),
        itemBuilder: (context, index) {
          return SingleChildScrollView(
            // 确保超长内容可滚动
            physics: const NeverScrollableScrollPhysics(),
            child: KeyedSubtree(
              key: _pageKeys[index],
              child: widget.itemBuilder(context, _dateForIndex(index)),
            ),
          );
        },
      ),
    );
  }

  /// 找到今天日期的位置
  /// 这个方法正常工作的前提:
  /// 1: 每日的Widget类型是 CalendarDay
  /// 2: 每日的Widget的 key 类型是 ValueKey< String >(“calendar_day_{year}_{month}_{day}")
  /// 如果今日的日期不在此显示的日历上,返回null
  Rect? findTodayRect() {
    final int? index;
    try {
      index = _pageController.page?.round();
      if (index == null) {
        return null;
      }
    } catch (e) {
      return null;
    }
    final BuildContext? subtreeContext = _pageKeys[index].currentContext;
    if (subtreeContext is! Element) {
      return null;
    }

    final DateTime dateTime = DateTime.now();
    final ValueKey<String> key = ValueKey(
        "calendar_day_${dateTime.year}_${dateTime.month}_${dateTime.day}");

    Element? find(Element element) {
      if (element.widget is CalendarDay) {
        return element.widget.key == key ? element : null;
      } else {
        Element? result;
        element.visitChildElements((child) => result ??= find(child));
        return result;
      }
    }

    final todayElement = find(subtreeContext);
    final renderObject = todayElement?.renderObject;
    if (renderObject is! RenderBox) return null;

    return renderObject.localToGlobal(Offset.zero) & renderObject.size;
  }
}
