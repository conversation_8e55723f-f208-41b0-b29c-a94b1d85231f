import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/widget/calendar/calendar.dart';
import 'package:gdjg_pure_flutter/widget/calendar/utils.dart';

import 'calendar_pager_view.dart';
import 'dayevent/DayEvent.dart';

class DynamicHeightCalendar extends StatefulWidget {
  final ValueChanged<DateTime> onValueChange;

  final Map<DateTime, List<DayEvent>> events;

  final ValueChanged<DateTime> onDayTap;

  final Key? renderBoxKey;

  const DynamicHeightCalendar(
      {super.key,
      this.renderBoxKey,
      required this.onValueChange,
      required this.events,
      required this.onDayTap});

  @override
  State<DynamicHeightCalendar> createState() => DynamicHeightCalendarState();
}

class DynamicHeightCalendarState extends State<DynamicHeightCalendar> {
  DateTime currentDate = DateTime.now();

  BuildContext? _currentContext;

  DateTime startDate = DateTime.now().copyWith(year: 2000, month: 1, day: 1);
  DateTime endDate = DateTime.now();

  late PageController _pageController;
  bool _disposed = false;

  final ValueNotifier<double> _currentHeight = ValueNotifier<double>(200);

  final GlobalKey _pagedCalendarKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _pageController = PageController(
      initialPage: calculatePageIndex(
        initDate: currentDate,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }

  @override
  void didUpdateWidget(covariant DynamicHeightCalendar oldWidget) {
    super.didUpdateWidget(oldWidget);
    updateHeight();
  }

  @override
  void dispose() {
    if (!_disposed) {
      _disposed = true;
      _currentHeight.dispose();
      _pageController.dispose();
      super.dispose();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_disposed) {
      return const SliverToBoxAdapter(child: SizedBox.shrink());
    }

    return ValueListenableBuilder<double>(
      valueListenable: _currentHeight,
      builder: (context, height, _) {
        return SliverToBoxAdapter(
          child: SizedBox(
            key: widget.renderBoxKey,
            height: height,
            child: CalendarPagerView(
              key: _pagedCalendarKey,
              onPageChanged: (context, date) {
                _currentContext = context;
                updateHeight();
                widget.onValueChange(date);
              },
              viewportFraction: 1,
              initialDate: currentDate,
              pageController: _pageController,
              startDate: DateTime.now().copyWith(year: 2000),
              endDate: DateTime.now(),
              itemBuilder: (context, cur) {
                return FullEventCalendar(
                  onTap: widget.onDayTap,
                  currentDate: cur,
                  events: widget.events,
                );
              },
            ),
          ),
        );
      },
    );
  }

  // 关键方法：更新当前高度
  void updateHeight() {
    if (_disposed) return;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_disposed &&
          _currentContext != null &&
          _currentContext!.mounted &&
          mounted) {
        final renderBox = _currentContext!.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          renderBox.markNeedsLayout();
          final newHeight = renderBox.size.height;
          if (_currentHeight.value != newHeight) {
            _currentHeight.value = newHeight;
          }
        }
      }
    });
  }

  // 切换月份
  void changeMonth(DateTime date) {
    if (_disposed || !mounted) return;
    setState(() {
      currentDate = date;
      final index = calculatePageIndex(
        initDate: date,
        startDate: startDate,
        endDate: endDate,
      );
      debugPrint('Date:${date},Index:$index');
      if (_pageController.hasClients) {
        _pageController.animateToPage(
          index,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  Rect? findTodayRect() {
    final State? s = _pagedCalendarKey.currentState;
    if (s is! CalendarPagerViewState) {
      return null;
    } else if (s.mounted) {
      return s.findTodayRect();
    } else {
      return null;
    }
  }
}
