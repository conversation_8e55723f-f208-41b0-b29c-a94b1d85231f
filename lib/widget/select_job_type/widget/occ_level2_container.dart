import 'package:flutter/material.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/colors_util.dart';
import 'package:gdjg_pure_flutter/widget/select_job_type/occupation_entity.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';

import 'occ_level2_item.dart';

class OccLevel2Container extends StatelessWidget {
  final List<OccupationEntity> occList;
  final OccupationSelectEntity? value;
  final ItemScrollController? scrollController;

  //点击事件处理
  final Function(OccupationEntity) onTap;

  const OccLevel2Container(
      {super.key,
      required this.occList,
      required this.onTap,
      this.scrollController,
      this.value});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: 245,
      child: Container(
        color: ColorsUtil.ypBgColor,
        child: ScrollablePositionedList.builder(
          itemScrollController: scrollController,
          itemBuilder: (context, index) {
            final item = occList[index];
            return OccLevel2Item(
              key: ValueKey(item.id),
              entity: item,
              isSelected: item.id == value?.level2?.id && item.pid == value?.level2?.pid,
              onTap: onTap,
            );
          },
          itemCount: occList.length,
        ),
      ),
    );
  }
}
