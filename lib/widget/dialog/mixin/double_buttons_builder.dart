import 'package:flutter/cupertino.dart';

import 'builder_units.dart';

/// [ENV] 点击事件的回调可以向外传递一个环境信息
/// [ENV] 一般会是构建的弹窗; 特殊的也可以是其它点击按钮后需要返回的数据等
/// 这在具体的业务才能确定; 按钮的点击事件设置延迟到实现的逻辑中处理
mixin DoubleButtonsBuilder<ENV> {
  Widget? _startButton;
  Widget? _endButton;
  void Function(ENV)? _onStartTapCallback =
      BuilderUnits.onNegativeButtonTapDefault;
  void Function(ENV)? _onEndTapCallback;

  Widget? get startButton => _startButton;

  Widget? get endButton => _endButton;

  void Function(ENV)? get onStartTapCallback => _onStartTapCallback;

  void Function(ENV)? get onEndTapCallback => _onEndTapCallback;

  void setStartButtonByText(
    String? text, {
    TextStyle? style = BuilderUnits.undefinedTextStyle,
    BoxDecoration? decoration = BuilderUnits.undefinedDecoration,
  }) =>
      setStartButtonByWidget(
        BuilderUnits.createTextByText(
          text,
          style,
          BuilderUnits.negativeTextStyle,
        ),
        decoration: decoration,
      );

  void setStartButtonBySpans(
    List<TextSpan>? spans, {
    BoxDecoration? decoration = BuilderUnits.undefinedDecoration,
  }) =>
      setStartButtonByWidget(
        BuilderUnits.createTextBySpans(spans),
        decoration: decoration,
      );

  void setStartButtonByWidget(
    Text? widget, {
    BoxDecoration? decoration = BuilderUnits.undefinedDecoration,
  }) =>
      _startButton = BuilderUnits.createDecoratedWidget(
        widget,
        decoration,
        null,
      );

  void setOnStartTapCallback(void Function(ENV)? callback) {
    _onStartTapCallback = callback;
  }

  void setEndButtonByText(
    String? text, {
    TextStyle? style = BuilderUnits.undefinedTextStyle,
    BoxDecoration? decoration = BuilderUnits.undefinedDecoration,
  }) =>
      setEndButtonByWidget(
        BuilderUnits.createTextByText(
          text,
          style,
          BuilderUnits.positiveTextStyle,
        ),
        decoration: decoration,
      );

  void setEndButtonBySpans(
    List<TextSpan>? spans, {
    BoxDecoration? decoration,
  }) =>
      setEndButtonByWidget(
        BuilderUnits.createTextBySpans(spans),
        decoration: decoration,
      );

  void setEndButtonByWidget(
    Text? widget, {
    BoxDecoration? decoration = BuilderUnits.undefinedDecoration,
  }) =>
      _endButton = BuilderUnits.createDecoratedWidget(
        widget,
        decoration,
        null,
      );

  void setOnEndTapCallback(void Function(ENV)? callback) {
    _onEndTapCallback = callback;
  }
}
