

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:gdjg_pure_flutter/data/fee_standard_data/repo/model/fee_standard_biz_model.dart';
import 'package:gdjg_pure_flutter/feature/group/common_params_model/worker_model.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/image_selector_and_remark_view/image_selector_and_remark_controller.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/widget/image_selector_and_remark_view/image_selector_and_remark_view.dart';
import 'package:gdjg_pure_flutter/iconfont/iconfont.dart';
import 'package:gdjg_pure_flutter/model/RecordNoteType.dart';
import 'package:gdjg_pure_flutter/model/RecordType.dart';
import 'package:gdjg_pure_flutter/utils/system_util/font_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/string_util.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/utils/ui_util/widget_util.dart';
import 'package:gdjg_pure_flutter/widget/dashed_vertical_divider.dart';
import 'package:gdjg_pure_flutter/widget/point_work/entity/record_workpoints_model.dart';
import 'package:gdjg_pure_flutter/widget/point_work/point_work_controller.dart';
import 'package:gdjg_pure_flutter/widget/point_work/vm/point_work_vm.dart';


class PointWorkView extends StatefulWidget {

  final PointWorkController controller;
  final ImageSelectorAndRemarkController imageSelectorAndRemarkController;
  // 账本id
  final String noteId;
  // 账目名称
  final String workNoteName;
  // 记工类型  点工 RwaRecordType.workDays ， 包工RwaRecordType.packageWork
  final RwaRecordType businessType;
  // 班组(RecordNoteType.group)/个人(RecordNoteType.personal)
  final RecordNoteType recordNoteType;
  // worker
  final WorkerModel? worker;
  // 是否是修改
  final bool isModify;
  // 工资规则
  final FeeStandardBizModel? feeStandardModel;
  // 是否律师提醒
  final bool isShowRemind;

  const PointWorkView({super.key,
    required this.controller,
    required this.imageSelectorAndRemarkController,
    required this.noteId,
    required this.workNoteName,
    required this.businessType,
    required this.recordNoteType,
    required this.isModify,
    this.isShowRemind = true,
    this.worker,
    this.feeStandardModel});

  @override
  PointWorkViewState createState() => PointWorkViewState();
}
class PointWorkViewState extends State<PointWorkView> {

  late PointWorkVM vm;

  final GlobalKey _worktimeKey = GlobalKey();
  final GlobalKey _overtimeKey = GlobalKey();


  @override
  void initState() {
    super.initState();
    vm = PointWorkVM(widget.controller, widget.noteId, widget.workNoteName, widget.businessType, widget.recordNoteType,widget.isModify, widget.worker);
    // widget.controller.addListener(_handleWagesChange);
  }

  // void _handleWagesChange() {
  //   setState(() {}); // 当Controller值变化时触发重建
  // }

  @override
  void dispose() {
    super.dispose();
    // widget.controller?.removeListener();
  }

  // 工资显示区域
  Widget _buildSalarySection() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      alignment: Alignment.center,
      constraints: BoxConstraints(minHeight: 60.h),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '工资：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF000000),
                ),
              ),
              Flexible(
                flex: 1,
                  child: GestureDetector(
                    onTap: () {
                      vm.showSalaryCalculationDialog();
                    },
                    child: Container(
                      width: double.infinity,
                      color: Colors.transparent,
                      child: Row(
                        children: [
                          Text(
                            // '${_vvm.pointUS.hasFeeStandardId ? _vvm.pointUS.money : '设置工价'}',
                            vm.getFeeStandardMoney(),
                            style: TextStyle(
                              fontSize: vm.isHasFeeStandard() ? 30.sp : 16.sp,
                              color: vm.isHasFeeStandard()
                                  ? const Color.fromRGBO(255, 160, 17, 1)
                                  : const Color(0xFFCCCCCC),
                              fontFamily: FontUtil.fontCondMedium,
                              // fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Container(
                            child: Row(
                              children: [
                                Text(
                                  vm.getFeeRuleText(),
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: const Color(0xFF999999),
                                  ),
                                ),
                                SizedBox(width: 4),
                                if (vm.isHasFeeStandard()) ...[
                                  GestureDetector(
                                      onTap: () {
                                        vm.deleteFeeStandard();
                                      },
                                      child: IconFont(IconNames.saasClose, size: 18.w)),
                                ] else ...[
                                  IconFont(IconNames.saasArrowRight, size: 18.w),
                                ],
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  )
              ),
            ],
          ),
        ],
      ),
    );
  }


  @override
  Widget build(BuildContext context) {
    vm.setNoteId(widget.noteId.trimTrailingZeros());
    vm.setWorkNoteName(widget.workNoteName);
    vm.setWorkers(widget.worker);
    vm.updateFeeStandardModel(widget.feeStandardModel);
    return Container(
      width: double.infinity,
      child:
      ValueListenableBuilder<RecordWorkpointsModel>(
        valueListenable: vm.controller,
        builder: (context, model, child) {
          return Column(
            children: [
              // 上班状态选择区域
              _buildWorkStatusSection(),
              // 加班选择区域
              _buildOvertimeSection(),
              // 工资区域
              Visibility(
                visible:vm.isShowFeeStandard(),
                  child: _buildSalarySection()
              ),
              _buildSelectPhotoAndRemark()
            ],

          );
        },

      ),
    );
  }

  /// 加班选择区域
  Widget _buildOvertimeSection() {
    return Container(
      key: _overtimeKey,
      width: double.infinity,
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 15.h),
      color: Colors.white,
      child: Row(
        children: [
          Text(
            '加班：',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF000000),
            ),
          ),
          _buildOvertimeButton(),
        ],
      ),
    );
  }

  ///加班按钮
  Widget _buildOvertimeButton() {
    return GestureDetector(
      onTap: () {
        vm.showOvertimeDialog(context);
      },
      child: Container(
        height: 34.h,
        constraints: BoxConstraints(minWidth: 88.w),
        padding: EdgeInsets.symmetric(horizontal: 6.w),
        decoration: BoxDecoration(
          color: vm.hasOvertime()
              ? const Color(0xFF5290FD)
              : const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(3.r),
        ),
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                vm.getOvertimeText(),
                style: TextStyle(
                  fontSize: 14.sp,
                  color: vm.hasOvertime()
                      ? Colors.white
                      : const Color(0xFF000000),
                ),
              ),
              SizedBox(width: 6.w),
              DashedVerticalDivider(
                height: 14.w, // 虚线总高度
                dashHeight: 1, // 每段虚线长度
                dashWidth: 1, // 虚线间隔
                color: vm.hasOvertime()?Colors.white:const Color(0xFFCCCCCC),
              ),
              _buildOvertimeIcon(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOvertimeIcon() {
    if (vm.hasOvertime()) {
      return GestureDetector(
        onTap: () {
          vm.onDeleteOverTime();
        },
        child: Container(
          height: double.infinity,
          padding: EdgeInsets.only(left: 6.w),
          child: Center(
            child: IconFont(
              IconNames.saasClose,
              size: 15,
              color: "#FFFFFF",
            ),
          ),
        ),
      );
    }
    return Row(
      children: [
        SizedBox(width: 6.w),
        IconFont(
          IconNames.saasEditPen,
          size: 14,
        )
      ],
    );
  }



  /// 上班状态选择区域
  Widget _buildWorkStatusSection() {
    return Container(
      key: _worktimeKey,
      width: double.infinity,
      padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '上班：',
                style: TextStyle(
                  fontSize: 16.sp,
                  color: const Color(0xFF000000),
                ),
              ),
              Expanded(
                child: Row(
                  spacing: 5.w,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      constraints: BoxConstraints(minWidth: 88.w),
                      child: _buildWorkTimeButton(
                          0, vm.getWorkTimeBtnText()),
                    ),
                    Expanded(
                      child: _buildWorkTimeButton(
                          1, vm.getWorkHalfBtnText()),
                    ),
                    Expanded(
                      child: _buildWorkTimeButton(
                          2, vm.getWorkTimeHourBtnText()),
                    ),
                    _buildWorkTimeButton(
                        3, '休息',
                        padding: EdgeInsets.symmetric(horizontal: 12.w)),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              SizedBox(width: 48.w),
              Container(
                constraints: BoxConstraints(minWidth: 88.w),
                child: _buildMorningAndAfternoonButton(),
              ),
              const Spacer(flex: 6),
            ],
          ),
        ],
      ),

    );
  }


  /// 工作时长按钮
  Widget _buildMorningAndAfternoonButton() {
    final isSelected = vm.isSelectTypeIndex()== 4;

    return GestureDetector(
      onTap: () {
        vm.showTimeSlotDialog(context);
      },
      child: Container(
        height: 34.h,
        padding: EdgeInsets.symmetric(horizontal: 6.w),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF5290FD) : const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(3.r),
        ),
        child: Center(
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                vm.getMorningAndAfternoonTextBtnText(),
                style: TextStyle(
                  fontSize: 14.sp,
                  color:
                  isSelected ? Colors.white : const Color(0xFF000000),
                ),
                maxLines: 1,
              ),
              SizedBox(width: 6.w),
              Visibility(
                visible: isSelected,
                  child: Row(
                children: [

                  DashedVerticalDivider(
                    height: 14.w, // 虚线总高度
                    dashHeight: 1, // 每段虚线长度
                    dashWidth: 1, // 虚线间隔
                    color: Colors.white,
                  ),
                  GestureDetector(
                    onTap: () {
                      vm.clearMorningAndAfternoon();
                    },
                    child: Container(
                      height: double.infinity,
                      padding: EdgeInsets.only(left: 6.w
                      ),
                      child: Center(
                        child:
                        IconFont(
                          IconNames.saasClose,
                          size: 14,
                          color: "#FFFFFF",
                        ),
                      ),
                    ),
                  ),
                ],
              ))

            ],
          ),
        ),
    ),
    );
  }

  /// 工作时长按钮
  Widget _buildWorkTimeButton(int index, String text,
      {EdgeInsets padding = const EdgeInsets.all(0)}) {
    final isSelected = vm.isSelectTypeIndex()== index;
    return GestureDetector(
      onTap: () {
        if (index == 0) {
          vm.showWorkHoursKeyboard(context);
        } else if (index == 1) {
          vm.onHalfTimeTap();
        } else if (index == 2) {
          vm.showHourSelectionDialog(context);
        } else if (index == 3) {
          vm.onRestTap();
        } else if (index == 4) {
          vm.showTimeSlotDialog(context);
        }
      },
      child: Container(
        height: 34.h,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF5290FD) : const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(3.r),
        ),
        child: Center(
          child: index == 0
              ? Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                text,
                style: TextStyle(
                  fontSize: 14.sp,
                  color:
                  isSelected ? Colors.white : const Color(0xFF000000),
                ),
                maxLines: 1,
              ),
              SizedBox(width: 6.w),
              DashedVerticalDivider(
                height: 14.w, // 虚线总高度
                dashHeight: 1, // 每段虚线长度
                dashWidth: 1, // 虚线间隔
                color: index == 0?Colors.white:const Color(0xFFCCCCCC),
              ),
              SizedBox(width: 6.w),
              IconFont(
                IconNames.saasEditPen,
                size: 14,
                color: isSelected ? "#FFFFFF" : "#000000",
              ),
            ],
          )
              : Padding(
            padding: padding,
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14.sp,
                color:
                isSelected ? Colors.white : const Color(0xFF000000),
              ),
              maxLines: 1,
            ),
          ),
        ),
      ),
    );
  }

  /// 选择照片和备注
  Widget _buildSelectPhotoAndRemark() {
    return ImageSelectorAndRemarkView(
      isShowRemind: widget.isShowRemind,
      defRemark: '11111',
      defImageUrls: [
        'https://static-test-public.cdqlkj.cn/r/4614/103/pb/p/20250912/d8bd83ec04024cb592000835acd6363a.png'
      ],
      controller: widget.imageSelectorAndRemarkController,
    );
  }

  Rect? findTimeRect() =>
      WidgetUtils.calculateBounding([_worktimeKey, _overtimeKey]);

}