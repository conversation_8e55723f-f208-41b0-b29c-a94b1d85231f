
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/worker_account_record_repo.dart';
import 'package:gdjg_pure_flutter/utils/system_util/yprint.dart';
import 'package:gdjg_pure_flutter/widget/expense_view/expense_controller.dart';

class ExpenseVM {


  final _recordRepo = WorkerAccountRecordRepo();

  final ExpenseController controller;
  String noteId;


  ExpenseVM(this.controller, this.noteId) {
    _getLastExpenses();
  }

  setNoteId(String noteId){
    this.noteId = noteId;
  }
  _getLastExpenses() async {
    var result = await _recordRepo.getLastExpenses(noteId);
    if (result.isOK()) {
      var lastExpenses = result.getSucData();
      if(lastExpenses != null){
        yprint(lastExpenses.toString());
        controller.updateLastExpenses(lastExpenses);
      }
    }
  }

  bool isHasExpense(){
    return getExpenseName().isNotEmpty;

  }


  String getExpenseName(){
    return controller.value.expenseModel?.name ?? '';

  }

  void setMoney(String value){
    controller.updateMoney(value);
  }

}