import 'package:flutter/cupertino.dart';
import 'package:gdjg_pure_flutter/feature/account/login/last_login/last_login_page.dart';
import 'package:gdjg_pure_flutter/feature/account/role/select_role_page.dart';
import 'package:gdjg_pure_flutter/feature/account/transfer/transfer_work_page.dart';
import 'package:gdjg_pure_flutter/feature/account/vip/buy_vip_page.dart';
import 'package:gdjg_pure_flutter/feature/account_manage/account_manage_page.dart';
import 'package:gdjg_pure_flutter/feature/account_manage/change_bind_phone/change_bind_phone_page.dart';
import 'package:gdjg_pure_flutter/feature/account_manage/history_phone_page/history_phone_page.dart';
import 'package:gdjg_pure_flutter/feature/account_manage/switch_account_page/switch_account_page.dart';
import 'package:gdjg_pure_flutter/feature/ad_interstitial_test.dart';
import 'package:gdjg_pure_flutter/feature/bill_flow/page/worker_account_record_page.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/export_record/export_record_page.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/choose_project_filter/choose_project_filter_page.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/group_cloud_album/group_cloud_album_page.dart';
import 'package:gdjg_pure_flutter/feature/cloud_album/group/image_viewer/image_viewer_page.dart';
import 'package:gdjg_pure_flutter/feature/common_page/change_project_name/change_project_name_page.dart';
import 'package:gdjg_pure_flutter/feature/common_page/notes/notes_page.dart';
import 'package:gdjg_pure_flutter/feature/common_page/personal_record_work/personal_record_work_page.dart';
import 'package:gdjg_pure_flutter/feature/common_page/photo_viewer/photo_viewer_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/my_routeInterceptor.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/name_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/setting_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/third_page.dart';
import 'package:gdjg_pure_flutter/feature/demo_setting_feature/page/version_page.dart';
import 'package:gdjg_pure_flutter/feature/group/common_page/select_type_page/select_type_page.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/change_log/change_log_page.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/change_weather/change_weather_page.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/construction_log_page.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/export_log_list/export_log_list_page.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/log_content/log_content_page.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/search_log/search_log_page.dart';
import 'package:gdjg_pure_flutter/feature/group/construction_log/select_export_time/select_export_time_page.dart';
import 'package:gdjg_pure_flutter/feature/group/contact/contact_page.dart';
import 'package:gdjg_pure_flutter/feature/group/edit_worker_info/edit_worker_info_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_account_work/group_account_work_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_record_work/group_edit_record_work_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_edit_wage/group_edit_wage_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated/group_liquidated_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_liquidated_detail/group_liquidated_detail_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_bill/group_pro_bill_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_calendar/group_pro_calendar_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_pro_statistics/group_pro_statistics_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_record_work/group_record_work_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_setting/group_project_setting_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project/settled_page/settled_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/daily_rate/daily_rate_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/download_workers_material/download_workers_material_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/on_site_workers/mass_operation/page/mass_operation_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/on_site_workers/on_site_workers_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/shift_lead_setup/shift_lead_delete/shift_lead_delete_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/shift_lead_setup/shift_lead_info/shift_lead_info_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_project_info/shift_lead_setup/shift_lead_setup_page.dart';
import 'package:gdjg_pure_flutter/feature/group/group_settlement/group_settlement_page.dart';
import 'package:gdjg_pure_flutter/feature/group/page/invite_worker_page.dart';
import 'package:gdjg_pure_flutter/feature/group/page/worker_resume_page.dart';
import 'package:gdjg_pure_flutter/feature/group/phone_contact/phone_contact_page.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_selector/worker_selector_page.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_setting/worker_setting_page.dart';
import 'package:gdjg_pure_flutter/feature/group/worker_manager/worker_tel_modify/modify_worker_tel_page.dart';
import 'package:gdjg_pure_flutter/feature/media_viewer/media_viewer_page.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/accredit_project/detail/accredit_project_detail.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/accredit_project/list/my_accredit_project_list.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/bank_card/list/bank_card_manager_page.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/bank_card/modify/bank_card_modify_page.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/home/<USER>';
import 'package:gdjg_pure_flutter/feature/modify_data/id_card/id_card_info_page.dart';
import 'package:gdjg_pure_flutter/feature/modify_data/phone/bind_phone_page.dart';
import 'package:gdjg_pure_flutter/feature/test/page/jg_test_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/attendance/page/worker_attendance_sheet_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/change_project/change_project_page.dart';
import 'package:gdjg_pure_flutter/feature/transfer_record_work/transfer_record_work_page.dart';
import 'package:gdjg_pure_flutter/feature/visitor/page/visitor_main_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/change_record_work/change_record_work_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/crosscheck/page/worker_worklog_crosscheck_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/expense_choose/expense_choose_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/invite_join/invite_join_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/invite_join/invite_name/invite_name_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_detail/worker_project_detail/worker_project_detail_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_setup/my_created_project_setup/my_created_project_setup_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/project_setup/my_participated_project_setup/my_participated_project_setup_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/qr_scan/qr_scan_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/qr_scan/what_scan/what_scan_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/record_workpoints/personal_record_workpoints_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/add_subitem/add_subitem_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/add_subitem_unit/add_subitem_unit_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/subitem/subitem_choose/subitem_choose_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_calendar/daily_flow/daily_flow_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/batch_delete/batch_delete_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_flow/choose_project/choose_project_page.dart';
import 'package:gdjg_pure_flutter/feature/worker/worker_project_unsettled/worker_project_unsettled_page.dart';
import 'package:gdjg_pure_flutter/feature/yupao_news/yu_pao_news_page.dart';
import 'package:gdjg_pure_flutter/utils/route_util/route_api/yp_route.dart';
import 'package:gdjg_pure_flutter/utils/web_util/web_page.dart';
import 'package:get/get.dart';

import '../feature/account/login/code_login/login_page.dart';
import '../feature/feedback/feedback_page.dart';
import '../feature/income_expenses/add_annual_income_spend_page.dart';
import '../feature/income_expenses/annual_income_add_type_page.dart';
import '../feature/income_expenses/annual_income_manage_type_page.dart';
import '../feature/income_expenses/annual_income_spend_page.dart';
import '../feature/splash/splash_page.dart';
import '../feature/tabbar/main_page.dart';
import '../feature/worker/attendance/page/worker_attendance_download_page.dart';
import '../feature/worker/crosscheck/page/worker_worklog_flow_details_page.dart';
import '../feature/worker/trash/page/worker_worklog_trash_page.dart';
import '../utils/route_util/route_core/yp_page_route.dart';

//各个独立的业务域自己添加对应的方法和路由
void initRoute() {
  _initRouteInterceptor();
  _initMainRoute();
  _initSettingRoutes();
  _initAccountRoutes();
}

Map<String, WidgetBuilder> getInitRouteMap() {
  Map<String, WidgetBuilder> initRoute = {};
  initRoute.addIf(true, appInitRoute.routeName, appInitRoute.widgetBuilder);
  return initRoute;
}

getInitRouteName() {
  return appInitRoute.routeName;
}

YPPageRoute appInitRoute = YPPageRoute(
  routeName: RouteNameCollection.splash,
  widgetBuilder: (context) => const SplashPage(),
);

void _initMainRoute() {
  YPRoute.registerRoute(appInitRoute);
}

void _initSettingRoutes() {
  YPRoute.registerRoutes([
    YPPageRoute(
      routeName: RouteNameCollection.splash,
      widgetBuilder: (context) => const SplashPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.main,
      widgetBuilder: (context) => const MainPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.setting,
      widgetBuilder: (context) => SettingPage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.name,
        widgetBuilder: (context) => NamePage(),
        isSingleTask: true),
    YPPageRoute(
      routeName: RouteNameCollection.version,
      widgetBuilder: (context) => VersionPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.test,
      widgetBuilder: (context) => JGTestPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.personalRecordWorkPoints,
      widgetBuilder: (context) => PersonalRecordWorkPoints(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.buyVipPage,
      widgetBuilder: (context) => BuyVipPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.changeProjectPage,
      widgetBuilder: (context) => ChangeProjectPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.third,
      widgetBuilder: (context) => ThirdPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.mediaViewer,
      widgetBuilder: (context) => MediaViewerPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.batchDelete,
      widgetBuilder: (context) => BatchDeletePage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.chooseProject,
      widgetBuilder: (context) => ChooseProjectPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.chooseProjectFilter,
      widgetBuilder: (context) => ChooseProjectFilterPage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.personalRecordWork,
        widgetBuilder: (context) => PersonalRecordWorkPage()),
    YPPageRoute(
      routeName: RouteNameCollection.changeProjectName,
      widgetBuilder: (context) => ChangeProjectNamePage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.dailyFlow,
        widgetBuilder: (context) => DailyFlowPage()),
    YPPageRoute(
        routeName: RouteNameCollection.myCreateProjectDetail,
        widgetBuilder: (context) => WorkerProjectDetailPage()),
    YPPageRoute(
      routeName: RouteNameCollection.visitor,
      widgetBuilder: (context) => VisitorMainPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.workerWorklogTrash,
      widgetBuilder: (context) => WorkerWorklogTrashPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.workerWorklogCrosscheck,
      widgetBuilder: (context) => WorkerWorklogCrosscheckPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.workerWorklogFlowDetails,
      widgetBuilder: (context) => WorkerWorklogFlowDetailsPage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.workerAttendanceSheet,
        widgetBuilder: (context) => WorkerAttendanceSheetPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerAttendanceDownload,
        widgetBuilder: (context) => WorkerAttendanceDownloadPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerProjectUnsettled,
        widgetBuilder: (context) => WorkerProjectUnsettledPage()),
    YPPageRoute(
        routeName: RouteNameCollection.expenseChoose,
        widgetBuilder: (context) => ExpenseChoosePage()),
    YPPageRoute(
        routeName: RouteNameCollection.subitemChoose,
        widgetBuilder: (context) => SubitemChoosePage()),
    YPPageRoute(
        routeName: RouteNameCollection.addSubitem,
        widgetBuilder: (context) => AddSubitemPage()),
    YPPageRoute(
        routeName: RouteNameCollection.addSubitemUnit,
        widgetBuilder: (context) => AddSubitemUnitPage()),
    YPPageRoute(
        routeName: RouteNameCollection.changePersonalRecordWork,
        widgetBuilder: (context) => ChangeRecordWorkPage()),
    /*************************** 公用页面在这下边 ****************************/
    YPPageRoute(
      routeName: RouteNameCollection.selectRecordType,
      widgetBuilder: (context) => SelectTypePage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.notes,
      widgetBuilder: (context) => NotesPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.transferRecord,
      widgetBuilder: (context) => TransferRecordWorkPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.exportRecord,
      widgetBuilder: (context) => ExportRecordPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.yuPaoNews,
      widgetBuilder: (context) => YuPaoNewsPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.feedback,
      widgetBuilder: (context) => FeedbackPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.accountManage,
      widgetBuilder: (context) => AccountManagePage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.changeBindPhone,
      widgetBuilder: (context) => ChangeBindPhonePage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.historyPhone,
      widgetBuilder: (context) => HistoryPhonePage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.switchAccount,
      widgetBuilder: (context) => SwitchAccountPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.editWorkerInfo,
      widgetBuilder: (context) => EditWorkerInfoPage(),
    ),
    /*************************** 公用页面在这上边 ****************************/

    /*************************** 班组的在这下边 ****************************/

    YPPageRoute(
      routeName: RouteNameCollection.groupProCalendar,
      widgetBuilder: (context) => GroupProCalendarPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProStatistics,
      widgetBuilder: (context) => GroupProStatisticsPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProBill,
      widgetBuilder: (context) => GroupProBillPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.constructionLog,
      widgetBuilder: (context) => ConstructionLogPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.changeLog,
      widgetBuilder: (context) => ChangeLogPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.changeWeather,
      widgetBuilder: (context) => ChangeWeatherPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.searchLog,
      widgetBuilder: (context) => SearchLogPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.logContentEdit,
      widgetBuilder: (context) => LogContentEditPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.selectExportTime,
      widgetBuilder: (context) => SelectExportTimePage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.exportLogList,
      widgetBuilder: (context) => ExportLogListPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProEditRecordWork,
      widgetBuilder: (context) => GroupEditRecordWorkPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.contact,
      widgetBuilder: (context) => ContactPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.liquidated,
      widgetBuilder: (context) => GroupLiquidatedPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.liquidatedDetail,
      widgetBuilder: (context) => GroupLiquidatedDetailPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupSettlement,
      widgetBuilder: (context) => GroupSettlementPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.settledPage,
      widgetBuilder: (context) => SettledPage(),
    ),
    YPPageRoute(
      routeName: RouteNameCollection.groupProEditWage,
      widgetBuilder: (context) => GroupEditWagePage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.inviteWorker,
        widgetBuilder: (context) => InviteWorkerPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerSetting,
        widgetBuilder: (context) => WorkerSettingPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerSelector,
        widgetBuilder: (context) => WorkerSelectorPage()),
    YPPageRoute(
        routeName: RouteNameCollection.workerResume,
        widgetBuilder: (context) => WorkerResumePage()),
    YPPageRoute(
        routeName: RouteNameCollection.modifyWorkerTel,
        widgetBuilder: (context) => ModifyWorkerTelPage()),
    YPPageRoute(
        routeName: RouteNameCollection.modifyWorkerTel,
        widgetBuilder: (context) => ModifyWorkerTelPage()),
    YPPageRoute(
        routeName: RouteNameCollection.phoneContact,
        widgetBuilder: (context) => PhoneContactPage()),
    YPPageRoute(
        routeName: RouteNameCollection.groupRecordWork,
        widgetBuilder: (context) => GroupRecordWorkPage()),
    YPPageRoute(
        routeName: RouteNameCollection.groupAccountWork,
        widgetBuilder: (context) => GroupAccountWorkPage()),
    YPPageRoute(
      routeName: RouteNameCollection.contact,
      widgetBuilder: (context) => ContactPage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.onSiteWorkers,
        widgetBuilder: (context) => OnSiteWorkersPage()),
    YPPageRoute(
        routeName: RouteNameCollection.massLeave,
        widgetBuilder: (context) => MassOperationPage()),
    YPPageRoute(
        routeName: RouteNameCollection.downloadWorkersMaterial,
        widgetBuilder: (context) => DownloadWorkersMaterialPage()),
    YPPageRoute(
        routeName: RouteNameCollection.shiftLeadSetup,
        widgetBuilder: (context) => const ShiftLeadSetupPage()),
    YPPageRoute(
        routeName: RouteNameCollection.shiftLeadDelete,
        widgetBuilder: (context) => const ShiftLeadDeletePage()),
    YPPageRoute(
        routeName: RouteNameCollection.shiftLeadInfo,
        widgetBuilder: (context) => const ShiftLeadInfoPage()),
    YPPageRoute(
        routeName: RouteNameCollection.dailyRate,
        widgetBuilder: (context) => const DailyRatePage()),
    // 班组-项目设置
    YPPageRoute(
      routeName: RouteNameCollection.groupProjectSetting,
      widgetBuilder: (context) => const GroupProjectSettingPage(),
    ),
    YPPageRoute(
        routeName: RouteNameCollection.projectSetup,
        widgetBuilder: (context) => MyCreatedProjectSetupPage()),
    YPPageRoute(
        routeName: RouteNameCollection.participatedProjectSetup,
        widgetBuilder: (context) => MyParticipatedProjectSetupPage()),
    YPPageRoute(
        routeName: RouteNameCollection.qrScan,
        widgetBuilder: (context) => QRScanPage()),
    YPPageRoute(
        routeName: RouteNameCollection.qrScan,
        widgetBuilder: (context) => QRScanPage()),
    YPPageRoute(
        routeName: RouteNameCollection.inviteJoin,
        widgetBuilder: (context) => InviteJoinPage()),
    YPPageRoute(
        routeName: RouteNameCollection.inviteJoin,
        widgetBuilder: (context) => InviteJoinPage()),
    YPPageRoute(
        routeName: RouteNameCollection.inviteName,
        widgetBuilder: (context) => InviteNamePage()),
    YPPageRoute(
        routeName: RouteNameCollection.whatScan,
        widgetBuilder: (context) => WhatScanPage()),
    /*************************** 班组的在这上边 ****************************/
    YPPageRoute(
        routeName: RouteNameCollection.personAccountRecord,
        widgetBuilder: (context) => WorkerAccountRecordPage()),
    /*************************** 全年收入支相关 ****************************/
    //全年收支-列表
    YPPageRoute(
        routeName: RouteNameCollection.annualIncomeSpendPage,
        widgetBuilder: (context) => AnnualIncomeSpendPage()),
    //全年收支-添加日常收支
    YPPageRoute(
        routeName: RouteNameCollection.addAnnualIncomeSpendPage,
        widgetBuilder: (context) => AddAnnualIncomeSpendPage()),
    // 全年收支-日常收支类型管理
    YPPageRoute(
        routeName: RouteNameCollection.annualIncomeManageTypePage,
        widgetBuilder: (context) => AnnualIncomeManageTypePage()),
    // 全年收支-日常收支添加类型
    YPPageRoute(
        routeName: RouteNameCollection.annualIncomeAddTypePage,
        widgetBuilder: (context) => AnnualIncomeAddTypePage()),
    YPPageRoute(
        routeName: RouteNameCollection.testPage,
        widgetBuilder: (context) => AdInterstitialTest()),
    /*************************** web的在这上边 ****************************/
    /*************************** web的在这下边 ****************************/
    YPPageRoute(
      routeName: RouteNameCollection.webPage,
      widgetBuilder: (context) => WebPage(),
    ),
    /*************************** web的在这上边 ****************************/
  ]);
}

_initAccountRoutes() {
  YPRoute.registerRoutes([
    YPPageRoute(
        routeName: RouteNameCollection.login,
        widgetBuilder: (context) => LoginPage()),
    YPPageRoute(
        routeName: RouteNameCollection.lastLogin,
        widgetBuilder: (context) => LastLoginPage()),
    YPPageRoute(
        routeName: RouteNameCollection.selectRole,
        widgetBuilder: (context) => SelectRolePage()),
    YPPageRoute(
        routeName: RouteNameCollection.transferWork,
        widgetBuilder: (context) => TransferWorkPage()),
    YPPageRoute(
        routeName: RouteNameCollection.groupCloudAlbum,
        widgetBuilder: (context) => GroupCloudAlbumPage()),
    YPPageRoute(
        routeName: RouteNameCollection.imageViewer,
        widgetBuilder: (context) => ImageViewerPage()),
    YPPageRoute(
        routeName: RouteNameCollection.photoViewer,
        widgetBuilder: (context) => PhotoViewerPage()),
    YPPageRoute(
        routeName: RouteNameCollection.modifyDataHome,
        widgetBuilder: (context) => ModifyDataHomePage()),
    YPPageRoute(
        routeName: RouteNameCollection.modifyDataPhone,
        widgetBuilder: (context) => BindPhonePage()),
    YPPageRoute(
        routeName: RouteNameCollection.idCardInfoPage,
        widgetBuilder: (context) => IdCardInfoPage()),
    YPPageRoute(
        routeName: RouteNameCollection.myAccreditProjectListPage,
        widgetBuilder: (context) => MyAccreditProjectListPage()),
    YPPageRoute(
        routeName: RouteNameCollection.accreditProjectDetailPage,
        widgetBuilder: (context) => AccreditProjectDetailPage()),
    YPPageRoute(
        routeName: RouteNameCollection.bankCardManagerPage,
        widgetBuilder: (context) => BankCardManagerPage()),
    YPPageRoute(
        routeName: RouteNameCollection.bankCardModifyPage,
        widgetBuilder: (context) => BankCardModifyPage()),
  ]);
}

void _initRouteInterceptor() {
  YPRoute.registerInterceptor(MyRouteInterceptor());
}

class RouteNameCollection {
  //============路由名字需要 / 开头，名字全小写，太长用下划线连接==========================

  static const String splash = "/splash";
  static const String main = "/main";
  static const String webPage = "/web/web_page";
  static const String setting = "/user_center/setting";
  static const String name = "/user_center/name";
  static const String version = "/user_center/version";
  static const String visitor = "/user_center/visitor";

  static const test = '/test';

  /// 个人记工页
  static const personalRecordWorkPoints =
      '/personal/personal_record_workpoints';
  /// 购买VIP
  static const buyVipPage = '/user/buy_vip_page';
  static const changeProjectPage = '/personal/change_project_page';
  static const String third = "/user_center/third";
  static const String mediaViewer = "/media/viewer";
  static const String photoViewer = "/photo/viewer";
  static const String batchDelete = "/batch/delete";
  static const String chooseProject = "/choose/project";
  static const String chooseProjectFilter = "/choose/projectFilter";
  static const String changeProjectName = "/common/change_project_name";

  static const String login = 'account/login';
  static const String lastLogin = 'account/login/last_login';
  static const String selectRole = 'account/role';
  static const String transferWork = 'account/transfer';
  static const String dailyFlow = "/calendar/daily_flow";
  static const String workerWorklogTrash = "/worker/worker_worklog_trash";
  static const String modifyDataHome = '/modify_data/home';
  static const String modifyDataPhone = '/modify_data/phone';
  static const String idCardInfoPage = '/modify_data/id_card';
  static const String myAccreditProjectListPage =
      '/modify_data/my_accredit_project_list';
  static const String accreditProjectDetailPage =
      '/modify_data/accredit_project_detail';
  static const String bankCardManagerPage = '/modify_data/bank_card_manager';
  static const String bankCardModifyPage = '/modify_data/bank_card_modify';
  static const String workerWorklogCrosscheck =
      "/worker/worker_worklog_crosscheck";
  static const String workerWorklogFlowDetails =
      "/worker/worker_worklog_flow_details";
  static const String workerAttendanceSheet = "/worker/worker_attendance_sheet";
  static const String workerAttendanceDownload =
      "/worker/worker_attendance_download";
  static const String workerProjectUnsettled =
      "/worker/worker_project_unsettled";

  /// 费用名称选择页面
  static const expenseChoose = '/common/expense_choose';

  /// 分项选择页面
  static const subitemChoose = '/common/subitem_choose';

  /// 添加分项页面
  static const addSubitem = '/common/add_subitem';

  /// 添加分项单位页面
  static const addSubitemUnit = '/common/add_subitem_unit';

  /*************************** 公用页面这下边 ****************************/

  /// 添加备注页面
  static const notes = '/common/notes';

  ///类型选择页面
  static const selectRecordType = '/common/select_record_type';

  /// 转移记工页面
  static const transferRecord = '/common/transfer_record';

  /// 鱼泡资讯页面
  static const yuPaoNews = '/common/yu_pao_news';

  /// 意见反馈页面
  static const feedback = '/common/feedback';

  /// 账号管理页面
  static const accountManage = '/common/account_manage';

  /// 换绑手机号页面
  static const changeBindPhone = '/common/change_bind_phone';

  /// 历史手机号
  static const historyPhone = '/common/history_phone';

  /// 切换手机号
  static const switchAccount = '/common/switch_account';

  /// 编辑用户信息
  static const editWorkerInfo = '/common/edit_worker_info';

  /*************************** 公用页面在这上边 ****************************/

  /*************************** 班组的在这下边 ****************************/

  ///通讯录页面
  static const contact = "/user_center/contact";

  ///班组项目日历页
  static const groupProCalendar = '/group/pro_calendar';

  ///班组项目统计页面
  static const groupProStatistics = '/group/pro_statistics';

  ///班组流水页面
  static const groupProBill = '/group/pro_bill';

  ///修改班组项目记工
  static const groupProEditRecordWork = '/group/edit_record_work';

  ///修改工资
  static const groupProEditWage = '/group/edit_wage';

  /// 班组项目设置
  static const groupProjectSetting = '/group/project_setting';

  /// 邀请工友
  static const inviteWorker = '/worker/invite';

  /// 工友设置
  static const workerSetting = '/group/worker_setting';

  /// 工友名片
  static const workerResume = '/group/worker_resume';

  /// 工友选择
  static const workerSelector = '/group/worker_selector';

  /// 手机联系人
  static const phoneContact = '/group/phone_contact';

  /// 修改工友手机号
  static const modifyWorkerTel = '/group/modify_worker_tel';

  /// 班组记工
  static const groupRecordWork = '/group/group_record_work';

  /// 班组记借支结算
  static const groupAccountWork = '/group/group_account_work';

  /// 未结列表
  static const liquidated = "/group/liquidated";

  /// 未结详情
  static const liquidatedDetail = "/group/liquidated_detail";

  /// 未结 记结算
  static const groupSettlement = "/group/settlement";

  ///班组云相册页面
  static const groupCloudAlbum = '/group/cloud_album';

  ///班组回收站
  static const groupRecycle = '/group/recycle';

  ///已结清项目页面
  static const settledPage = '/group/settled_page';

  ///施工日志
  static const constructionLog = '/group/construction_log';

  ///修改日志
  static const changeLog = '/group/change_log';

  ///修改天气
  static const changeWeather = '/group/change_weather';

  ///搜索日志
  static const searchLog = '/group/search_log';

  ///日志内容编辑
  static const logContentEdit = '/group/log_content_edit';

  ///选择导出时间
  static const selectExportTime = '/group/construction_log/select_export_time';

  ///选择导出日志
  static const exportLogList = '/group/construction_log/export_log_list';

  /*************************** 班组的在这上边 ****************************/

  /// 修改个人项目记工页面
  static const changePersonalRecordWork = '/worker/change_record_work';

  /// 我创建的项目详情
  static const myCreateProjectDetail = '/worker/my_create_project_detail';

  /// 工人创建项目设置
  static const projectSetup = '/worker/project_setup';

  /// 工人参与项目设置
  static const participatedProjectSetup = '/worker/participated_project_setup';

  /// 我参与的项目详情
  static const myParticipatedProjectDetail =
      '/worker/my_participated_project_detail';

  /// 扫码页面
  static const qrScan = '/worker/qr_scan';

  /// 邀请加入页面
  static const inviteJoin = '/worker/invite_join';

  /// 邀请姓名填写页面
  static const inviteName = '/worker/invite_name';

  /// 扫码教程页面
  static const whatScan = '/worker/qr_scan/what_scan';

  /// 个人记工
  static const personalRecordWork = '/common/personal_record_work';

  /// 图片查看
  static const imageViewer = '/worker/image_viewer';

  /// 图片查看
  static const personAccountRecord = '/person/account_record';

  /// 导出打卡记录
  static const exportRecord = '/export/record';

  /// 在场工友
  static const onSiteWorkers = '/group/on_site_workers';
  static const massLeave = '/group/on_site_workers/mass_operation';
  static const downloadWorkersMaterial = '/group/download_workers_material';

  /// 带班设置
  static const shiftLeadSetup = '/group/shift_lead_setup';

  /// 点工工价
  static const dailyRate = '/group/daily_rate';

  /// 删除带班
  static const shiftLeadDelete = '/group/shift_lead_delete';

  /// 带班工友信息
  static const shiftLeadInfo = '/group/shift_lead_info';

  /// 全年收支列表
  static const annualIncomeSpendPage = '/annual_income_spend/list';

  /// 全年收支-添加日常收支
  static const addAnnualIncomeSpendPage = '/annual_income_spend/add';

  /// 全年收支-日常收支类型管理
  static const annualIncomeManageTypePage = '/annual_income_spend/manage_type';

  /// 全年收支-日常收支添加类型
  static const annualIncomeAddTypePage = '/annual_income_spend/add_type';

  /// Test
  static const testPage = '/test_page';
}
