class VipPurchaseBizModel {

  /// 交易单号
  String tradeNo;

  /// 订单编号
  String orderNo;

  /// 过期时间
  double expireTime;

  /// 支付信息
  ExtInfoBizModel? extInfo;

  VipPurchaseBizModel({
    this.tradeNo = "",
    this.orderNo = "",
    this.expireTime = 0.0,
    this.extInfo,
  });

  @override
  String toString() {
    return "VipPurchaseBizModel(tradeNo: $tradeNo, orderNo: $orderNo, expireTime: $expireTime, extInfo: ${extInfo.toString()})";
  }
}

class ExtInfoBizModel {
  String timestamp;
  String packageValue;
  String paySign;
  String appId;
  String signType;
  String partnerId;
  String prepayId;
  String nonceStr;

  ExtInfoBizModel({
    this.timestamp = "",
    this.packageValue = "",
    this.paySign = "",
    this.appId = "",
    this.signType = "",
    this.partnerId = "",
    this.prepayId = "",
    this.nonceStr = "",
  });

  @override
  String toString() {
    return "ExtInfoBizModel(timestamp: $timestamp, packageValue: $packageValue, paySign: $paySign, appId: $appId, signType: $signType, partnerId: $partnerId, prepayId: $prepayId, nonceStr: $nonceStr)";
  }
}

