class VipNetModelBizModel {
  List<VipListBizModel> vipList;

  /// 会员说明文案 客户端需要将@之间内容着重显示
  String explanation;

  VipNetModelBizModel({
    this.vipList = const [],
    this.explanation = "",
  });

  @override
  String toString() {
    return "VipNetModelBizModel(vipList: ${vipList.toString()}, explanation: $explanation)";
  }
}

class VipListBizModel {
  double id;

  /// 会员名称
  String name;

  /// 会员天数
  int days;

  /// 价格，单位为分
  String price;

  /// 标签文案
  String label;

  /// 折扣价，单位为分
  String discountPrice;

  /// 节省价格，单位为分
  String reducePrice;

  /// 续费价格-连续包月价格（单位分）
  String renewPrice;

  /// 价格描述
  String priceDesc;

  /// 价格对比删除线 0-无 1-有
  double priceShow;

  /// 是否默认套餐
  double defaultShow;

  /// 套餐福利
  List<String> welfare;

  VipListBizModel({
    this.id = 0.0,
    this.name = "",
    this.days = 0,
    this.price = "",
    this.label = "",
    this.discountPrice = "",
    this.reducePrice = "",
    this.renewPrice = "",
    this.priceDesc = "",
    this.priceShow = 0.0,
    this.defaultShow = 0.0,
    this.welfare = const [],
  });

  @override
  String toString() {
    return "VipListBizModel(id: $id, name: $name, days: $days, price: $price, label: $label, discountPrice: $discountPrice, reducePrice: $reducePrice, renewPrice: $renewPrice, priceDesc: $priceDesc, priceShow: $priceShow, defaultShow: $defaultShow, welfare: ${welfare.toString()})";
  }
}

