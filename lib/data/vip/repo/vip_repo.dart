
import 'package:gdjg_pure_flutter/data/vip/ds/model/vip_net_model.dart';
import 'package:gdjg_pure_flutter/data/vip/ds/model/vip_pay_net_model.dart';
import 'package:gdjg_pure_flutter/data/vip/ds/model/vip_status_net_model.dart';
import 'package:gdjg_pure_flutter/data/vip/ds/param/vip_purchase_param_model.dart';
import 'package:gdjg_pure_flutter/data/vip/ds/vip_rds.dart';
import 'package:gdjg_pure_flutter/data/vip/repo/biz/vip_biz_model.dart';
import 'package:gdjg_pure_flutter/data/vip/repo/biz/vip_pay_biz_model.dart';
import 'package:gdjg_pure_flutter/data/vip/repo/biz/vip_status_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class VipRepo {
  final _vipRds = VipRds();



  Future<RespResult<VipNetModelBizModel>>
  getVipList() async {
    final result =
    await _vipRds.getVipList();
    return result.map(_transformExpensesLast);
  }

  VipNetModelBizModel _transformExpensesLast(
      VipNetModelNetModel? netModel) {
    if (netModel == null) {
      return VipNetModelBizModel();
    }
    return netModel.transform();
  }


  Future<RespResult<VipStatusBizModel>>
  getVipStatus() async {
    final result =
    await _vipRds.getVipStatus();
    return result.map(_transformVipStatus);
  }

  VipStatusBizModel _transformVipStatus(
      VipStatusNetModel? netModel) {
    if (netModel == null) {
      return VipStatusBizModel();
    }
    return netModel.transform();
  }


  Future<RespResult<VipPurchaseBizModel>>
  getVipOrder(VipPurchaseParamModel param) async {
    final result = await _vipRds.getVipOrder(param);
    return result.map(_transformVipPurchase);
  }

  VipPurchaseBizModel _transformVipPurchase(
      VipPurchaseNetModel? netModel) {
    if (netModel == null) {
      return VipPurchaseBizModel();
    }
    return netModel.transform();
  }

}