import 'package:gdjg_pure_flutter/data/vip/ds/model/vip_net_model.dart';
import 'package:gdjg_pure_flutter/data/vip/ds/model/vip_pay_net_model.dart';
import 'package:gdjg_pure_flutter/data/vip/ds/model/vip_status_net_model.dart';
import 'package:gdjg_pure_flutter/data/vip/ds/param/vip_purchase_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class VipRds {

  /// 获取VIP列表
  Future<RespResult<VipNetModelNetModel>>
  getVipList() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/vip/list',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: false, printResp: true)),
            (json) => VipNetModelNetModel.fromJson(json));
  }

  /// 获取VIP状态
  Future<RespResult<VipStatusNetModel>>
  getVipStatus() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/vip/status',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: false, printResp: true)),
            (json) => VipStatusNetModel.fromJson(json));
  }

  /// 获取VIP订单
  Future<RespResult<VipPurchaseNetModel>>
  getVipOrder(VipPurchaseParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/vip/purchase',
            content: param.toMap(),
            method: HTTP_METHOD.POST,
            requestExtra: RequestExtra(showLoading: false, printResp: true)),
            (json) => VipPurchaseNetModel.fromJson(json));
  }
}