import '../../repo/biz/vip_status_biz_model.dart';

class VipStatusNetModel {

  /// 0-无VIP 1-有VIP
  double? status;

  /// VIP过期时间 仅年月日
  String? expire_time;

  VipStatusNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (status != null) map["status"] = status!;
    if (expire_time != null) map["expire_time"] = expire_time!;
    return map;
  }

  factory VipStatusNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = VipStatusNetModel();
    netModel.status = double.tryParse(json["status"].toString());
    netModel.expire_time = json["expire_time"]?.toString();
    return netModel;
  }

  VipStatusBizModel transform() {
    return VipStatusBizModel(
      status: status ?? 0.0,
      expireTime: expire_time ?? "",
    );
  }
}

