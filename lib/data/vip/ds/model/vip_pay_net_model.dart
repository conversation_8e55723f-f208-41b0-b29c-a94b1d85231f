import '../../repo/biz/vip_pay_biz_model.dart';

class VipPurchaseNetModel {

  /// 交易单号
  String? trade_no;

  /// 订单编号
  String? order_no;

  /// 过期时间
  double? expire_time;

  /// 支付信息
  ExtInfoNetModel? ext_info;

  VipPurchaseNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (trade_no != null) map["trade_no"] = trade_no!;
    if (order_no != null) map["order_no"] = order_no!;
    if (expire_time != null) map["expire_time"] = expire_time!;
    if (ext_info != null) map["ext_info"] = ext_info!;
    return map;
  }

  factory VipPurchaseNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = VipPurchaseNetModel();
    netModel.trade_no = json["trade_no"]?.toString();
    netModel.order_no = json["order_no"]?.toString();
    netModel.expire_time = double.tryParse(json["expire_time"].toString());
    netModel.ext_info = json["ext_info"] == null
      ? null
      : ExtInfoNetModel.fromJson(json["ext_info"] as Map<String, dynamic>);
    return netModel;
  }

  VipPurchaseBizModel transform() {
    return VipPurchaseBizModel(
      tradeNo: trade_no ?? "",
      orderNo: order_no ?? "",
      expireTime: expire_time ?? 0.0,
      extInfo: ext_info?.transform(),
    );
  }
}

class ExtInfoNetModel {
  String? timestamp;
  String? package_value;
  String? pay_sign;
  String? app_id;
  String? sign_type;
  String? partner_id;
  String? prepay_id;
  String? nonce_str;

  ExtInfoNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (timestamp != null) map["timestamp"] = timestamp!;
    if (package_value != null) map["package_value"] = package_value!;
    if (pay_sign != null) map["pay_sign"] = pay_sign!;
    if (app_id != null) map["app_id"] = app_id!;
    if (sign_type != null) map["sign_type"] = sign_type!;
    if (partner_id != null) map["partner_id"] = partner_id!;
    if (prepay_id != null) map["prepay_id"] = prepay_id!;
    if (nonce_str != null) map["nonce_str"] = nonce_str!;
    return map;
  }

  factory ExtInfoNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ExtInfoNetModel();
    netModel.timestamp = json["timestamp"]?.toString();
    netModel.package_value = json["package_value"]?.toString();
    netModel.pay_sign = json["pay_sign"]?.toString();
    netModel.app_id = json["app_id"]?.toString();
    netModel.sign_type = json["sign_type"]?.toString();
    netModel.partner_id = json["partner_id"]?.toString();
    netModel.prepay_id = json["prepay_id"]?.toString();
    netModel.nonce_str = json["nonce_str"]?.toString();
    return netModel;
  }

  ExtInfoBizModel transform() {
    return ExtInfoBizModel(
      timestamp: timestamp ?? "",
      packageValue: package_value ?? "",
      paySign: pay_sign ?? "",
      appId: app_id ?? "",
      signType: sign_type ?? "",
      partnerId: partner_id ?? "",
      prepayId: prepay_id ?? "",
      nonceStr: nonce_str ?? "",
    );
  }
}

