import '../../repo/biz/vip_biz_model.dart';

class VipNetModelNetModel {
  List<VipListNetModel>? vip_list;

  /// 会员说明文案 客户端需要将@之间内容着重显示
  String? explanation;

  VipNetModelNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (vip_list != null) map["vip_list"] = vip_list!;
    if (explanation != null) map["explanation"] = explanation!;
    return map;
  }

  factory VipNetModelNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = VipNetModelNetModel();
    netModel.vip_list = (json["vip_list"] as List<dynamic>?)
        ?.map((e) => VipListNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.explanation = json["explanation"]?.toString();
    return netModel;
  }

  VipNetModelBizModel transform() {
    return VipNetModelBizModel(
      vipList: vip_list?.map((e) => e.transform()).toList() ?? [],
      explanation: explanation ?? "",
    );
  }
}

class VipListNetModel {
  double? id;

  /// 会员名称
  String? name;

  /// 会员天数
  int? days;

  /// 价格，单位为分
  String? price;

  /// 标签文案
  String? label;

  /// 折扣价，单位为分
  String? discount_price;

  /// 节省价格，单位为分
  String? reduce_price;

  /// 续费价格-连续包月价格（单位分）
  String? renew_price;

  /// 价格描述
  String? price_desc;

  /// 价格对比删除线 0-无 1-有
  double? price_show;

  /// 是否默认套餐
  double? default_show;

  /// 套餐福利
  List<String>? welfare;

  VipListNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (name != null) map["name"] = name!;
    if (days != null) map["days"] = days!;
    if (price != null) map["price"] = price!;
    if (label != null) map["label"] = label!;
    if (discount_price != null) map["discount_price"] = discount_price!;
    if (reduce_price != null) map["reduce_price"] = reduce_price!;
    if (renew_price != null) map["renew_price"] = renew_price!;
    if (price_desc != null) map["price_desc"] = price_desc!;
    if (price_show != null) map["price_show"] = price_show!;
    if (default_show != null) map["default_show"] = default_show!;
    if (welfare != null) map["welfare"] = welfare!;
    return map;
  }

  factory VipListNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = VipListNetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.days = int.tryParse(json["days"].toString());
    netModel.price = json["price"]?.toString();
    netModel.label = json["label"]?.toString();
    netModel.discount_price = json["discount_price"]?.toString();
    netModel.reduce_price = json["reduce_price"]?.toString();
    netModel.renew_price = json["renew_price"]?.toString();
    netModel.price_desc = json["price_desc"]?.toString();
    netModel.price_show = double.tryParse(json["price_show"].toString());
    netModel.default_show = double.tryParse(json["default_show"].toString());
    netModel.welfare = (json["welfare"] as List<dynamic>?)
        ?.map((e) => e as String)
        .toList();
    return netModel;
  }

  VipListBizModel transform() {
    return VipListBizModel(
      id: id ?? 0.0,
      name: name ?? "",
      days: days ?? 0,
      price: price ?? "",
      label: label ?? "",
      discountPrice: discount_price ?? "",
      reducePrice: reduce_price ?? "",
      renewPrice: renew_price ?? "",
      priceDesc: price_desc ?? "",
      priceShow: price_show ?? 0.0,
      defaultShow: default_show ?? 0.0,
      welfare: welfare ?? [],
    );
  }
}

