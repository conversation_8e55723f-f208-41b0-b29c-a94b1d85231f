import 'code_login_param_model.dart';

class LoginOneClickLoginAParamModel {

  ///
  ShareReqNetModel? shareReq;

  /// 设备信息上下文
  String? c;

  /// 令牌
  String? oneClickToken;

  /// 应用ID
  String? appId;

  LoginOneClickLoginAParamModel();

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (shareReq != null) map["shareReq"] = shareReq!.toMap();
    if (c != null) map["c"] = c!;
    if (oneClickToken != null) map["oneClickToken"] = oneClickToken!;
    if (appId != null) map["appId"] = appId!;
    return map;
  }

}