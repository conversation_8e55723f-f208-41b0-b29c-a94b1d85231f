import 'package:gdjg_pure_flutter/utils/store_util/base_lds.dart';

class GroupGuideLds extends BaseLds<int> {
  static const int maskStep1 = 0x01 << 0;
  static const int maskStep2 = 0x01 << 1;
  static const int maskStep3 = 0x01 << 2;
  static const int maskStep4 = 0x01 << 3;
  static const int maskStep5 = 0x01 << 4;

  @override
  String getBizName() => "GroupGuide";

  /// [mask] see: [maskStep1], [maskStep2], [maskStep3], [maskStep4], [maskStep5]
  Future<bool> fetchGuided(int mask) async {
    final int current = get(defaultValue: 0) ?? 0;
    return current & mask != 0;
  }

  /// [mask] see: [maskStep1], [maskStep2], [maskStep3], [maskStep4], [maskStep5]
  /// [complete] true: 完成，false: 未完成
  Future<bool> updateGuided(int mask, bool complete) async {
    final int current = get(defaultValue: 0) ?? 0;
    final int result;
    if (complete) {
      result = current | mask;
    } else {
      result = current & ~mask;
    }
    save(result);
    return complete;
  }
}
