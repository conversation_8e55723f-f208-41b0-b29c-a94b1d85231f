class WorkerListDeptParamModel {

  /// 记工本的所属部门ID
  final String? dept_id;

  /// 0 在场工人，1 退场工人
  final String? is_deleted;

  /// 类型 1-点工 6-包工
  final String? businessType;

  /// 是否休息 0-否 1-是
  final String? is_rest;

  /// 是否包含工价信息
  final String? fee;

  WorkerListDeptParamModel({
    this.dept_id,
    this.is_deleted,
    this.businessType,
    this.is_rest,
    this.fee,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (dept_id != null) map["dept_id"] = dept_id!;
    if (is_deleted != null) map["is_deleted"] = is_deleted!;
    if (businessType != null) map["businessType"] = businessType!;
    if (is_rest != null) map["is_rest"] = is_rest!;
    if (fee != null) map["fee"] = fee!;
    return map;
  }
}
