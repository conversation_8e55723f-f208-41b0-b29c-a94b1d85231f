class WorkerListDeptBizModel {
  List<NoteWorkerBizModel> noteWorker;
  String shareToken;
  double delNum;

  WorkerListDeptBizModel({
    this.noteWorker = const [],
    this.shareToken = "",
    this.delNum = 0.0,
  });

  @override
  String toString() {
    return "WorkerListDeptBizModel(noteWorker: ${noteWorker.toString()}, shareToken: $shareToken, delNum: $delNum)";
  }
}

class NoteWorkerBizModel {
  double workerId;
  String name;
  String tel;
  String namePy;
  String nameColor;
  String avatar;
  double quitTime;
  double isBind;
  double memberId;
  double userId;
  double isRest;
  double isSelf;
  double isSelfCreated;
  double isAgent;
  double contractEmployeeStatus;
  double isGrant;
  dynamic occ;
  double isWorkerFee;
  WorkerFeeBizModel? workerFee;

  NoteWorkerBizModel({
    this.workerId = 0.0,
    this.name = "",
    this.tel = "",
    this.namePy = "",
    this.nameColor = "",
    this.avatar = "",
    this.quitTime = 0.0,
    this.isBind = 0.0,
    this.memberId = 0.0,
    this.userId = 0.0,
    this.isRest = 0.0,
    this.isSelf = 0.0,
    this.isSelfCreated = 0.0,
    this.isAgent = 0.0,
    this.contractEmployeeStatus = 0.0,
    this.isGrant = 0.0,
    this.occ = null,
    this.isWorkerFee = 0.0,
    this.workerFee,
  });

  @override
  String toString() {
    return "NoteWorkerBizModel(workerId: $workerId, name: $name, tel: $tel, namePy: $namePy, nameColor: $nameColor, avatar: $avatar, quitTime: $quitTime, isBind: $isBind, memberId: $memberId, userId: $userId, isRest: $isRest, isSelf: $isSelf, isSelfCreated: $isSelfCreated, isAgent: $isAgent, contractEmployeeStatus: $contractEmployeeStatus, isGrant: $isGrant, occ: $occ, isWorkerFee: $isWorkerFee, workerFee: $workerFee)";
  }
}

class WorkerFeeBizModel {
  String feeStandardId;
  double workingHoursPrice;
  double workingHoursStandard;
  int businessType;
  int overtimeType;
  double overtimeHoursStandard;
  double overtimeHoursPrice;

  WorkerFeeBizModel({
    this.feeStandardId = "",
    this.workingHoursPrice = 0.0,
    this.workingHoursStandard = 0.0,
    this.businessType = 0,
    this.overtimeType = 0,
    this.overtimeHoursStandard = 0.0,
    this.overtimeHoursPrice = 0.0,
  });

  @override
  String toString() {
    return "WorkerFeeBizModel(feeStandardId: $feeStandardId, workingHoursPrice: $workingHoursPrice, workingHoursStandard: $workingHoursStandard, businessType: $businessType, overtimeType: $overtimeType, overtimeHoursStandard: $overtimeHoursStandard, overtimeHoursPrice: $overtimeHoursPrice)";
  }
}
