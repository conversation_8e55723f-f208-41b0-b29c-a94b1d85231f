class CorsNoteGroupNoFeeMoneyParamModel {

  /// 
  final String? start_business_time;

  /// 
  final String? end_business_time;

  /// 1,2,3 说明：1点工，2工量，3短工，4借支, 5支出, 6包工, 7小时工
  final String? business_type;

  /// 工人id 1,2,3
  final String? worker_id;

  CorsNoteGroupNoFeeMoneyParamModel({
    this.start_business_time,
    this.end_business_time,
    this.business_type,
    this.worker_id,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (start_business_time != null) map["start_business_time"] = start_business_time!;
    if (end_business_time != null) map["end_business_time"] = end_business_time!;
    if (business_type != null) map["business_type"] = business_type!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    return map;
  }
}
