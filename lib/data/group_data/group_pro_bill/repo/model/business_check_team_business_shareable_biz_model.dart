class BusinessCheckTeamBusinessShareableBizModel {

  /// -2-有无手机号工友 -1-有已删除工友 0-其他异常 1-正常
  int status;

  /// 提示信息
  String msg;

  /// 分享的工人数量
  int workerNum;

  /// 分享的工人ID
  List<num> workerIds;

  BusinessCheckTeamBusinessShareableBizModel({
    this.status = 0,
    this.msg = "",
    this.workerNum = 0,
    this.workerIds = const [],
  });

  @override
  String toString() {
    return "BusinessCheckTeamBusinessShareableBizModel(status: $status, msg: $msg, workerNum: $workerNum, workerIds: ${workerIds.toString()})";
  }
}

