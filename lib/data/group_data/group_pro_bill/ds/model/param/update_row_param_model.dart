class UpdateRowAParamModel {

  /// 工友通讯录ID
  String? worker_id;

  /// 头像
  String? avatar;

  /// 姓名
  String? name;

  /// 姓名展示颜色
  String? name_color;

  /// 手机号
  String? tel;

  /// 行业ID
  double? industry_id;

  /// 工种ID
  double? occupation_id;

  /// 项目ID, 与dept_id二选一
  String? work_note;

  /// 部门ID, 与work_note二选一
  String? dept_id;

  UpdateRowAParamModel();

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (avatar != null) map["avatar"] = avatar!;
    if (name != null) map["name"] = name!;
    if (name_color != null) map["name_color"] = name_color!;
    if (tel != null) map["tel"] = tel!;
    if (industry_id != null) map["industry_id"] = industry_id!;
    if (occupation_id != null) map["occupation_id"] = occupation_id!;
    if (work_note != null) map["work_note"] = work_note!;
    if (dept_id != null) map["dept_id"] = dept_id!;
    return map;
  }

}

