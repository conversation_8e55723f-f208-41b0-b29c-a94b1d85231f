class BusinessCheckTeamBusinessShareableParamModel {
  /// 记工本id，多个用逗号隔开
  String? work_note;

  /// 分享记工开始时间
  String? start_business_time;

  /// 分享记工结束时间
  String? end_business_time;

  /// 记工类型 多个用逗号隔开 不填默认全部
  String? business_type;

  /// 工人id，多个用逗号隔开 不填默认全部
  String? worker_id;

  BusinessCheckTeamBusinessShareableParamModel({
    this.work_note,
    this.start_business_time,
    this.end_business_time,
    this.business_type,
    this.worker_id,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (work_note != null) map["work_note"] = work_note!;
    if (start_business_time != null)
      map["start_business_time"] = start_business_time!;
    if (end_business_time != null)
      map["end_business_time"] = end_business_time!;
    if (business_type != null) map["business_type"] = business_type!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    return map;
  }
}
