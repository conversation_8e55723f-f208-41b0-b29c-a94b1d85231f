import '../../../repo/model/update_row_biz_model.dart';

class UpdateRowNetModel {
  double? id;
  String? name;
  String? name_py;
  String? name_color;
  String? tel;
  String? header_img;

  UpdateRowNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (name != null) map["name"] = name!;
    if (name_py != null) map["name_py"] = name_py!;
    if (name_color != null) map["name_color"] = name_color!;
    if (tel != null) map["tel"] = tel!;
    if (header_img != null) map["header_img"] = header_img!;
    return map;
  }

  factory UpdateRowNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = UpdateRowNetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.name_py = json["name_py"]?.toString();
    netModel.name_color = json["name_color"]?.toString();
    netModel.tel = json["tel"]?.toString();
    netModel.header_img = json["header_img"]?.toString();
    return netModel;
  }

  UpdateRowBizModel transform() {
    return UpdateRowBizModel(
      id: id ?? 0.0,
      name: name ?? "",
      namePy: name_py ?? "",
      nameColor: name_color ?? "",
      tel: tel ?? "",
      headerImg: header_img ?? "",
    );
  }
}

