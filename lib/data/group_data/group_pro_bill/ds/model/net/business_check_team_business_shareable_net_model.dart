import '../../../repo/model/business_check_team_business_shareable_biz_model.dart';

class BusinessCheckTeamBusinessShareableNetModel {

  /// -2-有无手机号工友 -1-有已删除工友 0-其他异常 1-正常
  int? status;

  /// 提示信息
  String? msg;

  /// 分享的工人数量
  int? worker_num;

  /// 分享的工人ID
  List<int>? worker_ids;

  BusinessCheckTeamBusinessShareableNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (status != null) map["status"] = status!;
    if (msg != null) map["msg"] = msg!;
    if (worker_num != null) map["worker_num"] = worker_num!;
    if (worker_ids != null) map["worker_ids"] = worker_ids!;
    return map;
  }

  factory BusinessCheckTeamBusinessShareableNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessCheckTeamBusinessShareableNetModel();
    netModel.status = json["status"];
    netModel.msg = json["msg"]?.toString();
    netModel.worker_num = json["worker_num"];
    netModel.worker_ids = (json["worker_ids"] as List<dynamic>?)
        ?.map((e) => e as int)
        .toList();
    return netModel;
  }

  BusinessCheckTeamBusinessShareableBizModel transform() {
    return BusinessCheckTeamBusinessShareableBizModel(
      status: status??0,
      msg: msg ?? "",
      workerNum: worker_num ?? 0,
      workerIds: worker_ids ?? [],
    );
  }
}

