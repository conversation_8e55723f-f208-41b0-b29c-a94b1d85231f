import '../../../repo/model/business_download_file_excel_biz_model.dart';

class BusinessDownloadFileExcelNetModel {

  /// 
  String? url;

  /// [4.9]新增 分享文件名
  String? file_name;

  BusinessDownloadFileExcelNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (url != null) map["url"] = url!;
    if (file_name != null) map["file_name"] = file_name!;
    return map;
  }

  factory BusinessDownloadFileExcelNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessDownloadFileExcelNetModel();
    netModel.url = json["url"]?.toString();
    netModel.file_name = json["file_name"]?.toString();
    return netModel;
  }

  BusinessDownloadFileExcelBizModel transform() {
    return BusinessDownloadFileExcelBizModel(
      url: url ?? "",
      fileName: file_name ?? "",
    );
  }
}

