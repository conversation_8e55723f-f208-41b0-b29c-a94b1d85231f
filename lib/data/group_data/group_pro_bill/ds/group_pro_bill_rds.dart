import 'package:gdjg_pure_flutter/data/common_data/net/count_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/net/business_download_file_excel_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/net/business_get_new_share_business_count_url_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/net/group_business_get_group_business_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/net/update_row_net_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/business_check_team_business_shareable_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/business_download_file_excel_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/business_get_new_share_business_count_url_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/group_business_get_group_business_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_pro_bill/ds/model/param/update_row_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

import 'model/net/business_check_team_business_shareable_net_model.dart';


class GroupProBillRds {
  ///班组统计
  Future<RespResult<CountNetModel>> getGroupBusinessCount(
      GroupBusinessGetGroupBusinessCountParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/group-business/get-group-business-count',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => CountNetModel.fromJson(json));
  }

  ///班组项目流水
  Future<RespResult<GroupBusinessGetGroupBusinessListNetModel>>
      getGroupBusinessList(
          GroupBusinessGetGroupBusinessListParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/group-business/get-group-business-list',
            method: HTTP_METHOD.GET,
            content: params.toMap()),
        (json) => GroupBusinessGetGroupBusinessListNetModel.fromJson(json));
  }

  /// 生成图片分享
  Future<RespResult<BusinessGetNewShareBusinessCountUrlNetModel>>
      generateShareImage(
          BusinessGetNewShareBusinessCountUrlParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/get_new_share_business_count_url',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true, printResp: true),
            content: params.toMap().cast()),
        (json) => BusinessGetNewShareBusinessCountUrlNetModel.fromJson(json));
  }

  /// 下载考勤表excel
  Future<RespResult<BusinessDownloadFileExcelNetModel>> downloadAttendanceExcel(
      BusinessDownloadFileExcelParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/download_file_excel',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true, printResp: true),
            content: params.toMap().cast()),
        (json) => BusinessDownloadFileExcelNetModel.fromJson(json));
  }

  /// 班组项目分享条件是否有效
  Future<RespResult<BusinessCheckTeamBusinessShareableNetModel>> fetchCheckIsShare(
      BusinessCheckTeamBusinessShareableParamModel params)async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/check_team_business_shareable',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true, printResp: true),
            content: params.toMap().cast()),
            (json) => BusinessCheckTeamBusinessShareableNetModel.fromJson(json));
  }

  /// 更新用户信息
  Future<RespResult<UpdateRowNetModel>> updateWorkerInfo(
      UpdateRowAParamModel params)async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/worker/update',
            method: HTTP_METHOD.POST ,
            requestExtra: RequestExtra(showLoading: true, printResp: true),
            content: params.toMap().cast()),
            (json) => UpdateRowNetModel.fromJson(json));
  }
}
