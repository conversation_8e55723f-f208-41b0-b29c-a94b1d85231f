import 'dart:convert';

class BusinessAddParamModel {
  ///
  String? worker_id;

  BusinessAddParamModel({
    this.worker_id,
  });

  Map<String, Object> toMap() {
    return {};
  }
}

/// 公共参数
class BusinessAddPublicParamModel {
  /// 当前的记工日期,可以多选
  late String? businessTime;

  /// 身份标示-班组记工 - 写死1
  final int? identity;

  /// 项目id
  late String? workNoteId;

  BusinessAddPublicParamModel({
    this.businessTime,
    this.identity,
    this.workNoteId,
  });
}

class ResourceExtParamModel {
  /// 图片id
  final String resourceId;

  /// 图片类型-0图片，1视频
  final int resourceType;

  /// 拍摄时间-2025-08-27 15: 30: 55
  final String? takeTime;

  /// 资源地址-半路径
  final String url;

  /// 视频时长
  final double? videoTime;

  ResourceExtParamModel({
    required this.resourceId,
    required this.resourceType,
    required this.url,
    this.takeTime,
    this.videoTime,
  });

  Map<String, dynamic> toMap() {
    return {
      "resource_id": resourceId,
      "resource_type": resourceType,
      "take_time": takeTime ?? "",
      "url": url,
      "video_time": videoTime ?? 0.0,
    };
  }

  @override
  String toString() {
    return "ResourceExtParamModel(resourceId: $resourceId, resourceType: $resourceType, takeTime: $takeTime, url: $url, videoTime: $videoTime)";
  }
}

/// 点工包工记工数据
class WorkRecordParamModel extends BusinessAddParamModel {
  ///
  final String? business_time;

  ///
  final String? work_note;

  ///
  final String? business_type;

  ///
  final String? identity;

  /// 记工备注
  final String? note;

  /// 加班时间
  final String? overtime;

  /// 上班工时类型(工天和休息: 1, 小时: 2, 上下午: 3),该字段主要作为产品面板的统计使用
  final String? user_choose_spotwork;

  ///
  final String? img_url;

  ///
  final String? work_time;

  ///
  final String? work_time_hour;

  ///
  String? worker_id;

  final List<ResourceExtParamModel>? resource_ext;

  /// 上班工时
  final String? overtime_work;

  /// 下午工时
  final String? afternoon_work_time;

  /// 下午加班小时
  final String? afternoon_work_time_hour;

  /// 上午工时
  final String? morning_work_time;

  /// 上午加班小时
  final String? morning_work_time_hour;

  WorkRecordParamModel({
    this.work_note,
    this.business_type,
    this.note,
    this.img_url,
    this.business_time,
    this.work_time,
    this.work_time_hour,
    this.worker_id,
    this.overtime,
    this.resource_ext,
    this.user_choose_spotwork,
    this.identity,
    this.overtime_work,
    this.afternoon_work_time,
    this.afternoon_work_time_hour,
    this.morning_work_time,
    this.morning_work_time_hour,
  });

  @override
  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (work_note != null) map["work_note"] = work_note!;
    if (business_type != null) map["business_type"] = business_type!;
    if (identity != null) map["identity"] = identity!;
    if (note != null) map["note"] = note!;
    if (img_url != null) map["img_url"] = img_url!;
    if (business_time != null) map["business_time"] = business_time!;
    if (work_time != null) map["work_time"] = work_time!;
    if (work_time_hour != null) map["work_time_hour"] = work_time_hour!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (overtime != null) map["overtime"] = overtime!;
    if (overtime_work != null) map["overtime_work"] = overtime_work!;
    if (morning_work_time != null)
      map["morning_work_time"] = morning_work_time!;
    if (morning_work_time_hour != null)
      map["morning_work_time_hour"] = morning_work_time_hour!;
    if (afternoon_work_time != null)
      map["afternoon_work_time"] = afternoon_work_time!;
    if (afternoon_work_time_hour != null)
      map["afternoon_work_time_hour"] = afternoon_work_time_hour!;
    if (user_choose_spotwork != null) {
      map["user_choose_spotwork"] = user_choose_spotwork!;
    }
    if (resource_ext != null) {
      map["resource_ext"] = resourceExtToMap() as Object;
    }
    return map;
  }

  List<Map<String, dynamic>>? resourceExtToMap() {
    return resource_ext?.map((e) => e.toMap()).toList() ?? [];
  }

  @override
  String toString() {
    return "WorkRecordParamModel(work_note: $work_note, business_type: $business_type, identity: $identity, note: $note, img_url: $img_url, business_time: $business_time, work_time: $work_time, work_time_hour: $work_time_hour, worker_id: $worker_id, overtime: $overtime, user_choose_spotwork: $user_choose_spotwork, resource_ext: ${resource_ext.toString()})";
  }
}

class ShortWorkRecordParamModel extends BusinessAddParamModel {
  ///
  final String? business_time;

  ///
  final String? business_type;

  ///
  final String? img_url;

  ///
  final String? identity;

  ///
  final String? money;

  /// 记工备注
  final String? note;

  ///
  final String? work_note;

  /// 上班工时类型(工天和休息: 1, 小时: 2, 上下午: 3),该字段主要作为产品面板的统计使用
  final String? user_choose_spotwork;

  ///
  final String? worker_id;

  /// 媒体资源列表
  final List<ResourceExtParamModel>? resource_ext;

  ShortWorkRecordParamModel({
    this.business_time,
    this.business_type,
    this.img_url,
    this.identity,
    this.money,
    this.note,
    this.work_note,
    this.user_choose_spotwork,
    this.worker_id,
    this.resource_ext,
  });

  @override
  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (work_note != null) map["work_note"] = work_note!;
    if (business_type != null) map["business_type"] = business_type!;
    if (identity != null) map["identity"] = identity!;
    if (note != null) map["note"] = note!;
    if (img_url != null) map["img_url"] = img_url!;
    if (business_time != null) map["business_time"] = business_time!;
    if (money != null) map["money"] = money!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (user_choose_spotwork != null) map["action"] = user_choose_spotwork!;
    if (resource_ext != null) map["action"] = resourceExtToMap() as Object;
    return map;
  }

  List<Map<String, dynamic>>? resourceExtToMap() {
    return resource_ext?.map((e) => e.toMap()).toList() ?? [];
  }

  @override
  String toString() {
    return "ShortWorkRecordParamModel(business_time: $business_time, business_type: $business_type, img_url: $img_url, identity: $identity, money: $money, note: $note, work_note: $work_note, user_choose_spotwork: $user_choose_spotwork, worker_id: $worker_id, resource_ext: ${resource_ext.toString()})";
  }
}

/// 计量记工数据
class MetrologyWorkRecordParamModel extends BusinessAddParamModel {
  ///
  final String? business_time;

  ///
  final String? business_type;

  ///
  final String? img_url;

  ///
  final String? identity;

  ///
  final String? money;

  /// 记工备注
  final String? note;

  ///
  final String? work_note;

  /// 上班工时类型(工天和休息: 1, 小时: 2, 上下午: 3),该字段主要作为产品面板的统计使用
  final String? user_choose_spotwork;

  ///
  final String? worker_id;

  /// 总工程量
  final String? unit_num;

  /// 单位价格
  final String? unit_price;

  /// 分项id
  final String? unit_work_type;

  /// 分项名称
  final String? unit_work_type_name;

  /// 媒体资源列表
  final List<ResourceExtParamModel>? resource_ext;

  MetrologyWorkRecordParamModel({
    this.business_time,
    this.business_type,
    this.img_url,
    this.identity,
    this.money,
    this.note,
    this.work_note,
    this.user_choose_spotwork,
    this.worker_id,
    this.unit_num,
    this.unit_price,
    this.unit_work_type,
    this.unit_work_type_name,
    this.resource_ext,
  });

  @override
  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (work_note != null) map["work_note"] = work_note!;
    if (business_type != null) map["business_type"] = business_type!;
    if (identity != null) map["identity"] = identity!;
    if (note != null) map["note"] = note!;
    if (img_url != null) map["img_url"] = img_url!;
    if (business_time != null) map["business_time"] = business_time!;
    if (money != null) map["money"] = money!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (unit_num != null) map["unit_num"] = unit_num!;
    if (unit_price != null) map["unit_price"] = unit_price!;
    if (unit_work_type != null) map["unit_work_type"] = unit_work_type!;
    if (unit_work_type_name != null) {
      map["unit_work_type_name"] = unit_work_type_name!;
    }
    if (user_choose_spotwork != null) map["action"] = user_choose_spotwork!;
    if (resource_ext != null) map["action"] = resourceExtToMap() as Object;
    return map;
  }

  List<Map<String, dynamic>>? resourceExtToMap() {
    return resource_ext?.map((e) => e.toMap()).toList() ?? [];
  }

  @override
  String toString() {
    return "MetrologyWorkRecordParamModel(business_time: $business_time, business_type: $business_type, img_url: $img_url, identity: $identity, money: $money, note: $note, work_note: $work_note,"
        " user_choose_spotwork: $user_choose_spotwork,unit_num: $unit_num, unit_price: $unit_price, unit_work_type: $unit_work_type, unit_work_type_name: $unit_work_type_name, worker_id: $worker_id, resource_ext: ${resource_ext.toString()})";
  }
}
