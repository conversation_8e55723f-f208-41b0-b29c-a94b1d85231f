import '../../../repo/model/report_create_task_biz_model.dart';

class ReportCreateTaskNetModel {
  bool? scalar;

  ReportCreateTaskNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (scalar != null) map["scalar"] = scalar!;
    return map;
  }

  factory ReportCreateTaskNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ReportCreateTaskNetModel();
    netModel.scalar = bool.tryParse(json["scalar"].toString());
    return netModel;
  }

  ReportCreateTaskBizModel transform() {
    return ReportCreateTaskBizModel(
      scalar: scalar ?? false,
    );
  }
}
