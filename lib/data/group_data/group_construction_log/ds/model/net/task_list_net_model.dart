import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/task_list_biz_model.dart';

class TaskListNetModel {

  /// 列表信息
  List<TaskListANetModel>? list;

  /// 通用分页信息
  PageNetModel? page;

  TaskListNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (list != null) map["list"] = list!;
    if (page != null) map["page"] = page!;
    return map;
  }

  factory TaskListNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = TaskListNetModel();
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) => TaskListANetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    netModel.page = json["page"] == null
      ? null
      : PageNetModel.fromJson(json["page"] as Map<String, dynamic>);
    return netModel;
  }

  TaskListBizModel transform() {
    return TaskListBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
      page: page?.transform(),
    );
  }
}

class TaskListANetModel {

  /// 任务ID，既task_id
  double? id;

  /// 记工本ID
  double? work_note;

  /// 显示标题
  String? title;

  /// 显示副标题
  String? subtitle;

  /// 任务类型 log施工日志
  String? process_type;

  /// 任务状态 waiting待处理 processing处理中 finished处理完成
  String? process_status;

  /// 任务状态显示文本
  String? process_status_text;

  /// 处理中信息
  ProcessDataNetModel? process_data;

  /// 处理完成文件信息
  FileDataNetModel? file_data;

  /// 冗余字段
  String? created_at;

  TaskListANetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (work_note != null) map["work_note"] = work_note!;
    if (title != null) map["title"] = title!;
    if (subtitle != null) map["subtitle"] = subtitle!;
    if (process_type != null) map["process_type"] = process_type!;
    if (process_status != null) map["process_status"] = process_status!;
    if (process_status_text != null) map["process_status_text"] = process_status_text!;
    if (process_data != null) map["process_data"] = process_data!;
    if (file_data != null) map["file_data"] = file_data!;
    if (created_at != null) map["created_at"] = created_at!;
    return map;
  }

  factory TaskListANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = TaskListANetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.work_note = double.tryParse(json["work_note"].toString());
    netModel.title = json["title"]?.toString();
    netModel.subtitle = json["subtitle"]?.toString();
    netModel.process_type = json["process_type"]?.toString();
    netModel.process_status = json["process_status"]?.toString();
    netModel.process_status_text = json["process_status_text"]?.toString();
    netModel.process_data = json["process_data"] == null
      ? null
      : ProcessDataNetModel.fromJson(json["process_data"] as Map<String, dynamic>);
    netModel.file_data = json["file_data"] == null
      ? null
      : FileDataNetModel.fromJson(json["file_data"] as Map<String, dynamic>);
    netModel.created_at = json["created_at"]?.toString();
    return netModel;
  }

  TaskListABizModel transform() {
    return TaskListABizModel(
      id: id ?? 0.0,
      workNote: work_note ?? 0.0,
      title: title ?? "",
      subtitle: subtitle ?? "",
      processType: process_type ?? "",
      processStatus: process_status ?? "",
      processStatusText: process_status_text ?? "",
      processData: process_data?.transform(),
      fileData: file_data?.transform(),
      createdAt: created_at ?? "",
    );
  }
}

class ProcessDataNetModel {

  /// 显示预估耗时
  String? estimate;

  /// 显示总计耗时
  String? cost;

  /// 显示当前进度百分比 0-100
  double? percent;

  ProcessDataNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (estimate != null) map["estimate"] = estimate!;
    if (cost != null) map["cost"] = cost!;
    if (percent != null) map["percent"] = percent!;
    return map;
  }

  factory ProcessDataNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ProcessDataNetModel();
    netModel.estimate = json["estimate"]?.toString();
    netModel.cost = json["cost"]?.toString();
    netModel.percent = double.tryParse(json["percent"].toString());
    return netModel;
  }

  ProcessDataBizModel transform() {
    return ProcessDataBizModel(
      estimate: estimate ?? "",
      cost: cost ?? "",
      percent: percent ?? 0.0,
    );
  }
}

class FileDataNetModel {

  /// 显示文件大小
  String? size;

  /// 显示文件扩展名
  String? extension;

  /// 显示文件名
  String? name;

  /// 下载链接
  String? url;

  FileDataNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (size != null) map["size"] = size!;
    if (extension != null) map["extension"] = extension!;
    if (name != null) map["name"] = name!;
    if (url != null) map["url"] = url!;
    return map;
  }

  factory FileDataNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = FileDataNetModel();
    netModel.size = json["size"]?.toString();
    netModel.extension = json["extension"]?.toString();
    netModel.name = json["name"]?.toString();
    netModel.url = json["url"]?.toString();
    return netModel;
  }

  FileDataBizModel transform() {
    return FileDataBizModel(
      size: size ?? "",
      extension: extension ?? "",
      name: name ?? "",
      url: url ?? "",
    );
  }
}

class PageNetModel {
  double? curPage;
  double? pageSize;
  bool? hasMore;

  PageNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (curPage != null) map["curPage"] = curPage!;
    if (pageSize != null) map["pageSize"] = pageSize!;
    if (hasMore != null) map["hasMore"] = hasMore!;
    return map;
  }

  factory PageNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = PageNetModel();
    netModel.curPage = double.tryParse(json["curPage"].toString());
    netModel.pageSize = double.tryParse(json["pageSize"].toString());
    netModel.hasMore = bool.tryParse(json["hasMore"].toString());
    return netModel;
  }

  PageBizModel transform() {
    return PageBizModel(
      curPage: curPage ?? 0.0,
      pageSize: pageSize ?? 0.0,
      hasMore: hasMore ?? false,
    );
  }
}
