import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/logs_create_biz_model.dart';

class LogsCreateNetModel {
  /// 创建成功返回的日志ID
  final double? log_id;

  LogsCreateNetModel({
    this.log_id,
  });

  factory LogsCreateNetModel.fromJson(Map<String, dynamic> json) {
    return LogsCreateNetModel(
      log_id: json['log_id']?.toDouble(),
    );
  }

  LogsCreateBizModel transform() {
    return LogsCreateBizModel(
      logId: log_id ?? 0.0,
    );
  }
}
