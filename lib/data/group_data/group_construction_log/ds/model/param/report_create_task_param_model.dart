class ReportCreateTaskParamModel {

  /// 班组ID
  final String? dept_id;

  /// 筛选开始时间
  final String? start_time;

  /// 筛选结束时间
  final String? end_time;

  /// 关键词搜索
  final String? keyword;

  /// 工人ID(作废)
  final String? user_ids;

  /// 指定ID下载
  final String? log_id;

  ReportCreateTaskParamModel({
    this.dept_id,
    this.start_time,
    this.end_time,
    this.keyword,
    this.user_ids,
    this.log_id,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (dept_id != null) map["dept_id"] = dept_id!;
    if (start_time != null) map["start_time"] = start_time!;
    if (end_time != null) map["end_time"] = end_time!;
    if (keyword != null) map["keyword"] = keyword!;
    if (user_ids != null) map["user_ids"] = user_ids!;
    if (log_id != null) map["log_id"] = log_id!;
    return map;
  }
}
