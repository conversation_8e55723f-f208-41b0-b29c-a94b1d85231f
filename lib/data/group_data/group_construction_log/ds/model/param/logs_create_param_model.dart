class LogsCreateParamModel {
  String? member_id;
  String? dept_id;
  String? dayweather;
  String? nightweather;
  String? daytemp;
  String? nighttemp;
  String? contents;
  String? edit_time;

  /// 传列表返回的worker_id
  String? recorder_user_id;

  /// 人数
  String? construction_peo_num;

  /// 图片URL
  String? img_url;

  /// 资源扩展
  List<Map<String, dynamic>>? resource_ext;

  /// 微信token
  String? wechat_token;

  LogsCreateParamModel({
    this.member_id,
    this.dept_id,
    this.dayweather,
    this.nightweather,
    this.daytemp,
    this.nighttemp,
    this.contents,
    this.edit_time,
    this.recorder_user_id,
    this.construction_peo_num,
    this.img_url,
    this.resource_ext,
    this.wechat_token,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    map["member_id"] = member_id ?? "";
    map["dept_id"] = dept_id ?? "";
    map["dayweather"] = dayweather ?? "";
    map["nightweather"] = nightweather ?? "";
    map["daytemp"] = daytemp ?? "";
    map["nighttemp"] = nighttemp ?? "";
    map["contents"] = contents ?? "";
    map["edit_time"] = edit_time ?? "";
    map["recorder_user_id"] = recorder_user_id ?? "";
    map["construction_peo_num"] = construction_peo_num ?? "";
    map["img_url"] = img_url ?? "";
    if (resource_ext != null) map["resource_ext"] = resource_ext!;
    map["wechat_token"] = wechat_token ?? "";
    return map;
  }
}
