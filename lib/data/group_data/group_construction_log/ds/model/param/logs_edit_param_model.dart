class LogsEditParamModel {

  /// 部门ID
  final double? dept_id;

  /// 白天天气
  final String? dayweather;

  /// 夜间天气
  final String? nightweather;

  /// 白天温度
  final String? daytemp;

  /// 夜间温度
  final String? nighttemp;

  /// 日志内容
  final String? contents;

  /// 编辑时间
  final String? edit_time;

  /// 日志ID
  final double? log_id;

  /// 图片URL
  final String? img_url;

  /// 资源扩展
  final List<Map<String, dynamic>>? resource_ext;

  /// 记录员用户ID
  final double? recorder_user_id;

  /// 施工人数
  final String? construction_peo_num;

  /// 微信token
  final String? wechat_token;

  LogsEditParamModel({
    this.dept_id,
    this.dayweather,
    this.nightweather,
    this.daytemp,
    this.nighttemp,
    this.contents,
    this.edit_time,
    this.log_id,
    this.img_url,
    this.resource_ext,
    this.recorder_user_id,
    this.construction_peo_num,
    this.wechat_token,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    map["dept_id"] = dept_id ?? 0.0;
    map["dayweather"] = dayweather ?? "";
    map["nightweather"] = nightweather ?? "";
    map["daytemp"] = daytemp ?? "";
    map["nighttemp"] = nighttemp ?? "";
    map["contents"] = contents ?? "";
    map["edit_time"] = edit_time ?? "";
    map["log_id"] = log_id ?? 0.0;
    map["img_url"] = img_url ?? "";
    if (resource_ext != null) map["resource_ext"] = resource_ext!;
    map["recorder_user_id"] = recorder_user_id ?? 0.0;
    map["construction_peo_num"] = construction_peo_num ?? "";
    map["wechat_token"] = wechat_token ?? "";
    return map;
  }
}
