import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/group_construction_log_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/log_history_lds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/report_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/logs_edit_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/logs_create_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/report_create_task_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/task_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/ds/model/param/task_delete_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/report_create_task_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/task_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/logs_create_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_construction_log/repo/model/logs_getdetail_biz_model.dart';

/// 施工日志仓库
class GroupConstructionLogRepo {
  final _groupConstructionLogRds = GroupConstructionLogRds();
  final _logHistoryLds = LogHistoryLds();

  /// 获取施工日志列表
  Future<RespResult<ReportNetModelBizModel>> getReportList({
    String page = "1",
    String startTime = "",
    String endTime = "",
    String keyword = "",
    String deptId = "",
  }) async {
    final params = ReportNetModelParamModel(
      page: page,
      per_page: "20",
      start_time: startTime,
      end_time: endTime,
      keyword: keyword,
      dept_id: deptId,
    );

    final result = await _groupConstructionLogRds.getReportList(params);
    return result.map((netModel) => netModel?.transform() ?? ReportNetModelBizModel());
  }

  /// 获取施工日志详情
  Future<RespResult<LogsGetdetailBizModel>> getLogDetail(double logId) async {
    final result = await _groupConstructionLogRds.getLogDetail(logId);
    return result.map((netModel) => netModel?.transform() ?? LogsGetdetailBizModel());
  }

  /// 删除施工日志
  Future<RespResult<bool>> deleteLog(double logId) async {
    final result = await _groupConstructionLogRds.deleteLog(logId);
    return result;
  }

  /// 编辑施工日志
  Future<RespResult<bool>> editLog(LogsEditParamModel params) async {
    final result = await _groupConstructionLogRds.editLog(params);
    return result;
  }

  /// 创建施工日志
  Future<RespResult<LogsCreateBizModel>> createLog(LogsCreateParamModel params) async {
    final result = await _groupConstructionLogRds.createLog(params);
    return result.map((netModel) => netModel?.transform() ?? LogsCreateBizModel());
  }

  /// 创建导出任务
  Future<RespResult<ReportCreateTaskBizModel>> createExportTask(
      ReportCreateTaskParamModel req,
      ) async {
    final result = await _groupConstructionLogRds.createExportTask(req);
    return result.map((netModel) => netModel?.transform() ?? ReportCreateTaskBizModel());
  }

  /// 获取任务列表
  Future<RespResult<TaskListBizModel>> getTaskList({
    String processType = "log",
    String page = "1",
    String perPage = "20",
    String? deptId,
  }) async {
    final params = TaskListParamModel(
      process_type: processType,
      page: page,
      per_page: perPage,
      dept_id: deptId,
    );

    final result = await _groupConstructionLogRds.getTaskList(params);
    return result.map((netModel) => netModel?.transform() ?? TaskListBizModel());
  }

  /// 删除任务
  Future<RespResult<bool>> deleteTask(List<int> taskIds) async {
    // 参数验证
    if (taskIds.isEmpty) {
      return RespFail.buildProcessFail("任务ID列表不能为空");
    }

    final params = TaskDeleteParamModel(task_ids: taskIds);
    final result = await _groupConstructionLogRds.deleteTask(params);
    return result;
  }

  // ==================== 日志内容历史记录相关方法 ====================

  /// 保存日志内容到历史记录
  /// [content] 要保存的日志内容
  void saveLogContent(String content) {
    if (content.trim().isEmpty) return;
    _logHistoryLds.addHistory(content.trim());
  }

  /// 获取历史日志内容列表
  List<String> getHistoryLogContents() {
    return _logHistoryLds.getHistoryList();
  }

  /// 删除指定的历史记录
  /// [content] 要删除的内容
  /// 返回删除后的历史记录列表
  List<String> removeHistoryLogContent(String content) {
    return _logHistoryLds.removeHistory(content);
  }
}
