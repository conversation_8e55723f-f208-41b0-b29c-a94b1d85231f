import '../../../repo/model/workers_get_worker_info_biz_model.dart';

class WorkersGetWorkerInfoNetModel {

  /// 
  double? id;

  /// 工人姓名
  String? name;

  /// 电话
  String? tel;

  /// 
  String? name_py;

  /// 
  String? name_color;

  /// 
  double? member_id;

  /// 
  double? is_deleted;

  /// 
  double? corp_id;

  /// 是否绑定
  double? is_bind;

  /// 真实姓名
  String? username;

  /// 是否允许在线查看记工数据1-允许 2-不允许
  double? is_show;

  /// 是否是班组长 0-不是 1-是
  double? is_self_created;

  /// 是否是带班 0-不是 1-是
  double? is_agent;

  /// 
  FeeStandardInfoNetModel? fee_standard_info;

  /// 
  ContractorFeeStandardInfoNetModel? contractor_fee_standard_info;

  /// 隐私信息
  GrantNetModel? grant;

  WorkersGetWorkerInfoNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (name != null) map["name"] = name!;
    if (tel != null) map["tel"] = tel!;
    if (name_py != null) map["name_py"] = name_py!;
    if (name_color != null) map["name_color"] = name_color!;
    if (member_id != null) map["member_id"] = member_id!;
    if (is_deleted != null) map["is_deleted"] = is_deleted!;
    if (corp_id != null) map["corp_id"] = corp_id!;
    if (is_bind != null) map["is_bind"] = is_bind!;
    if (username != null) map["username"] = username!;
    if (is_show != null) map["is_show"] = is_show!;
    if (is_self_created != null) map["is_self_created"] = is_self_created!;
    if (is_agent != null) map["is_agent"] = is_agent!;
    if (fee_standard_info != null) map["fee_standard_info"] = fee_standard_info!;
    if (contractor_fee_standard_info != null) map["contractor_fee_standard_info"] = contractor_fee_standard_info!;
    if (grant != null) map["grant"] = grant!;
    return map;
  }

  factory WorkersGetWorkerInfoNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = WorkersGetWorkerInfoNetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.tel = json["tel"]?.toString();
    netModel.name_py = json["name_py"]?.toString();
    netModel.name_color = json["name_color"]?.toString();
    netModel.member_id = double.tryParse(json["member_id"].toString());
    netModel.is_deleted = double.tryParse(json["is_deleted"].toString());
    netModel.corp_id = double.tryParse(json["corp_id"].toString());
    netModel.is_bind = double.tryParse(json["is_bind"].toString());
    netModel.username = json["username"]?.toString();
    netModel.is_show = double.tryParse(json["is_show"].toString());
    netModel.is_self_created = double.tryParse(json["is_self_created"].toString());
    netModel.is_agent = double.tryParse(json["is_agent"].toString());
    netModel.fee_standard_info = json["fee_standard_info"] == null
      ? null
      : FeeStandardInfoNetModel.fromJson(json["fee_standard_info"] as Map<String, dynamic>);
    netModel.contractor_fee_standard_info = json["contractor_fee_standard_info"] == null
      ? null
      : ContractorFeeStandardInfoNetModel.fromJson(json["contractor_fee_standard_info"] as Map<String, dynamic>);
    netModel.grant = json["grant"] == null
      ? null
      : GrantNetModel.fromJson(json["grant"] as Map<String, dynamic>);
    return netModel;
  }

  WorkersGetWorkerInfoBizModel transform() {
    return WorkersGetWorkerInfoBizModel(
      id: id ?? 0.0,
      name: name ?? "",
      tel: tel ?? "",
      namePy: name_py ?? "",
      nameColor: name_color ?? "",
      memberId: member_id ?? 0.0,
      isDeleted: is_deleted ?? 0.0,
      corpId: corp_id ?? 0.0,
      isBind: is_bind ?? 0.0,
      username: username ?? "",
      isShow: is_show ?? 0.0,
      isSelfCreated: is_self_created ?? 0.0,
      isAgent: is_agent ?? 0.0,
      feeStandardInfo: fee_standard_info?.transform(),
      contractorFeeStandardInfo: contractor_fee_standard_info?.transform(),
      grant: grant?.transform(),
    );
  }
}

class FeeStandardInfoNetModel {

  ///
  double? businessType;

  ///
  double? feeStandardId;

  ///
  String? overtimeHoursPrice;

  ///
  String? overtimeHoursStandard;

  ///
  double? overtimeType;

  ///
  String? workingHoursPrice;

  ///
  String? workingHoursStandard;

  FeeStandardInfoNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (businessType != null) map["businessType"] = businessType!;
    if (feeStandardId != null) map["feeStandardId"] = feeStandardId!;
    if (overtimeHoursPrice != null) map["overtimeHoursPrice"] = overtimeHoursPrice!;
    if (overtimeHoursStandard != null) map["overtimeHoursStandard"] = overtimeHoursStandard!;
    if (overtimeType != null) map["overtimeType"] = overtimeType!;
    if (workingHoursPrice != null) map["workingHoursPrice"] = workingHoursPrice!;
    if (workingHoursStandard != null) map["workingHoursStandard"] = workingHoursStandard!;
    return map;
  }

  factory FeeStandardInfoNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = FeeStandardInfoNetModel();
    netModel.businessType = double.tryParse(json["businessType"].toString());
    netModel.feeStandardId = double.tryParse(json["feeStandardId"].toString());
    netModel.overtimeHoursPrice = json["overtimeHoursPrice"]?.toString();
    netModel.overtimeHoursStandard = json["overtimeHoursStandard"]?.toString();
    netModel.overtimeType = double.tryParse(json["overtimeType"].toString());
    netModel.workingHoursPrice = json["workingHoursPrice"]?.toString();
    netModel.workingHoursStandard = json["workingHoursStandard"]?.toString();
    return netModel;
  }

  FeeStandardInfoBizModel transform() {
    return FeeStandardInfoBizModel(
      businessType: businessType ?? 0.0,
      feeStandardId: feeStandardId ?? 0.0,
      overtimeHoursPrice: overtimeHoursPrice ?? "",
      overtimeHoursStandard: overtimeHoursStandard ?? "",
      overtimeType: overtimeType ?? 0.0,
      workingHoursPrice: workingHoursPrice ?? "",
      workingHoursStandard: workingHoursStandard ?? "",
    );
  }
}

class ContractorFeeStandardInfoNetModel {

  ///
  double? feeStandardId;

  ///
  double? businessType;

  ///
  String? workingHoursPrice;

  ///
  String? workingHoursStandard;

  ///
  double? overtimeType;

  ///
  String? overtimeHoursPrice;

  ///
  String? overtimeHoursStandard;

  ContractorFeeStandardInfoNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (feeStandardId != null) map["feeStandardId"] = feeStandardId!;
    if (businessType != null) map["businessType"] = businessType!;
    if (workingHoursPrice != null) map["workingHoursPrice"] = workingHoursPrice!;
    if (workingHoursStandard != null) map["workingHoursStandard"] = workingHoursStandard!;
    if (overtimeType != null) map["overtimeType"] = overtimeType!;
    if (overtimeHoursPrice != null) map["overtimeHoursPrice"] = overtimeHoursPrice!;
    if (overtimeHoursStandard != null) map["overtimeHoursStandard"] = overtimeHoursStandard!;
    return map;
  }

  factory ContractorFeeStandardInfoNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ContractorFeeStandardInfoNetModel();
    netModel.feeStandardId = double.tryParse(json["feeStandardId"].toString());
    netModel.businessType = double.tryParse(json["businessType"].toString());
    netModel.workingHoursPrice = json["workingHoursPrice"]?.toString();
    netModel.workingHoursStandard = json["workingHoursStandard"]?.toString();
    netModel.overtimeType = double.tryParse(json["overtimeType"].toString());
    netModel.overtimeHoursPrice = json["overtimeHoursPrice"]?.toString();
    netModel.overtimeHoursStandard = json["overtimeHoursStandard"]?.toString();
    return netModel;
  }

  ContractorFeeStandardInfoBizModel transform() {
    return ContractorFeeStandardInfoBizModel(
      feeStandardId: feeStandardId ?? 0.0,
      businessType: businessType ?? 0.0,
      workingHoursPrice: workingHoursPrice ?? "",
      workingHoursStandard: workingHoursStandard ?? "",
      overtimeType: overtimeType ?? 0.0,
      overtimeHoursPrice: overtimeHoursPrice ?? "",
      overtimeHoursStandard: overtimeHoursStandard ?? "",
    );
  }
}

class GrantNetModel {

  /// 银行卡名字
  String? bank_name;

  /// 银行卡卡号
  String? bank_no;

  /// 身份证名字
  String? card_name;

  /// 身份证号
  String? card_no;

  /// 授权时间
  String? expired_time;

  /// ID
  double? grant_id;

  GrantNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (bank_name != null) map["bank_name"] = bank_name!;
    if (bank_no != null) map["bank_no"] = bank_no!;
    if (card_name != null) map["card_name"] = card_name!;
    if (card_no != null) map["card_no"] = card_no!;
    if (expired_time != null) map["expired_time"] = expired_time!;
    if (grant_id != null) map["grant_id"] = grant_id!;
    return map;
  }

  factory GrantNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = GrantNetModel();
    netModel.bank_name = json["bank_name"]?.toString();
    netModel.bank_no = json["bank_no"]?.toString();
    netModel.card_name = json["card_name"]?.toString();
    netModel.card_no = json["card_no"]?.toString();
    netModel.expired_time = json["expired_time"]?.toString();
    netModel.grant_id = double.tryParse(json["grant_id"].toString());
    return netModel;
  }

  GrantBizModel transform() {
    return GrantBizModel(
      bankName: bank_name ?? "",
      bankNo: bank_no ?? "",
      cardName: card_name ?? "",
      cardNo: card_no ?? "",
      expiredTime: expired_time ?? "",
      grantId: grant_id ?? 0.0,
    );
  }
}
