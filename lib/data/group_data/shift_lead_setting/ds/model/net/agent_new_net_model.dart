import '../../../repo/model/agent_new_biz_model.dart';

class AgentNewNetModelNetModel {

  /// 
  List<AgentNewNetModelANetModel>? list;

  AgentNewNetModelNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (list != null) map["list"] = list!;
    return map;
  }

  factory AgentNewNetModelNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = AgentNewNetModelNetModel();
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) => AgentNewNetModelANetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  AgentNewNetModelBizModel transform() {
    return AgentNewNetModelBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }
}

class AgentNewNetModelANetModel {

  /// 
  double? id;

  /// 
  String? name;

  /// 
  String? name_color;

  /// 
  String? name_py;

  /// 
  String? tel;

  AgentNewNetModelANetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (name != null) map["name"] = name!;
    if (name_color != null) map["name_color"] = name_color!;
    if (name_py != null) map["name_py"] = name_py!;
    if (tel != null) map["tel"] = tel!;
    return map;
  }

  factory AgentNewNetModelANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = AgentNewNetModelANetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.name_color = json["name_color"]?.toString();
    netModel.name_py = json["name_py"]?.toString();
    netModel.tel = json["tel"]?.toString();
    return netModel;
  }

  AgentNewNetModelABizModel transform() {
    return AgentNewNetModelABizModel(
      id: id ?? 0.0,
      name: name ?? "",
      nameColor: name_color ?? "",
      namePy: name_py ?? "",
      tel: tel ?? "",
    );
  }
}
