import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import '../ds/shift_lead_setting_rds.dart';
import '../ds/param/agent_new_cancel_param_model.dart';
import '../ds/param/workers_get_worker_info_param_model.dart';
import 'model/agent_new_biz_model.dart';
import 'model/agent_new_cancel_biz_model.dart';
import 'model/workers_get_worker_info_biz_model.dart';

class ShiftLeadSettingRepo {
  final _rds = ShiftLeadSettingRds();

  Future<RespResult<AgentNewNetModelBizModel>> fetchShiftLeadWorkers(String deptId) async {
    final resp = await _rds.fetchShiftLeadWorkers(deptId);
    return resp.map((netModel) => netModel?.transform() ?? AgentNewNetModelBizModel());
  }

  /// 删除带班权限
  Future<RespResult<AgentNewCancelBizModel>> cancelShiftLeadWorkers(String deptId, List<double> workerIds) async {
    final workerIdsStr = workerIds.map((id) => id.toInt().toString()).join(',');
    final params = AgentNewCancelParamModel(
      dept_id: deptId,
      worker_ids: workerIdsStr,
    );
    final resp = await _rds.cancelShiftLeadWorkers(params);
    return resp.map((netModel) => netModel?.transform() ?? AgentNewCancelBizModel());
  }

  /// 获取工友详细信息
  Future<RespResult<WorkersGetWorkerInfoBizModel>> fetchWorkerInfo(String workerId, String deptId) async {
    final params = WorkersGetWorkerInfoParamModel(
      worker_id: workerId,
      dept_id: deptId,
    );
    final resp = await _rds.fetchWorkerInfo(params);
    return resp.map((netModel) => netModel?.transform() ?? WorkersGetWorkerInfoBizModel());
  }
}