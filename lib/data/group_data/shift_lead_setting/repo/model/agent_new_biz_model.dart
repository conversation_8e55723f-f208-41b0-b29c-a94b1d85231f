class AgentNewNetModelBizModel {

  /// 
  List<AgentNewNetModelABizModel> list;

  AgentNewNetModelBizModel({
    this.list = const [],
  });

  @override
  String toString() {
    return "AgentNewNetModelBizModel(list: ${list.toString()})";
  }
}

class AgentNewNetModelABizModel {

  /// 
  double id;

  /// 
  String name;

  /// 
  String nameColor;

  /// 
  String namePy;

  /// 
  String tel;

  AgentNewNetModelABizModel({
    this.id = 0.0,
    this.name = "",
    this.nameColor = "",
    this.namePy = "",
    this.tel = "",
  });

  @override
  String toString() {
    return "AgentNewNetModelABizModel(id: $id, name: $name, nameColor: $nameColor, namePy: $namePy, tel: $tel)";
  }
}
