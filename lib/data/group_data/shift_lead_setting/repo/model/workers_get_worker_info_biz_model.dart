class WorkersGetWorkerInfoBizModel {

  /// 
  double id;

  /// 工人姓名
  String name;

  /// 电话
  String tel;

  /// 
  String namePy;

  /// 
  String nameColor;

  /// 
  double memberId;

  /// 
  double isDeleted;

  /// 
  double corpId;

  /// 是否绑定
  double isBind;

  /// 真实姓名
  String username;

  /// 是否允许在线查看记工数据1-允许 2-不允许
  double isShow;

  /// 是否是班组长 0-不是 1-是
  double isSelfCreated;

  /// 是否是带班 0-不是 1-是
  double isAgent;

  /// 
  FeeStandardInfoBizModel? feeStandardInfo;

  /// 
  ContractorFeeStandardInfoBizModel? contractorFeeStandardInfo;

  /// 隐私信息
  GrantBizModel? grant;

  WorkersGetWorkerInfoBizModel({
    this.id = 0.0,
    this.name = "",
    this.tel = "",
    this.namePy = "",
    this.nameColor = "",
    this.memberId = 0.0,
    this.isDeleted = 0.0,
    this.corpId = 0.0,
    this.isBind = 0.0,
    this.username = "",
    this.isShow = 0.0,
    this.isSelfCreated = 0.0,
    this.isAgent = 0.0,
    this.feeStandardInfo,
    this.contractorFeeStandardInfo,
    this.grant,
  });

  @override
  String toString() {
    return "WorkersGetWorkerInfoBizModel(id: $id, name: $name, tel: $tel, namePy: $namePy, nameColor: $nameColor, memberId: $memberId, isDeleted: $isDeleted, corpId: $corpId, isBind: $isBind, username: $username, isShow: $isShow, isSelfCreated: $isSelfCreated, isAgent: $isAgent, feeStandardInfo: ${feeStandardInfo.toString()}, contractorFeeStandardInfo: ${contractorFeeStandardInfo.toString()}, grant: ${grant.toString()})";
  }
}

class FeeStandardInfoBizModel {

  /// 
  double businessType;

  /// 
  double feeStandardId;

  /// 
  String overtimeHoursPrice;

  /// 
  String overtimeHoursStandard;

  /// 
  double overtimeType;

  /// 
  String workingHoursPrice;

  /// 
  String workingHoursStandard;

  FeeStandardInfoBizModel({
    this.businessType = 0.0,
    this.feeStandardId = 0.0,
    this.overtimeHoursPrice = "",
    this.overtimeHoursStandard = "",
    this.overtimeType = 0.0,
    this.workingHoursPrice = "",
    this.workingHoursStandard = "",
  });

  @override
  String toString() {
    return "FeeStandardInfoBizModel(businessType: $businessType, feeStandardId: $feeStandardId, overtimeHoursPrice: $overtimeHoursPrice, overtimeHoursStandard: $overtimeHoursStandard, overtimeType: $overtimeType, workingHoursPrice: $workingHoursPrice, workingHoursStandard: $workingHoursStandard)";
  }
}

class ContractorFeeStandardInfoBizModel {

  /// 
  double feeStandardId;

  /// 
  double businessType;

  /// 
  String workingHoursPrice;

  /// 
  String workingHoursStandard;

  /// 
  double overtimeType;

  /// 
  String overtimeHoursPrice;

  /// 
  String overtimeHoursStandard;

  ContractorFeeStandardInfoBizModel({
    this.feeStandardId = 0.0,
    this.businessType = 0.0,
    this.workingHoursPrice = "",
    this.workingHoursStandard = "",
    this.overtimeType = 0.0,
    this.overtimeHoursPrice = "",
    this.overtimeHoursStandard = "",
  });

  @override
  String toString() {
    return "ContractorFeeStandardInfoBizModel(feeStandardId: $feeStandardId, businessType: $businessType, workingHoursPrice: $workingHoursPrice, workingHoursStandard: $workingHoursStandard, overtimeType: $overtimeType, overtimeHoursPrice: $overtimeHoursPrice, overtimeHoursStandard: $overtimeHoursStandard)";
  }
}

class GrantBizModel {

  /// 银行卡名字
  String bankName;

  /// 银行卡卡号
  String bankNo;

  /// 身份证名字
  String cardName;

  /// 身份证号
  String cardNo;

  /// 授权时间
  String expiredTime;

  /// ID
  double grantId;

  GrantBizModel({
    this.bankName = "",
    this.bankNo = "",
    this.cardName = "",
    this.cardNo = "",
    this.expiredTime = "",
    this.grantId = 0.0,
  });

  @override
  String toString() {
    return "GrantBizModel(bankName: $bankName, bankNo: $bankNo, cardName: $cardName, cardNo: $cardNo, expiredTime: $expiredTime, grantId: $grantId)";
  }
}
