import 'package:gdjg_pure_flutter/data/group_data/download_workers_material/repo/model/download_workers_biz_model.dart';

/// 下载工友资料网络模型
class DownloadWorkersNetModel {
  /// 下载链接
  String? url;
  
  /// 文件名
  String? filename;
  
  /// 文件大小
  String? filesize;

  DownloadWorkersNetModel();

  Map<String, dynamic> toJson(DownloadWorkersNetModel instance) => <String, dynamic>{
    "url": instance.url,
    "filename": instance.filename,
    "filesize": instance.filesize,
  };

  factory DownloadWorkersNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = DownloadWorkersNetModel();
    netModel.url = json["url"]?.toString();
    netModel.filename = json["file_name"]?.toString();
    netModel.filesize = json["filesize"]?.toString();
    return netModel;
  }

  DownloadWorkersBizModel transform() {
    return DownloadWorkersBizModel(
      url: url ?? "",
      filename: filename ?? "",
      filesize: filesize ?? "",
    );
  }
}
