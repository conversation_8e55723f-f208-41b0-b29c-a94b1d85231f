import 'package:gdjg_pure_flutter/data/occupation/model/v3_tree_query_net_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class OccRds {
  /// 获取工种列表数据
  Future<RespResult<V3TreeQueryNetModel>> queryOccupationList() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/occupation/list/get',
            method: HTTP_METHOD.GET,
            content: const {},
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (v) => V3TreeQueryNetModel.fromJson(v));
  }
}
