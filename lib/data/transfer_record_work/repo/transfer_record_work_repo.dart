import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/param/code_get_param_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/param/migrate_apply_param_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/param/migrate_confirm_param_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/transfer_record_work_rds.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/repo/model/migrate_apply_biz_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/repo/model/migrate_confirm_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class TransferRecordWorkRepo {
  final _transferRecordWorkRds = TransferRecordWorkRds();

  /// 获取验证
  Future<RespResult<dynamic>> getCode(CodeGetParamModel params) async {
    return await _transferRecordWorkRds.getCode(params);
  }

  /// 申请转移记工
  Future<RespResult<MigrateApplyBizModel?>> applyMigrate(MigrateApplyParamModel params) async {
    final result = await _transferRecordWorkRds.applyMigrate(params);
    return result.map((result) => result?.transform());
  }

  /// 确认转移记工
  Future<RespResult<MigrateConfirmBizModel?>> confirmMigrate(MigrateConfirmParamModel params) async {
    final result = await _transferRecordWorkRds.confirmMigrate(params);
    return result.map((result) => result?.transform());
  }
}
