import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/net/migrate_apply_net_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/net/migrate_confirm_net_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/param/code_get_param_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/param/migrate_apply_param_model.dart';
import 'package:gdjg_pure_flutter/data/transfer_record_work/ds/model/param/migrate_confirm_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

/// 转移记工相关
class TransferRecordWorkRds {
  /// 获取验证码
  Future<RespResult<dynamic>> getCode(CodeGetParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/code/get',
            method: HTTP_METHOD.GET,
            content: param.toMap().cast()),
        (json) => json);
  }

  /// 申请转移记工
  Future<RespResult<MigrateApplyNetModel>> applyMigrate(
      MigrateApplyParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/member/migrate/apply',
            method: HTTP_METHOD.POST,
            content: param.toMap().cast()),
        (json) => MigrateApplyNetModel.fromJson(json));
  }
  /// 确认转移记工
  Future<RespResult<MigrateConfirmNetModel>> confirmMigrate(
      MigrateConfirmParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/member/migrate/confirm',
            method: HTTP_METHOD.POST,
            content: param.toMap().cast()),
        (json) => MigrateConfirmNetModel.fromJson(json));
  }
}
