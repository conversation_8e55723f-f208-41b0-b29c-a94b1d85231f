class BusinessGetRecycleBinParamModel {

  /// 多个项目ID,隔开
  final String? work_notes;

  /// 格式 2022-08-24
  final String? start_time;

  /// 格式 2022-08-24
  final String? end_time;

  /// 多个类型用,隔开
  final String? business_type;

  /// 当前页
  final String? page;

  /// 每页大小
  final String? limit;

  BusinessGetRecycleBinParamModel({
    this.work_notes,
    this.start_time,
    this.end_time,
    this.business_type,
    this.page,
    this.limit,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (work_notes != null) map["work_notes"] = work_notes!;
    if (start_time != null) map["start_time"] = start_time!;
    if (end_time != null) map["end_time"] = end_time!;
    if (business_type != null) map["business_type"] = business_type!;
    if (page != null) map["page"] = page!;
    if (limit != null) map["limit"] = limit!;
    return map;
  }
}

