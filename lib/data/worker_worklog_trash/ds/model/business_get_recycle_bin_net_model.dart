import '../../repo/model/business_get_recycle_bin_biz_model.dart';

class BusinessGetRecycleBinNetModel {
  List<BusinessGetRecycleBinANetModel>? list;

  BusinessGetRecycleBinNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (list != null) map["list"] = list!;
    return map;
  }

  factory BusinessGetRecycleBinNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetRecycleBinNetModel();
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) =>
            BusinessGetRecycleBinANetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  BusinessGetRecycleBinBizModel transform() {
    return BusinessGetRecycleBinBizModel(
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }
}

class BusinessGetRecycleBinANetModel {
  String? date;
  List<BusinessGetRecycleBinBNetModel>? list;

  BusinessGetRecycleBinANetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (date != null) map["date"] = date!;
    if (list != null) map["list"] = list!;
    return map;
  }

  factory BusinessGetRecycleBinANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetRecycleBinANetModel();
    netModel.date = json["date"]?.toString();
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) =>
            BusinessGetRecycleBinBNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  BusinessGetRecycleBinABizModel transform() {
    return BusinessGetRecycleBinABizModel(
      date: date ?? "",
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }
}

class BusinessGetRecycleBinBNetModel {
  int? id;
  int? business_type;
  String? business_time;
  double? work_note;
  double? worker_id;
  double? unit_work_type;
  double? work_time;
  double? work_time_hour;
  double? overtime;
  double? overtime_work;
  double? morning_work_time;
  double? morning_work_time_hour;
  double? afternoon_work_time;
  double? afternoon_work_time_hour;
  double? user_choose_spotwork;
  String? unit_num;
  String? unit;
  String? note;
  double? fee_money;
  double? created_time;
  double? fee_standard_id;
  String? work_note_name;
  String? worker_name;
  String? unit_work_type_name;
  String? unit_work_type_unit;
  String? money;
  double? has_img;
  double? has_video;
  double? is_share_single;
  String? other_expenses;

  BusinessGetRecycleBinBNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (business_type != null) map["business_type"] = business_type!;
    if (business_time != null) map["business_time"] = business_time!;
    if (work_note != null) map["work_note"] = work_note!;
    if (worker_id != null) map["worker_id"] = worker_id!;
    if (unit_work_type != null) map["unit_work_type"] = unit_work_type!;
    if (work_time != null) map["work_time"] = work_time!;
    if (work_time_hour != null) map["work_time_hour"] = work_time_hour!;
    if (overtime != null) map["overtime"] = overtime!;
    if (overtime_work != null) map["overtime_work"] = overtime_work!;
    if (morning_work_time != null)
      map["morning_work_time"] = morning_work_time!;
    if (morning_work_time_hour != null)
      map["morning_work_time_hour"] = morning_work_time_hour!;
    if (afternoon_work_time != null)
      map["afternoon_work_time"] = afternoon_work_time!;
    if (afternoon_work_time_hour != null)
      map["afternoon_work_time_hour"] = afternoon_work_time_hour!;
    if (user_choose_spotwork != null)
      map["user_choose_spotwork"] = user_choose_spotwork!;
    if (unit_num != null) map["unit_num"] = unit_num!;
    if (unit != null) map["unit"] = unit!;
    if (note != null) map["note"] = note!;
    if (fee_money != null) map["fee_money"] = fee_money!;
    if (created_time != null) map["created_time"] = created_time!;
    if (fee_standard_id != null) map["fee_standard_id"] = fee_standard_id!;
    if (work_note_name != null) map["work_note_name"] = work_note_name!;
    if (worker_name != null) map["worker_name"] = worker_name!;
    if (unit_work_type_name != null)
      map["unit_work_type_name"] = unit_work_type_name!;
    if (unit_work_type_unit != null)
      map["unit_work_type_unit"] = unit_work_type_unit!;
    if (money != null) map["money"] = money!;
    if (has_img != null) map["has_img"] = has_img!;
    if (has_video != null) map["has_video"] = has_video!;
    if (is_share_single != null) map["is_share_single"] = is_share_single!;
    if (other_expenses != null) map["other_expenses"] = other_expenses!;
    return map;
  }

  factory BusinessGetRecycleBinBNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetRecycleBinBNetModel();
    netModel.id = int.tryParse(json["id"].toString());
    netModel.business_type = int.tryParse(json["business_type"].toString());
    netModel.business_time = json["business_time"]?.toString();
    netModel.work_note = double.tryParse(json["work_note"].toString());
    netModel.worker_id = double.tryParse(json["worker_id"].toString());
    netModel.unit_work_type =
        double.tryParse(json["unit_work_type"].toString());
    netModel.work_time = double.tryParse(json["work_time"].toString());
    netModel.work_time_hour =
        double.tryParse(json["work_time_hour"].toString());
    netModel.overtime = double.tryParse(json["overtime"].toString());
    netModel.overtime_work = double.tryParse(json["overtime_work"].toString());
    netModel.morning_work_time =
        double.tryParse(json["morning_work_time"].toString());
    netModel.morning_work_time_hour =
        double.tryParse(json["morning_work_time_hour"].toString());
    netModel.afternoon_work_time =
        double.tryParse(json["afternoon_work_time"].toString());
    netModel.afternoon_work_time_hour =
        double.tryParse(json["afternoon_work_time_hour"].toString());
    netModel.user_choose_spotwork =
        double.tryParse(json["user_choose_spotwork"].toString());
    netModel.unit_num = json["unit_num"]?.toString();
    netModel.unit = json["unit"]?.toString();
    netModel.note = json["note"]?.toString();
    netModel.fee_money = double.tryParse(json["fee_money"].toString());
    netModel.created_time = double.tryParse(json["created_time"].toString());
    netModel.fee_standard_id =
        double.tryParse(json["fee_standard_id"].toString());
    netModel.work_note_name = json["work_note_name"]?.toString();
    netModel.worker_name = json["worker_name"]?.toString();
    netModel.unit_work_type_name = json["unit_work_type_name"]?.toString();
    netModel.unit_work_type_unit = json["unit_work_type_unit"]?.toString();
    netModel.money = json["money"]?.toString();
    netModel.has_img = double.tryParse(json["has_img"].toString());
    netModel.has_video = double.tryParse(json["has_video"].toString());
    netModel.is_share_single =
        double.tryParse(json["is_share_single"].toString());
    netModel.other_expenses = json["other_expenses"]?.toString();
    return netModel;
  }

  BusinessGetRecycleBinBBizModel transform() {
    return BusinessGetRecycleBinBBizModel(
      id: id ?? 0,
      businessType: business_type ?? 0,
      businessTime: business_time ?? "",
      workNote: work_note ?? 0.0,
      workerId: worker_id ?? 0.0,
      unitWorkType: unit_work_type ?? 0.0,
      workTime: work_time,
      workTimeHour: work_time_hour,
      overtime: overtime,
      overtimeWork: overtime_work,
      morningWorkTime: morning_work_time,
      morningWorkTimeHour: morning_work_time_hour,
      afternoonWorkTime: afternoon_work_time,
      afternoonWorkTimeHour: afternoon_work_time_hour,
      userChooseSpotwork: user_choose_spotwork ?? 0.0,
      unitNum: unit_num ?? "",
      unit: unit ?? "",
      note: note ?? "",
      feeMoney: fee_money ?? 0.0,
      createdTime: created_time ?? 0.0,
      feeStandardId: fee_standard_id ?? 0.0,
      workNoteName: work_note_name ?? "",
      workerName: worker_name ?? "",
      unitWorkTypeName: unit_work_type_name ?? "",
      unitWorkTypeUnit: unit_work_type_unit ?? "",
      money: money ?? "",
      hasImg: has_img ?? 0.0,
      hasVideo: has_video ?? 0.0,
      isShareSingle: is_share_single ?? 0.0,
      otherExpenses: other_expenses ?? "",
    );
  }
}
