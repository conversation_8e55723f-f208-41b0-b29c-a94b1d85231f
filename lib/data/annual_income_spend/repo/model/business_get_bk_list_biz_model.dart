class BusinessGetBkListBizModel {
  List<BusinessGetBkListABizModel> list;

  BusinessGetBkListBizModel({
    this.list = const [],
  });
}

class BusinessGetBkListABizModel {
  double month;
  List<BusinessGetBkListBBizModel> list;

  BusinessGetBkListABizModel({
    this.month = 0.0,
    this.list = const [],
  });
}

class BusinessGetBkListBBizModel {
  String day;
  List<BusinessGetBkListCBizModel> list;
  String income;
  String expend;

  BusinessGetBkListBBizModel({
    this.day = "",
    this.list = const [],
    this.income = "",
    this.expend = "",
  });
}

class BusinessGetBkListCBizModel {
  double id;
  double workNote;
  double groupLeader;
  double workerId;
  double bookkeepingType;
  double businessType;
  double bookkeepingSource;
  double realMoney;
  String note;
  double businessTime;
  double identity;
  String money;
  String workerName;
  double hasImg;
  dynamic leaderName;
  String sourceName;
  List<ChooseImgBizModel> chooseImg;
  List<NoChooseImgBizModel> noChooseImg;
  dynamic workNoteName;

  BusinessGetBkListCBizModel({
    this.id = 0.0,
    this.workNote = 0.0,
    this.groupLeader = 0.0,
    this.workerId = 0.0,
    this.bookkeepingType = 0.0,
    this.businessType = 0.0,
    this.bookkeepingSource = 0.0,
    this.realMoney = 0.0,
    this.note = "",
    this.businessTime = 0.0,
    this.identity = 0.0,
    this.money = "",
    this.workerName = "",
    this.hasImg = 0.0,
    this.leaderName = "",
    this.sourceName = "",
    this.chooseImg = const [],
    this.noChooseImg = const [],
    this.workNoteName = "",
  });
}

class ChooseImgBizModel {
  String url;
  String type;
  String baseUrl;

  ChooseImgBizModel({
    this.url = "",
    this.type = "",
    this.baseUrl = "",
  });
}

class NoChooseImgBizModel {
  String url;
  String type;
  String baseUrl;

  NoChooseImgBizModel({
    this.url = "",
    this.type = "",
    this.baseUrl = "",
  });
}

