
import '../../../repo/model/business_get_bk_index_biz_model.dart';

class BusinessGetBkIndexNetModel {
  AllNetModel? all;
  List<BusinessGetBkIndexANetModel>? list;

  BusinessGetBkIndexNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (all != null) map["all"] = all!;
    if (list != null) map["list"] = list!;
    return map;
  }

  factory BusinessGetBkIndexNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetBkIndexNetModel();
    netModel.all = json["all"] == null
      ? null
      : AllNetModel.fromJson(json["all"] as Map<String, dynamic>);
    netModel.list = (json["list"] as List<dynamic>?)
        ?.map((e) => BusinessGetBkIndexANetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  BusinessGetBkIndexBizModel transform() {
    return BusinessGetBkIndexBizModel(
      all: all?.transform(),
      list: list?.map((e) => e.transform()).toList() ?? [],
    );
  }
}

class AllNetModel {
  String? income;
  String? expend;

  AllNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (income != null) map["income"] = income!;
    if (expend != null) map["expend"] = expend!;
    return map;
  }

  factory AllNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = AllNetModel();
    netModel.income = json["income"]?.toString();
    netModel.expend = json["expend"]?.toString();
    return netModel;
  }

  AllBizModel transform() {
    return AllBizModel(
      income: income ?? "",
      expend: expend ?? "",
    );
  }
}

class BusinessGetBkIndexANetModel {
  double? month;
  String? income;
  String? expend;
  double? has_bookkeeping;
  List<UrlNetModel>? url;

  BusinessGetBkIndexANetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (month != null) map["month"] = month!;
    if (income != null) map["income"] = income!;
    if (expend != null) map["expend"] = expend!;
    if (has_bookkeeping != null) map["has_bookkeeping"] = has_bookkeeping!;
    if (url != null) map["url"] = url!;
    return map;
  }

  factory BusinessGetBkIndexANetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGetBkIndexANetModel();
    netModel.month = double.tryParse(json["month"].toString());
    netModel.income = json["income"]?.toString();
    netModel.expend = json["expend"]?.toString();
    netModel.has_bookkeeping = double.tryParse(json["has_bookkeeping"].toString());
    netModel.url = (json["url"] as List<dynamic>?)
        ?.map((e) => UrlNetModel.fromJson(e as Map<String, dynamic>))
        .toList();
    return netModel;
  }

  BusinessGetBkIndexABizModel transform() {
    return BusinessGetBkIndexABizModel(
      month: month ?? 0.0,
      income: income ?? "",
      expend: expend ?? "",
      hasBookkeeping: has_bookkeeping ?? 0.0,
      url: url?.map((e) => e.transform()).toList() ?? [],
    );
  }
}

class UrlNetModel {
  String? url;
  String? type;
  String? base_url;

  UrlNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (url != null) map["url"] = url!;
    if (type != null) map["type"] = type!;
    if (base_url != null) map["base_url"] = base_url!;
    return map;
  }

  factory UrlNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = UrlNetModel();
    netModel.url = json["url"]?.toString();
    netModel.type = json["type"]?.toString();
    netModel.base_url = json["base_url"]?.toString();
    return netModel;
  }

  UrlBizModel transform() {
    return UrlBizModel(
      url: url ?? "",
      type: type ?? "",
      baseUrl: base_url ?? "",
    );
  }
}

