class BusinessGetBookkeepingExcelCodeParamModel {

  /// 开始时间 格式 2023-01-01 默认当月一号
  final String? start_date;

  /// 结束时间 格式 2023-01-31 默认当天
  final String? end_date;

  /// 项目 多个项目用,隔开
  final String? work_note;

  BusinessGetBookkeepingExcelCodeParamModel({
    this.start_date,
    this.end_date,
    this.work_note,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (start_date != null) map["start_date"] = start_date!;
    if (end_date != null) map["end_date"] = end_date!;
    if (work_note != null) map["work_note"] = work_note!;
    return map;
  }
}

