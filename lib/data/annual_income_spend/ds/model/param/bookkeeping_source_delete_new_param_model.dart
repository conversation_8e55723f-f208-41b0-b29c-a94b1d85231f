class BookkeepingSourceDeleteNewParamModel {

  /// 
  final int? id;

  /// 
  final int? fix_id;

  /// 
  final int? type;

  BookkeepingSourceDeleteNewParamModel({
    this.id,
    this.fix_id,
    this.type,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (fix_id != null) map["fix_id"] = fix_id!;
    if (type != null) map["type"] = type!;
    return map;
  }
}

