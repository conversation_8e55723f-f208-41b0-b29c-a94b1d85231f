class BookkeepingSourceAddUserSourceParamModel {

  /// 
  final String? name;

  /// 1 收入 2支出
  final int? type;

  /// 图标id
  final int? choose_img_id;

  BookkeepingSourceAddUserSourceParamModel({
    this.name,
    this.type,
    this.choose_img_id,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (name != null) map["name"] = name!;
    if (type != null) map["type"] = type!;
    if (choose_img_id != null) map["choose_img_id"] = choose_img_id!;
    return map;
  }
}

