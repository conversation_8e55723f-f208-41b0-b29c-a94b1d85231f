import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';
import '../../group_data/group_settlement/ds/model/param/group_settlement_param_model.dart';
import 'model/net/bookkeeping_get_bk_year_net_model.dart';
import 'model/net/bookkeeping_source_add_user_source_net_model.dart';
import 'model/net/bookkeeping_source_get_source_list_net_model.dart';
import 'model/net/bookkeeping_source_get_type_source_net_model.dart';
import 'model/net/bookkeeping_source_get_user_source_img_net_model.dart';
import 'model/net/business_get_bk_index_net_model.dart';
import 'model/net/business_get_bk_list_net_model.dart';
import 'model/net/business_get_bookkeeping_excel_code_net_model.dart';
import 'model/net/dept_get_dept_net_model.dart';
import 'model/net/work_notes_get_bk_work_note_net_model.dart';
import 'model/param/bookkeeping_source_add_user_source_param_model.dart';
import 'model/param/bookkeeping_source_delete_new_param_model.dart';
import 'model/param/business_get_bk_index_param_model.dart';
import 'model/param/business_get_bk_list_param_model.dart';
import 'model/param/business_get_bookkeeping_excel_code_param_model.dart';

class AutoIncomeSpendRds {
  ///获取在线记账表地址
  Future<RespResult<BusinessGetBookkeepingExcelCodeNetModel>>
      getBookkeepingExcelCode(
          BusinessGetBookkeepingExcelCodeParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/business/get_bookkeeping_excel_code',
            method: HTTP_METHOD.GET,
            content: param.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => BusinessGetBookkeepingExcelCodeNetModel.fromJson(json));
  }

  ///获取记账年
  Future<RespResult<BookkeepingGetBkYearNetModel>> getBookYear() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/bookkeeping/get-bk-year',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true)),
        (json) => BookkeepingGetBkYearNetModel.fromJson(json));
  }

  ///在记账本中获取班组本列表(项目列表)
  Future<RespResult<WorkNotesGetBkWorkNoteNetModel>> getProList() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/work-notes/get-bk-work-note',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true)),
        (json) => WorkNotesGetBkWorkNoteNetModel.fromJson(json));
  }

  ///获取收入支出类型列表
  Future<RespResult<BookkeepingSourceGetTypeSourceNetModel>> getTypeList(
      int type) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/bookkeeping-source/get-type-source?type=$type',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true)),
        (json) => BookkeepingSourceGetTypeSourceNetModel.fromJson(json));
  }

  ///获取我创建的项目列表
  Future<RespResult<DeptGetDeptNetModel>> fetchDeptList() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/dept/get-dept?type=created',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true)),
        (json) => DeptGetDeptNetModel.fromJson(json));
  }

  ///获取记工统计-年
  Future<RespResult<BusinessGetBkIndexNetModel>> getBkIndexOfYear(BusinessGetBkIndexParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/get-bk-index',
            method: HTTP_METHOD.GET,
            content: param.toMap(),
            requestExtra: RequestExtra(showLoading: false)),
        (json) => BusinessGetBkIndexNetModel.fromJson(json));
  }

  ///获取记工统计-月(包含日)
  Future<RespResult<BusinessGetBkListNetModel>> getBkIndexOfMonth(BusinessGetBkListParamModel  param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/get-bk-list',
            method: HTTP_METHOD.GET,
            content: param.toMap(),
            requestExtra: RequestExtra(showLoading: false)),
            (json) => BusinessGetBkListNetModel.fromJson(json));
  }
  ///获取全部类型(含已删除预设类型)
  Future<RespResult<BookkeepingSourceGetSourceListNetModel>> getSourceList() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/bookkeeping-source/get-source-list',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true)),
            (json) => BookkeepingSourceGetSourceListNetModel.fromJson(json));
  }
  ///获取自定义类型可选图标
  Future<RespResult<BookkeepingSourceGetUserSourceImgNetModel>> getUserSourceImg(int type) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/bookkeeping-source/get-user-source-img?type=$type',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true)),
            (json) => BookkeepingSourceGetUserSourceImgNetModel.fromJson(json));
  }

  ///添加自定义类型
  Future<RespResult<BookkeepingSourceAddUserSourceNetModel>> addUserSource(BookkeepingSourceAddUserSourceParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/bookkeeping-source/add-user-source',
            method: HTTP_METHOD.POST,
            content: param.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
            (json) => BookkeepingSourceAddUserSourceNetModel.fromJson(json));
  }
  ///更多分类-添加
  Future<RespResult<Object?>> recoverUserSource(BookkeepingSourceDeleteNewParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/bookkeeping-source/recover',
            method: HTTP_METHOD.POST,
            content: param.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
            (json) => Object());
  }
  ///更多分类-删除
  Future<RespResult<Object?>> deleteUserSource(BookkeepingSourceDeleteNewParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/bookkeeping-source/delete-new',
            method: HTTP_METHOD.POST,
            content: param.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
            (json) => Object());
  }
  ///日常收支-新增
  Future<RespResult<dynamic>> addRecord(
      GroupSettlementParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/add',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
            (json) => json);
  }

}
