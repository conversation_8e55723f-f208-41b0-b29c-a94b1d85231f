import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/net/delete_single_record_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_index_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/delete_single_record_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

import 'model/net/business_get_business_list_net_model.dart';
import 'model/net/business_get_index_business_count_net_model.dart';
import 'model/net/business_get_index_business_list_net_model.dart';
import 'model/net/net_model_personal_with_join_net_model.dart';
import 'model/param/business_get_business_list_param_model.dart';
import 'model/param/business_get_index_business_list_param_model.dart';

class WorkerFlowRds {
  /// 获取头部统计数据
  Future<RespResult<BusinessCountNetModel>> fetchHeaderData(
      BusinessCountParamModel params) async {
    return await NetCore.requestJGPHP(
      BaseBizRequestEntity(
          url: "/api/v3/business/get-index-business-count",
          method: HTTP_METHOD.GET,
          content: params.toMap(),
          requestExtra: RequestExtra(showLoading: true, printResp: true)),
      (json) => BusinessCountNetModel.fromJson(json),
    );
  }

  /// 获取列表数据
  Future<RespResult<BusinessListNetModel>> fetchListData(
      BusinessListParamModel param) async {
    return await NetCore.requestJGPHP(
      BaseBizRequestEntity(
          url: '/api/v3/business/get-index-business-list',
          method: HTTP_METHOD.GET,
          content: param.toMap(),
          requestExtra: RequestExtra(showLoading: true, printResp: true)),
      (json) => BusinessListNetModel.fromJson(json),
    );
  }

  /// 获取已有项目列表数据
  Future<RespResult<NetModelPersonalWithJoinNetModel>> fetchProjectList(
      Map<String, Object> param) async {
    return await NetCore.requestJGPHP(
      BaseBizRequestEntity(
          url: '/api/v3/dept/list/personal_with_join',
          method: HTTP_METHOD.GET,
          content: param,
          requestExtra: RequestExtra(showLoading: true, printResp: true)),
      (json) => NetModelPersonalWithJoinNetModel.fromJson(json),
    );
  }

  /// 批量删除记工记录
  Future<RespResult<dynamic>> fetchDeleteRecord(
      Map<String, Object> param) async {
    return await NetCore.requestJGPHP(
      BaseBizRequestEntity(
          url: '/api/v3/business/batch/delete',
          method: HTTP_METHOD.POST,
          content: param,
          requestExtra: RequestExtra(showLoading: true, printResp: true)),
      (json) => NetModelPersonalWithJoinNetModel.fromJson(json),
    );
  }

  /// 获取流水列表数据
  Future<RespResult<BusinessGetBusinessListNetModel?>> fetchFlowListData(
      BusinessGetBusinessListParamModel params) async {
    return await NetCore.requestJGPHP(
      BaseBizRequestEntity(
          url: '/api/v3/business/get-business-list',
          method: HTTP_METHOD.GET,
          content: params.toMap(),
          requestExtra: RequestExtra(showLoading: true, printResp: true)),
      (json) => BusinessGetBusinessListNetModel.fromJson(json),
    );
  }

  ///删除单个记工
  Future<RespResult<DeleteSingleRecordNetModel>> deleteRecordWork(
      DeleteSingleRecordAParamModel params, int id) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/delete/$id',
            method: HTTP_METHOD.DELETE,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
            (json) => DeleteSingleRecordNetModel.fromJson(json));
  }
}

//   /// 获取流水统计数据
//   Future<BusinessCountBizModel?> fetchHeaderData2() async {
//     try {
//       Dio dio = Dio(BaseOptions(
//         baseUrl: baseUrl,
//         connectTimeout: const Duration(seconds: 10),
//         receiveTimeout: const Duration(seconds: 10),
//         headers: headerMap,
//       ));
//       if (kDebugMode) {
//         dio.httpClientAdapter = IOHttpClientAdapter()
//           ..createHttpClient = () {
//             var httpClient = HttpClient();
//             // httpClient.findProxy = (uri) => "PROXY ************:8889";
//             httpClient.badCertificateCallback =
//                 (X509Certificate cert, String host, int port) => true;
//             return httpClient;
//           };
//       }
//       var mYear = DateTime.now().year;
//       var mMonth = DateTime.now().month;
//       var mDay = DateTime.now().day;
//       Response response = await dio.get(
//         "/api/v3/business/get-index-business-count",
//         data: {
//           "identity": 2,
//           "start_time": "$mYear-$mMonth-01",
//           "end_time": "$mYear-$mMonth-$mDay", //当日
//           "status": 0,
//         },
//       );
//       if (response.statusCode == 200) {
//         // print('成功: ${response.data}');
//         var respData = BusinessGetIndexBusinessCountNetModel.fromJson(response.data["data"]);
//         var respEntity = respData.transform();
//         return respEntity;
//       } else {
//         print('失败: code:${response.statusCode} msg:${response.statusMessage}');
//       }
//     } catch (e, a) {
//       print('请求异常: $e $a');
//       throw Exception("error");
//     }
//     return null;
//   }
// }
//
// final baseUrl = "http://app-test.cdmgkj.cn";
// final token =
//     "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTA0MDc4ODcsImV4cCI6MTc2MDc3NTg4NywiZGF0YSI6eyJzaW5nbGUiOiIyN0FDRzFQVklYSUhROEVBIiwidWlkIjoyNTEzNTYyNywiYnVzaW5lc3MiOiIyIiwic3lzdGVtX3R5cGUiOiJhbmRyb2lkIiwibWluaV90b2tlbiI6IjI3QUNHMVBWSVhJSFE4RUEiLCJpZCI6MjUxMzU2MjcsInV1aWQiOjI1MTM1NjI3fSwidG9rZW4iOnsicmVnUnQiOiJhbmRyb2lkIiwidGVuYW50S2V5IjoiSkdKWiIsInRlbmFudElkIjoyNTEzNTYyNywicGFja2FnZU5hbWUiOiJjb20ueXVwYW8uZ29uZ2Rpamlnb25nIiwidXNlcklkIjoyNTEzNTYyNywidG9rZW4iOiIyN0FDRzFQVklYSUhROEVBIn19.LdJz4KOiXyGEL_-chdEVenEbc_m-tkITI7NEfdm-BXM";
// final jgjztoken =
//     "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ0ZXN0LmxvZ2luIiwiaWF0IjoxNzUwNDA3ODg3LCJ1aWQiOjI1MTM1NjI3fQ.MavprWEOyrpZJqFJaMa5aA40YezjDxjnuDYMEdOREgY";
//
// final headerMap = {
//   'Content-Type': 'application/json',
//   'jgjztoken': jgjztoken,
//   'singletoken': token,
//   'uuid': '25135627',
//   'uid': '25135627',
//   'deviceuuid': '195b682cf0a4f826',
//   'imei': '195b682cf0a4f826',
//   'env': 'TEST',
//   'channel': 'authority',
//   'user-agent': 'YP JGJZ Redmi 22101317C 13 6.6.0 195b682cf0a4f826 1749713690379',
//   'business': '2',
//   'package_name': 'gdjg',
//   'version': '6.6.0',
//   'device': 'Redmi,22101317C',
//   'versioncode': '660',
//   'system_type': 'android',
//   'system': 'android',
//   'systemversion': '13',
//   'apiversion': '3.0',
//   'token': token,
//   'source': 'agd',
//   'oaid': '2068c49e6c60528a',
// };
