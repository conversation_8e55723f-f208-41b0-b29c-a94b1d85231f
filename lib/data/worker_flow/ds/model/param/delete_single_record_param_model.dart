class DeleteSingleRecordAParamModel {
  double? note_id;
  String? wechat_token;
  double? member_id;
  String? member_worker;
  double? token_user_id;
  double? token_corp_id;
  double? visit_member_id;
  VisitNoteParamModel? visit_note;
  double? worker_agent;

  DeleteSingleRecordAParamModel();

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (note_id != null) map["note_id"] = note_id!;
    if (wechat_token != null) map["wechat_token"] = wechat_token!;
    if (member_id != null) map["member_id"] = member_id!;
    if (member_worker != null) map["member_worker"] = member_worker!;
    if (token_user_id != null) map["token_user_id"] = token_user_id!;
    if (token_corp_id != null) map["token_corp_id"] = token_corp_id!;
    if (visit_member_id != null) map["visit_member_id"] = visit_member_id!;
    if (visit_note != null) map["visit_note"] = visit_note!;
    if (worker_agent != null) map["worker_agent"] = worker_agent!;
    return map;
  }

}

class VisitNoteParamModel {
  double? id;
  double? dept_id;
  String? name;
  double? created_by;
  double? fee_switch;

  VisitNoteParamModel();

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (dept_id != null) map["dept_id"] = dept_id!;
    if (name != null) map["name"] = name!;
    if (created_by != null) map["created_by"] = created_by!;
    if (fee_switch != null) map["fee_switch"] = fee_switch!;
    return map;
  }

}

