import 'package:gdjg_pure_flutter/data/worker_flow/repo/delete_single_record_biz_model.dart';

class DeleteSingleRecordNetModel {
  double? code;
  List<String>? data;
  String? msg;

  DeleteSingleRecordNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (code != null) map["code"] = code!;
    if (data != null) map["data"] = data!;
    if (msg != null) map["msg"] = msg!;
    return map;
  }

  factory DeleteSingleRecordNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = DeleteSingleRecordNetModel();
    netModel.code = double.tryParse(json["code"].toString());
    netModel.data = (json["data"] as List<dynamic>?)
        ?.map((e) => e as String)
        .toList();
    netModel.msg = json["msg"]?.toString();
    return netModel;
  }

  DeleteSingleBizModel transform() {
    return DeleteSingleBizModel(
      code: code ?? 0.0,
      data: data ?? [],
      msg: msg ?? "",
    );
  }
}

