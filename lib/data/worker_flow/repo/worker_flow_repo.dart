import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/net/business_get_business_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/net/business_get_business_list_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/net/delete_single_record_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_business_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_index_business_count_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/business_get_index_business_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/model/param/delete_single_record_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/ds/worker_flow_rds.dart';
import 'package:gdjg_pure_flutter/data/worker_flow/repo/delete_single_record_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_fail.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_result/resp_suc.dart';

import 'business_get_index_business_count_biz_model.dart';
import 'business_get_index_business_list_biz_model.dart';
import 'net_model_personal_with_join_biz_model.dart';

class WorkerFlowRepo {
  final _workFlowRds = WorkerFlowRds();

  Future<RespResult<BusinessCountBizModel?>> fetchHeaderData(BusinessCountParamModel param) async {
    final resp = await _workFlowRds.fetchHeaderData(param);
    final data = resp.getSucData();
    if (data == null) {
      return RespFail.buildBizFail<BusinessCountBizModel>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }
    return resp.map((e) => e?.transform());
  }

  Future<RespResult<BusinessListBizModel?>> fetchFlowList(BusinessListParamModel param) async {
    final resp = await _workFlowRds.fetchListData(param);
    final data = resp.getSucData();
    if (data == null) {
      return RespFail.buildBizFail<BusinessListBizModel>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }
    return resp.map((e) => e?.transform());
  }

  Future<RespResult<NetModelPersonalWithJoinBizModel?>> fetchProjectList(
      Map<String, Object> param) async {
    final resp = await _workFlowRds.fetchProjectList(param);
    final data = resp.getSucData();
    if (data == null) {
      return RespFail.buildBizFail<NetModelPersonalWithJoinBizModel>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }
    return resp.map((e) => e?.transform());
  }

  Future<RespResult<bool>> fetchDeleteRecord(Map<String, Object> param) async {
    final resp = await _workFlowRds.fetchDeleteRecord(param);
    if (!resp.isOK()) {
      return RespFail.buildBizFail<bool>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }
    return RespSuc.buildBizSuccess(true, resp.success?.msg, resp.success?.askId);
  }

  /// 个人单日流水
  Future<List<BusinessGetBusinessListBBizModel>> fetchWorkerDailyFlowList(
      {required DateTime date}) async {
    final dateFormat = "${date.year}-${date.month}-${date.day}";
    final param = BusinessGetBusinessListParamModel(
        identity: "2", start_time: dateFormat, end_time: dateFormat, status: "0");

    final resp = await _workFlowRds.fetchFlowListData(param);
    if (!resp.isOK()) {
      return [];
    }
    return resp
            .getSucData()
            ?.list
            ?.firstOrNull
            ?.list
            ?.whereType<BusinessGetBusinessListBNetModel>()
            .map((i) => i.transform())
            .toList() ??
        List.empty();
  }

  Future<List<BusinessGetBusinessListABizModel>?> fetchWorkerWorklogFlowDetailsList(
    BusinessGetBusinessListParamModel params,
  ) async {
    final RespResult<BusinessGetBusinessListNetModel?> resp =
        await _workFlowRds.fetchFlowListData(params);
    if (!resp.isOK()) {
      return null;
    } else {
      return resp.getSucData()?.list?.map((i) => i.transform()).toList() ?? List.empty();
    }
  }

  Future<RespResult<DeleteSingleBizModel>> deleteRecordWork(
      DeleteSingleRecordAParamModel params, int id) async {
    final result = await _workFlowRds.deleteRecordWork(params, id);
    return result.map(_transformDeleteSingleRecord);
  }

  DeleteSingleBizModel _transformDeleteSingleRecord(
      DeleteSingleRecordNetModel? netModel) {
    if (netModel == null) {
      return DeleteSingleBizModel();
    }
    return netModel.transform();
  }
}
