class BusinessDownloadFileExcelParamModel {

  /// 记账本id（没有传0）
  final String? work_note;

  /// 开始日期 2021-05-15
  final String? start_date;

  /// 结束日期 2021-05-15
  final String? end_date;

  /// 类型筛选 多个用逗号隔开
  final String? business_type;

  /// 工人筛选 多个用逗号隔开
  final String? workers_id;

  /// 账本筛选 多个用逗号隔开
  final String? note_id;

  /// 身份 1班组长 2工人
  final String? identity;

  /// 1-可以查看工资，2-不可以查看工资
  final String? is_show;

  /// 【5.1.0新增】1导出备注 0不导出备注，默认0
  final String? is_remark;

  BusinessDownloadFileExcelParamModel({
    this.work_note,
    this.start_date,
    this.end_date,
    this.business_type,
    this.workers_id,
    this.note_id,
    this.identity,
    this.is_show,
    this.is_remark,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (work_note != null) map["work_note"] = work_note!;
    if (start_date != null) map["start_date"] = start_date!;
    if (end_date != null) map["end_date"] = end_date!;
    if (business_type != null) map["business_type"] = business_type!;
    if (workers_id != null) map["workers_id"] = workers_id!;
    if (note_id != null) map["note_id"] = note_id!;
    if (identity != null) map["identity"] = identity!;
    if (is_show != null) map["is_show"] = is_show!;
    if (is_remark != null) map["is_remark"] = is_remark!;
    return map;
  }
}

