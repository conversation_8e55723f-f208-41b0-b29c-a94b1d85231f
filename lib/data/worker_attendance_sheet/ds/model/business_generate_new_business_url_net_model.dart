import '../../repo/model/business_generate_new_business_url_biz_model.dart';

class BusinessGenerateNewBusinessUrlNetModel {

  /// 账本名字
  String? title;

  /// 内容
  String? content;

  /// 分享的图片
  String? img;

  /// 分享的地址
  String? url;

  /// 加密code
  String? code;

  /// 小程序原始id
  String? originId;

  /// 小程序路径
  String? path;

  BusinessGenerateNewBusinessUrlNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (title != null) map["title"] = title!;
    if (content != null) map["content"] = content!;
    if (img != null) map["img"] = img!;
    if (url != null) map["url"] = url!;
    if (code != null) map["code"] = code!;
    if (originId != null) map["originId"] = originId!;
    if (path != null) map["path"] = path!;
    return map;
  }

  factory BusinessGenerateNewBusinessUrlNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BusinessGenerateNewBusinessUrlNetModel();
    netModel.title = json["title"]?.toString();
    netModel.content = json["content"]?.toString();
    netModel.img = json["img"]?.toString();
    netModel.url = json["url"]?.toString();
    netModel.code = json["code"]?.toString();
    netModel.originId = json["originId"]?.toString();
    netModel.path = json["path"]?.toString();
    return netModel;
  }

  BusinessGenerateNewBusinessUrlBizModel transform() {
    return BusinessGenerateNewBusinessUrlBizModel(
      title: title ?? "",
      content: content ?? "",
      img: img ?? "",
      url: url ?? "",
      code: code ?? "",
      originId: originId ?? "",
      path: path ?? "",
    );
  }
}

