import 'package:gdjg_pure_flutter/feature/tabbar/us/identity_us.dart';

import '../../../utils/store_util/base_lds.dart';

class VisitorRoleLds extends BaseLds<String> {
  static const String _worker = "worker";
  static const String _leader = "leader";

  @override
  String getBizName() => "VisitorRoleLds";

  Future<UserIdentity> fetchRole() async {
    final String storage = get(defaultValue: _worker) ?? _worker;
    switch (storage) {
      case _worker:
        return UserIdentity.worker;
      case _leader:
        return UserIdentity.leader;
      default:
        return UserIdentity.worker;
    }
  }

  Future<UserIdentity> updateRole(UserIdentity role) async {
    final String storage;
    switch (role) {
      case UserIdentity.worker:
        storage = _worker;
        break;
      case UserIdentity.leader:
        storage = _leader;
        break;
    }
    save(storage);
    return role;
  }

}
