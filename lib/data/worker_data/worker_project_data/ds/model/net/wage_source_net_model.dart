import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/wage_source_biz_model.dart';

class WageSourceNetModel {
  double? id;

  /// 固定id
  double? fix_id;

  /// 1 收入 2支出
  double? type;

  WageSourceNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (fix_id != null) map["fix_id"] = fix_id!;
    if (type != null) map["type"] = type!;
    return map;
  }

  factory WageSourceNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = WageSourceNetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.fix_id = double.tryParse(json["fix_id"].toString());
    netModel.type = double.tryParse(json["type"].toString());
    return netModel;
  }

  WageSourceBizModel transform() {
    return WageSourceBizModel(
      id: id ?? 0.0,
      fixId: fix_id ?? 0.0,
      type: type ?? 0.0,
    );
  }
}
