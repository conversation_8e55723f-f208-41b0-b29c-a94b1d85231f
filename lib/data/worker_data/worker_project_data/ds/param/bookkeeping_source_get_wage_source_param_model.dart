class BookkeepingSourceGetWageSourceParamModel {

  /// 
  final String? wechat_token;

  /// 工本id
  final String? work_note;

  BookkeepingSourceGetWageSourceParamModel({
    this.wechat_token,
    this.work_note,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (wechat_token != null) map["wechat_token"] = wechat_token!;
    if (work_note != null) map["work_note"] = work_note!;
    return map;
  }
}

