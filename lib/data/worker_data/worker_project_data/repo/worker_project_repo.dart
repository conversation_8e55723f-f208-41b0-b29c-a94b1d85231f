import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_create_dept_req_entity.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_detail_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_put_away_dept_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/dept_update_name_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/project_get_project_list_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/param/wage_source_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/ds/worker_project_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_project/ds/group_project_rds.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_project/ds/model/param/dept_list_group_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/wage_source_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/store_util/sp_keys.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_create_dept_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/dept_detail_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_project/repo/model/net_model_group_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/personal_with_join_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/worker_project_data/repo/model/project_get_project_list_biz_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/ds/model/param/group_project_get_group_worker_settle_param_model.dart';
import 'package:gdjg_pure_flutter/data/group_data/group_un_liauidated/repo/model/group_project_get_group_worker_settle_biz_model.dart';
import 'package:gdjg_pure_flutter/utils/store_util/kv_util.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class WorkerProjectRepo {
  final _rds = WorkerProjectRds();
  final _groupProjectRds = GroupProjectRds();

  // KVUtil - 项目隐私设置
  final _privacyKV = KVUtil.getKV("project_privacy");

  //获取项目列表
  Future<RespResult<ProjectGetProjectListBizModel>> queryProjectList(
      ProjectGetProjectListParamModel req,
      ) async {
    final result = await _rds.queryProjectList(req);
    return result.map((netModel) => netModel?.transform() ?? ProjectGetProjectListBizModel());
  }

  //新建项目
  Future<RespResult<DeptCreateDeptBizModel>> createProject(
      DeptCreateDeptParamModel req,
      ) async {
    final result = await _rds.createProject(req);
    return result.map((netModel) => netModel?.transform() ?? DeptCreateDeptBizModel());
  }

  //获取我参与的项目列表
  Future<RespResult<NetModelGroupBizModel>> queryParticipatedProjectList() async {
    final params = DeptListGroupParamModel(
      isIgnore: '0',
      type: 'join',
    );
    final result = await _groupProjectRds.getDeptListGroup(params);
    return result.map((netModel) => netModel?.transform() ?? NetModelGroupBizModel());
  }

  //获取项目详情
  Future<RespResult<ProjectGetProjectBizModel>> getProject(String projectId) async {
    final result = await _rds.getProject(projectId);
    return result.map((netModel) => netModel?.transform() ?? ProjectGetProjectBizModel());
  }

  //设为已结清
  Future<RespResult<bool>> putAwayDept(DeptPutAwayDeptParamModel req) async {
    final result = await _rds.putAwayDept(req);
    return result;
  }

  //获取已结清项目列表
  Future<RespResult<NetModelPersonalWithJoinBizModel>> queryCompletedProjectList() async {
    final result = await _rds.queryCompletedProjectList();
    return result.map((netModel) => netModel?.transform() ?? NetModelPersonalWithJoinBizModel());
  }

  //删除项目
  Future<RespResult<bool>> deleteProject(String deptId) async {
    final result = await _rds.deleteProject(deptId);
    return result;
  }

  //修改项目名称
  Future<RespResult<bool>> updateDeptName(DeptUpdateDeptNameParamModel req) async {
    final result = await _rds.updateDeptName(req);
    return result;
  }

  //获取工人在项目中的结算信息
  Future<RespResult<GroupProjectGetGroupWorkerSettleBizModel>> getGroupWorkerSettle(
      GroupProjectGetGroupWorkerSettleParamModel req) async {
    final result = await _rds.getGroupWorkerSettle(req);
    return result.map((netModel) => netModel?.transform() ?? GroupProjectGetGroupWorkerSettleBizModel());
  }

  // 获取部门详情
  Future<RespResult<DeptDetailBizModel>> getDeptDetail(String deptId) async {
    final req = DeptDetailParamModel(dept_id: deptId);
    final result = await _rds.getDeptDetail(req);
    return result.map((netModel) => netModel?.transform() ?? DeptDetailBizModel());
  }

  /// 获取工资来源
  Future<RespResult<WageSourceBizModel>> getWageSource(String workNoteId) async {
    final req = WageSourceParamModel(work_note: workNoteId);
    final result = await _rds.getWageSource(req);
    return result.map((r) => r?.transform() ?? WageSourceBizModel());
  }

  // ========== 项目隐私设置相关方法 ==========

  /// 获取"分享时不展示我的工资"设置
  bool getHideMyWageWhenShare() {
    return _privacyKV.getBool(StoreKeys.key_hide_my_wage_when_share, defaultValue: false);
  }

  /// 设置"分享时不展示我的工资"
  void setHideMyWageWhenShare(bool hide) {
    _privacyKV.setBool(StoreKeys.key_hide_my_wage_when_share, hide);
  }

  /// 获取"分享时不展示工友工资"设置
  bool getHideWorkerWageWhenShare() {
    return _privacyKV.getBool(StoreKeys.key_hide_worker_wage_when_share, defaultValue: false);
  }

  /// 设置"分享时不展示工友工资"
  void setHideWorkerWageWhenShare(bool hide) {
    _privacyKV.setBool(StoreKeys.key_hide_worker_wage_when_share, hide);
  }
}


