class UnitWorkTypeGetBizModel {

  /// 
  List<UnitWorkTypeGetABizModel> list;

  UnitWorkTypeGetBizModel({
    this.list = const [],
  });

  @override
  String toString() {
    return "UnitWorkTypeGetBizModel(list: ${list.toString()})";
  }
}

class UnitWorkTypeGetABizModel {

  /// 
  double id;

  /// 分项名称
  String name;

  /// 分项单位
  String unit;

  /// 
  String lastUnitPrice;

  UnitWorkTypeGetABizModel({
    this.id = 0.0,
    this.name = "",
    this.unit = "",
    this.lastUnitPrice = "",
  });

  @override
  String toString() {
    return "UnitWorkTypeGetABizModel(id: $id, name: $name, unit: $unit, lastUnitPrice: $lastUnitPrice)";
  }
}

