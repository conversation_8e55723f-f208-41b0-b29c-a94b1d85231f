import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/check_business_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/delete_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/expenses_add_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/expenses_del_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/expenses_net_model_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/expenses_last_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/project_get_last_business_project_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/unit_get_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/unit_work_type_add_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/unit_work_type_get_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/unit_work_type_last_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/update_new_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/add_record_work_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/check_business_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/delete_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/expenses_add_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/expenses_del_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/project_get_last_business_project_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/unit_work_type_add_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/unit_work_type_get_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/update_new_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/worker_account_record_rds.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/check_business_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/delete_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/expenses_add_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/expenses_del_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/expenses_net_model_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/expenses_lastbiz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/project_get_last_business_project_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/unit_get_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/unit_work_type_add_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/unit_work_type_get_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/unit_work_type_last_biz_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/update_new_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class WorkerAccountRecordRepo {
  final _workerAccountRecordRds = WorkerAccountRecordRds();

  Future<RespResult<ProjectGetLastBusinessProjectBizModel>>
      getProjectLastBusinessProject(
          ProjectGetLastBusinessProjectParamModel param) async {
    final result =
        await _workerAccountRecordRds.getProjectLastBusinessProject(param);
    return result.map(_transform);
  }

  ProjectGetLastBusinessProjectBizModel _transform(
      ProjectGetLastBusinessProjectNetModel? netModel) {
    if (netModel == null) {
      return ProjectGetLastBusinessProjectBizModel();
    }
    return netModel.transform();
  }

  Future<RespResult<BusinessCheckBusinessBizModel>> checkBusiness(
      BusinessCheckBusinessParamModel param) async {
    final result = await _workerAccountRecordRds.checkBusiness(param);
    return result.map(_transformCheckBusiness);
  }

  BusinessCheckBusinessBizModel _transformCheckBusiness(
      BusinessCheckBusinessNetModel? netModel) {
    if (netModel == null) {
      return BusinessCheckBusinessBizModel();
    }
    return netModel.transform();
  }

  Future<RespResult<UnitWorkTypeGetLastUnitWorkTypeBizModel>> getLastUnitType(
      String workNote) async {
    final result = await _workerAccountRecordRds.getLastUnitType(workNote);
    return result.map(_transformUnitWorkType);
  }

  UnitWorkTypeGetLastUnitWorkTypeBizModel _transformUnitWorkType(
      UnitWorkTypeGetLastUnitWorkTypeNetModel? netModel) {
    if (netModel == null) {
      return UnitWorkTypeGetLastUnitWorkTypeBizModel();
    }
    return netModel.transform();
  }

  Future<RespResult<ExpensesLastBizModel>> getLastExpenses(
      String workNote) async {
    final result = await _workerAccountRecordRds.getLastExpenses(workNote);
    return result.map(_transformExpensesLast);
  }

  ExpensesLastBizModel _transformExpensesLast(ExpensesLastNetModel? netModel) {
    if (netModel == null) {
      return ExpensesLastBizModel();
    }
    return netModel.transform();
  }

  Future<RespResult<dynamic>> addRecordWork(
      BusinessAddMyselfAParamModel params) async {
    return _workerAccountRecordRds.addRecordWork(params);
  }

  Future<RespResult<ExpensesNetModelBizModel>>
      fetchOtherExpenseOptions() async {
    final result = await _workerAccountRecordRds.fetchOtherExpenseOptions();
    return result.map(_transformOtherExpenseOptions);
  }

  ExpensesNetModelBizModel _transformOtherExpenseOptions(
      ExpensesNetModelNetModel? netModel) {
    if (netModel == null) {
      return ExpensesNetModelBizModel();
    }
    return netModel.transform();
  }

  Future<RespResult<ExpensesAddBizModel>> addExpenseOption(
      ExpensesAddAParamModel params) async {
    final result = await _workerAccountRecordRds.addOtherExpenseOptions(params);
    return result.map(_transformAddOtherExpenseOptions);
  }

  ExpensesAddBizModel _transformAddOtherExpenseOptions(
      ExpensesAddNetModel? netModel) {
    if (netModel == null) {
      return ExpensesAddBizModel();
    }
    return netModel.transform();
  }

  Future<RespResult<ExpensesDelBizModel>> deleteExpenseOption(
      ExpensesDelAParamModel params) async {
    final result =
        await _workerAccountRecordRds.deleteOtherExpenseOptions(params);
    return result.map(_transformDeleteOtherExpenseOptions);
  }

  ExpensesDelBizModel _transformDeleteOtherExpenseOptions(
      ExpensesDelNetModel? netModel) {
    if (netModel == null) {
      return ExpensesDelBizModel();
    }
    return netModel.transform();
  }

  Future<RespResult<UnitWorkTypeGetBizModel>> fetchSubitemList(
      UnitWorkTypeGetParamModel params) async {
    final result = await _workerAccountRecordRds.fetchSubitemList(params);
    return result.map(_transformSubitemList);
  }

  UnitWorkTypeGetBizModel _transformSubitemList(
      UnitWorkTypeGetNetModel? netModel) {
    if (netModel == null) {
      return UnitWorkTypeGetBizModel();
    }
    return netModel.transform();
  }

  Future<RespResult<UnitWorkTypeAddBizModel>> addNewSubitem(
      UnitWorkTypeAddParamModel params) async {
    final result = await _workerAccountRecordRds.addNewSubitem(params);
    return result.map(_transformNewSubitem);
  }

  UnitWorkTypeAddBizModel _transformNewSubitem(
      UnitWorkTypeAddNetModel? netModel) {
    if (netModel == null) {
      return UnitWorkTypeAddBizModel();
    }
    return netModel.transform();
  }

  Future<RespResult<UnitGetBizModel>> fetchSubitemUnitList() async {
    final result = await _workerAccountRecordRds.fetchSubitemUnitList();
    return result.map(_transformSubitemUnitList);
  }

  UnitGetBizModel _transformSubitemUnitList(UnitGetNetModel? netModel) {
    if (netModel == null) {
      return UnitGetBizModel();
    }
    return netModel.transform();
  }

  /// 更新分项
  Future<RespResult<UpdateNewBizModel>> updateSubitem(
      UpdateNewAParamModel params, int id) async {
    final result = await _workerAccountRecordRds.updateSubitem(params, id);
    return result.map(_transformUpdateSubitem);
  }

  UpdateNewBizModel _transformUpdateSubitem(UpdateNewNetModel? netModel) {
    if (netModel == null) {
      return UpdateNewBizModel();
    }
    return netModel.transform();
  }

  /// 删除分项
  Future<RespResult<DeleteBizModel>> deleteSubitem(
      DeleteParamModel params, int id) async {
    final result = await _workerAccountRecordRds.deleteSubitem(params, id);
    return result.map(_transformDeleteOtherSubitem);
  }

  DeleteBizModel _transformDeleteOtherSubitem(DeleteNetModel? netModel) {
    if (netModel == null) {
      return DeleteBizModel();
    }
    return netModel.transform();
  }
}
