class DeleteParamModel {

  /// 记工用户id
  final String? member_id;

  /// 多个id 用英文逗号分隔 6592,6593,
  final String? id;

  /// 工本id
  final int? work_note;

  DeleteParamModel({
    this.member_id,
    this.id,
    this.work_note,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (member_id != null) map["member_id"] = member_id!;
    if (id != null) map["id"] = id!;
    if (work_note != null) map["work_note"] = work_note!;
    return map;
  }
}

