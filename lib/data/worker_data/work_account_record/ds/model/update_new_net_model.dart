import '../../repo/model/update_new_biz_model.dart';

class UpdateNewNetModel {
  double? code;
  List<String>? data;
  String? msg;

  UpdateNewNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (code != null) map["code"] = code!;
    if (data != null) map["data"] = data!;
    if (msg != null) map["msg"] = msg!;
    return map;
  }

  factory UpdateNewNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = UpdateNewNetModel();
    netModel.code = double.tryParse(json["code"].toString());
    netModel.data = (json["data"] as List<dynamic>?)
        ?.map((e) => e as String)
        .toList();
    netModel.msg = json["msg"]?.toString();
    return netModel;
  }

  UpdateNewBizModel transform() {
    return UpdateNewBizModel(
      code: code ?? 0.0,
      data: data ?? [],
      msg: msg ?? "",
    );
  }
}

