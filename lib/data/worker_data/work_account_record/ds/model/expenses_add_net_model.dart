
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/repo/model/expenses_add_biz_model.dart';

class ExpensesAddNetModel {

  /// 名称
  String? name;

  /// 记工ID
  String? member_id;

  /// 鱼泡ID
  String? yupao_id;

  /// 名称主键ID
  String? bk_other_expenses_id;

  ExpensesAddNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (name != null) map["name"] = name!;
    if (member_id != null) map["member_id"] = member_id!;
    if (yupao_id != null) map["yupao_id"] = yupao_id!;
    if (bk_other_expenses_id != null) map["bk_other_expenses_id"] = bk_other_expenses_id!;
    return map;
  }

  factory ExpensesAddNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = ExpensesAddNetModel();
    netModel.name = json["name"]?.toString();
    netModel.member_id = json["member_id"]?.toString();
    netModel.yupao_id = json["yupao_id"]?.toString();
    netModel.bk_other_expenses_id = json["bk_other_expenses_id"]?.toString();
    return netModel;
  }

  ExpensesAddBizModel transform() {
    return ExpensesAddBizModel(
      name: name ?? "",
      memberId: member_id ?? "",
      yupaoId: yupao_id ?? "",
      bkOtherExpensesId: bk_other_expenses_id ?? "",
    );
  }
}

