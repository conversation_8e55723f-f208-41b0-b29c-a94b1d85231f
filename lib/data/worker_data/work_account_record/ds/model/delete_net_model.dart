import '../../repo/model/delete_biz_model.dart';

class DeleteNetModel {
  String? code;
  List<String>? data;

  /// 删除成功
  String? msg;

  DeleteNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (code != null) map["code"] = code!;
    if (data != null) map["data"] = data!;
    if (msg != null) map["msg"] = msg!;
    return map;
  }

  factory DeleteNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = DeleteNetModel();
    netModel.code = json["code"]?.toString();
    netModel.data = (json["data"] as List<dynamic>?)
        ?.map((e) => e as String)
        .toList();
    netModel.msg = json["msg"]?.toString();
    return netModel;
  }

  DeleteBizModel transform() {
    return DeleteBizModel(
      code: code ?? "",
      data: data ?? [],
      msg: msg ?? "",
    );
  }
}

