import '../../repo/model/unit_work_type_last_biz_model.dart';

class UnitWorkTypeGetLastUnitWorkTypeNetModel {

  /// 
  double? id;

  /// 分项名称
  String? name;

  /// 分项单位
  String? unit;

  /// 上次记工单价
  String? last_unit_price;

  UnitWorkTypeGetLastUnitWorkTypeNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (id != null) map["id"] = id!;
    if (name != null) map["name"] = name!;
    if (unit != null) map["unit"] = unit!;
    if (last_unit_price != null) map["last_unit_price"] = last_unit_price!;
    return map;
  }

  factory UnitWorkTypeGetLastUnitWorkTypeNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = UnitWorkTypeGetLastUnitWorkTypeNetModel();
    netModel.id = double.tryParse(json["id"].toString());
    netModel.name = json["name"]?.toString();
    netModel.unit = json["unit"]?.toString();
    netModel.last_unit_price = json["last_unit_price"]?.toString();
    return netModel;
  }

  UnitWorkTypeGetLastUnitWorkTypeBizModel transform() {
    return UnitWorkTypeGetLastUnitWorkTypeBizModel(
      id: id ?? 0.0,
      name: name ?? "",
      unit: unit ?? "",
      lastUnitPrice: last_unit_price ?? "",
    );
  }
}

