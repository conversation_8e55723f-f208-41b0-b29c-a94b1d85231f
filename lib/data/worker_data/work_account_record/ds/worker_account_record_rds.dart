import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/check_business_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/delete_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/expenses_add_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/expenses_del_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/expenses_net_model_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/expenses_last_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/project_get_last_business_project_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/unit_get_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/unit_work_type_add_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/unit_work_type_get_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/unit_work_type_last_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/model/update_new_net_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/add_record_work_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/check_business_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/delete_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/expenses_add_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/expenses_del_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/project_get_last_business_project_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/unit_work_type_add_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/unit_work_type_get_param_model.dart';
import 'package:gdjg_pure_flutter/data/worker_data/work_account_record/ds/param/update_new_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

class WorkerAccountRecordRds {
  ///获取上次记工的记工本
  Future<RespResult<ProjectGetLastBusinessProjectNetModel>>
      getProjectLastBusinessProject(
          ProjectGetLastBusinessProjectParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/project/get-last-business-project',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => ProjectGetLastBusinessProjectNetModel.fromJson(json));
  }

  ///检查是否记过工
  Future<RespResult<BusinessCheckBusinessNetModel>> checkBusiness(
      BusinessCheckBusinessParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/check-business',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true)),
        (json) => BusinessCheckBusinessNetModel.fromJson(json));
  }

  /// 记工
  Future<RespResult<dynamic>> addRecordWork(
      BusinessAddMyselfAParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/business/add-myself',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => json);
  }

  /// 获取最后记工的工量分项
  Future<RespResult<UnitWorkTypeGetLastUnitWorkTypeNetModel>> getLastUnitType(
      String workNote) async {
    var map = <String, Object>{};
    map["work_note"] = workNote;
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/UnitWorkType/get-last-unit-work-type',
            method: HTTP_METHOD.GET,
            content: map,
            requestExtra: RequestExtra(showLoading: false, printResp: true)),
        (json) => UnitWorkTypeGetLastUnitWorkTypeNetModel.fromJson(json));
  }

  /// 获取最后记工的其他费用
  Future<RespResult<ExpensesLastNetModel>> getLastExpenses(
      String workNote) async {
    var map = <String, Object>{};
    map["work_note"] = workNote;
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: 'api/v3/business/other/expenses/last',
            method: HTTP_METHOD.GET,
            content: map,
            requestExtra: RequestExtra(showLoading: false, printResp: true)),
        (json) => ExpensesLastNetModel.fromJson(json));
  }

  ///用户获取其他费用选项列表
  Future<RespResult<ExpensesNetModelNetModel>>
      fetchOtherExpenseOptions() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/business/other/expenses/list',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => ExpensesNetModelNetModel.fromJson(json));
  }

  ///用户新增其他费用-自定义费用
  Future<RespResult<ExpensesAddNetModel>> addOtherExpenseOptions(
      ExpensesAddAParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/business/other/expenses/add',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => ExpensesAddNetModel.fromJson(json));
  }

  ///用户删除其他费用-自定义费用
  Future<RespResult<ExpensesDelNetModel>> deleteOtherExpenseOptions(
      ExpensesDelAParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/v3/business/other/expenses/del',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => ExpensesDelNetModel.fromJson(json));
  }

  ///获取分项列表
  Future<RespResult<UnitWorkTypeGetNetModel>> fetchSubitemList(
      UnitWorkTypeGetParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/UnitWorkType/get',
            method: HTTP_METHOD.GET,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => UnitWorkTypeGetNetModel.fromJson(json));
  }

  ///增加新分项
  Future<RespResult<UnitWorkTypeAddNetModel>> addNewSubitem(
      UnitWorkTypeAddParamModel params) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/UnitWorkType/add',
            method: HTTP_METHOD.POST,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => UnitWorkTypeAddNetModel.fromJson(json));
  }

  /// 修改分项
  Future<RespResult<UpdateNewNetModel>> updateSubitem(
      UpdateNewAParamModel params, int id) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/UnitWorkType/update-new/$id',
            method: HTTP_METHOD.PUT,
            content: params.toMap()),
        (json) => UpdateNewNetModel.fromJson(json));
  }

  ///删除分项
  Future<RespResult<DeleteNetModel>> deleteSubitem(
      DeleteParamModel params, int id) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/UnitWorkType/delete/$id',
            method: HTTP_METHOD.DELETE,
            content: params.toMap(),
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => DeleteNetModel.fromJson(json));
  }

  ///获取单位列表
  Future<RespResult<UnitGetNetModel>> fetchSubitemUnitList() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/unit/get',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true, printResp: true)),
        (json) => UnitGetNetModel.fromJson(json));
  }
}
