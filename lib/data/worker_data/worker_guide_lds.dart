import 'package:gdjg_pure_flutter/utils/store_util/base_lds.dart';

class WorkerGuideLds extends BaseLds<int> {
  static const int maskStep1 = 0x01 << 0;
  static const int maskStep2 = 0x01 << 1;
  static const int maskStep3 = 0x01 << 2;

  @override
  String getBizName() => "WorkerGuide";

  /// [mask] see: [maskStep1], [maskStep2], [maskStep3]
  Future<bool> fetchGuided(int mask) async {
    final int current = get(defaultValue: 0) ?? 0;
    return current & mask != 0;
  }

  /// [mask] see: [maskStep1], [maskStep2], [maskStep3]
  /// [complete] true: 完成，false: 未完成
  Future<bool> updateGuided(int mask, bool complete) async {
    final int current = get(defaultValue: 0) ?? 0;
    final int result;
    if (complete) {
      result = current | mask;
    } else {
      result = current & ~mask;
    }
    save(result);
    return complete;
  }

}
