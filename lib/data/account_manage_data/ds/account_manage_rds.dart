import 'package:gdjg_pure_flutter/data/account_manage_data/ds/model/net/member_get_related_tel_net_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/ds/model/net/member_get_related_tel_new_net_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/ds/model/param/decrypt_tel_param_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/ds/model/param/member_send_code_param_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/ds/model/param/member_update_info_param_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

import 'model/net/decrypt_tel_net_model.dart';

/// 账号管理
class AccountManageRds {
  /// 发送验证码
  Future<RespResult<dynamic>> sendCode(MemberSendCodeParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/member/send_code',
          method: HTTP_METHOD.POST,
          content: param.toMap().cast(),
        ),
        (json) => json);
  }

  /// 修改手机号
  Future<RespResult<dynamic>> updatePhoneNumber(
      MemberUpdateInfoParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/member/update_info',
          method: HTTP_METHOD.POST,
          content: param.toMap().cast(),
        ),
        (json) => json);
  }

  /// 历史手机号
  Future<RespResult<MemberGetRelatedTelNetModel>> getRelatedTel() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
            url: '/api/member/get_related_tel',
            method: HTTP_METHOD.GET,
            requestExtra: RequestExtra(showLoading: true),
            content: {"wechat_token": 'rn', "mustLogin": true}),
        (json) => MemberGetRelatedTelNetModel.fromJson(json));
  }

  /// 切换手机号列表
  Future<RespResult<MemberGetRelatedTelNewNetModel>>
      getSwitchPhoneList() async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/v3/member/get_related_tel',
          method: HTTP_METHOD.GET,
          requestExtra: RequestExtra(showLoading: true),
        ),
        (json) => MemberGetRelatedTelNewNetModel.fromJson(json));
  }

  /// 解密手机号
  Future<RespResult<DecryptTelNetModel>> decryptTel(
      DecryptTelParamModel param) async {
    return await NetCore.requestJGPHP(
        BaseBizRequestEntity(
          url: '/api/v3/decrypt/tel',
          method: HTTP_METHOD.POST,
          content: param.toMap().cast(),
        ),
        (json) => DecryptTelNetModel.fromJson(json));
  }
}
