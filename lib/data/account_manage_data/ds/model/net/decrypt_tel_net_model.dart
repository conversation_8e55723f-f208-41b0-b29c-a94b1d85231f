import '../../../repo/model/decrypt_tel_biz_model.dart';

class DecryptTelNetModel {
  String? tel;

  DecryptTelNetModel();

  Map<String, dynamic> toJson(DecryptTelNetModel instance) {
    var map = <String, Object>{};
    if (tel != null) map["tel"] = tel!;
    return map;
  }

  factory DecryptTelNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = DecryptTelNetModel();
    netModel.tel = json["tel"]?.toString();
    return netModel;
  }

  DecryptTelBizModel transform() {
    return DecryptTelBizModel(
      tel: tel ?? "",
    );
  }
}
