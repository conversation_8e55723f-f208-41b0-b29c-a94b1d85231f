import 'package:gdjg_pure_flutter/data/account_manage_data/ds/account_manage_rds.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/ds/model/param/decrypt_tel_param_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/ds/model/param/member_send_code_param_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/ds/model/param/member_update_info_param_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/repo/model/decrypt_tel_biz_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/repo/model/member_get_related_tel_biz_model.dart';
import 'package:gdjg_pure_flutter/data/account_manage_data/repo/model/member_get_related_tel_new_biz_model.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';

class AccountManageRepo {
  final _rds = AccountManageRds();

  /// 获取验证码
  Future<RespResult<dynamic>> sendCode(MemberSendCodeParamModel params) async {
    return await _rds.sendCode(params);
  }

  /// 修改手机号
  Future<RespResult<dynamic>> updatePhoneNumber(
      MemberUpdateInfoParamModel params) async {
    return await _rds.updatePhoneNumber(params);
  }

  /// 历史手机号
  Future<RespResult<MemberGetRelatedTelBizModel?>> getRelatedTel() async {
    final result = await _rds.getRelatedTel();
    return result.map((result) => result?.transform());
  }

  /// 切换账号列表
  Future<RespResult<MemberGetRelatedTelNewBizModel?>> getSwitchPhoneList() async {
    final result =await _rds.getSwitchPhoneList();
    return result.map((result) => result?.transform());
  }

  /// 解密手机号
  Future<RespResult<DecryptTelBizModel?>> decryptTel(DecryptTelParamModel params) async{
    final result = await _rds.decryptTel(params);
    return result.map((result) => result?.transform());
  }
}
