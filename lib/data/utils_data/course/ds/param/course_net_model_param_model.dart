class CourseNetModelParamModel {

  /// 
  final String? identity;

  /// 当前页面 PROJECT_TEAM 班组项目页 PROJECT_WORKER 工人项目页
  final String? page_name;

  /// 页码
  final String? page;

  CourseNetModelParamModel({
    this.identity,
    this.page_name,
    this.page,
  });

  Map<String, Object> toMap() {
    var map = <String, Object>{};
    if (identity != null) map["identity"] = identity!;
    if (page_name != null) map["page_name"] = page_name!;
    if (page != null) map["page"] = page!;
    return map;
  }
}
