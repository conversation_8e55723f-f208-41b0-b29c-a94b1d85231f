class CourseNetModelBizModel {
  List<CourseNetModelABizModel> list;
  PageBizModel? page;

  CourseNetModelBizModel({
    this.list = const [],
    this.page,
  });
}

class CourseNetModelABizModel {
  double id;

  /// 标题
  String name;

  /// 教程类型 1 帮助视频 3 图文教程
  double category;

  /// 封面图片
  String coverPic;

  /// h5地址 客户端需要将 {token} {identity} {source} {uid} {env} {version} 替换成对应值
  String url;

  CourseNetModelABizModel({
    this.id = 0.0,
    this.name = "",
    this.category = 0.0,
    this.coverPic = "",
    this.url = "",
  });
}

class PageBizModel {

  /// 目前页数
  double curPage;

  /// 每页数量
  double pageSize;

  /// 是否有下一页
  bool hasMore;

  PageBizModel({
    this.curPage = 0.0,
    this.pageSize = 0.0,
    this.hasMore = false,
  });
}
