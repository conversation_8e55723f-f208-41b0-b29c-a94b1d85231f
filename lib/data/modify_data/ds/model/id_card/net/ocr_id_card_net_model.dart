import 'dart:convert';
import 'ocr_id_card_biz_model.dart';

class OcrIdCardNetModel {

  /// 身份证姓名
  String? card_name;

  /// 身份证号码
  String? card_no;

  OcrIdCardNetModel();

  Map<String, dynamic> toJson(OcrIdCardNetModel instance) => <String, dynamic>{
      "card_name": instance.card_name,
      "card_no": instance.card_no,
    };

  factory OcrIdCardNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = OcrIdCardNetModel();
    netModel.card_name = json["card_name"].toString();
    netModel.card_no = json["card_no"].toString();
    return netModel;
  }

  OcrIdCardBizModel transform() {
    return OcrIdCardBizModel(
      cardName: card_name ?? "",
      cardNo: card_no ?? "",
    );
  }

  @override
  String toString() {
    return jsonEncode(this);
  }
}

