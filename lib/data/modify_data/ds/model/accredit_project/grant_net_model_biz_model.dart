import 'dart:convert';

class AccreditProjectListBizModel {

  /// 授权列表
  List<AccreditProjectBizModel> grantList;

  AccreditProjectListBizModel({
    this.grantList = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class AccreditProjectBizModel {

  /// 班组长ID
  int leaderId;

  /// 班组长姓名
  String leaderName;

  /// 班组长手机号
  String leaderTel;

  /// 是否已授权 1是 0否
  int isGranted;

  /// 授权ID
  int grantId;

  /// 授权过期日期
  String expiredTime;

  /// 项目列表
  List<WorkNoteListBizModel> workNoteList;

  AccreditProjectBizModel({
    this.leaderId = 0,
    this.leaderName = "",
    this.leaderTel = "",
    this.isGranted = 0,
    this.grantId = 0,
    this.expiredTime = "",
    this.workNoteList = const [],
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class WorkNoteListBizModel {
  /// 项目ID
  final int id;

  /// 项目名称
  String name;

  /// 创建人
  final int createdBy;

  WorkNoteListBizModel({
    this.id = 0,
    this.name = "",
    this.createdBy = 0,
  });

}

