import 'privacy_get_biz_model.dart';

class PrivacyGetNetModel {
  /// 是否已实名 1是 0否
  int? is_self_verify;

  /// 默认身份证信息
  CardNetModel? card;

  /// 默认银行卡信息
  BankNetModel? bank;

  PrivacyGetNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (is_self_verify != null) map["is_self_verify"] = is_self_verify!;
    if (card != null) map["card"] = card!;
    if (bank != null) map["bank"] = bank!;
    return map;
  }

  factory PrivacyGetNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = PrivacyGetNetModel();
    netModel.is_self_verify = int.tryParse(json["is_self_verify"].toString());
    netModel.card = json["card"] == null
        ? null
        : CardNetModel.fromJson(json["card"] as Map<String, dynamic>);
    netModel.bank = json["bank"] == null
        ? null
        : BankNetModel.fromJson(json["bank"] as Map<String, dynamic>);
    return netModel;
  }

  PrivacyGetBizModel transform() {
    return PrivacyGetBizModel(
      isSelfVerify: is_self_verify ?? 0,
      card: card?.transform(),
      bank: bank?.transform(),
    );
  }
}

class CardNetModel {
  /// 姓名
  String? card_name;

  /// 身份证号
  String? card_no;

  CardNetModel({
    this.card_name,
    this.card_no,
  });

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (card_name != null) map["card_name"] = card_name!;
    if (card_no != null) map["card_no"] = card_no!;
    return map;
  }

  factory CardNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = CardNetModel();
    netModel.card_name = json["card_name"]?.toString();
    netModel.card_no = json["card_no"]?.toString();
    return netModel;
  }

  CardBizModel transform() {
    return CardBizModel(
      cardName: card_name ?? "",
      cardNo: card_no ?? "",
    );
  }
}

class BankNetModel {
  /// 开户行
  String? bank_name;

  /// 银行卡号
  String? bank_no;

  /// id
  int? privacy_id;

  BankNetModel({
    this.bank_name,
    this.bank_no,
    this.privacy_id,
  });

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (bank_name != null) map["bank_name"] = bank_name!;
    if (bank_no != null) map["bank_no"] = bank_no!;
    if (privacy_id != null) map["privacy_id"] = privacy_id!;
    return map;
  }

  factory BankNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = BankNetModel();
    netModel.bank_name = json["bank_name"]?.toString();
    netModel.bank_no = json["bank_no"]?.toString();
    netModel.privacy_id = int.tryParse(json["privacy_id"].toString());
    return netModel;
  }

  BankBizModel transform() {
    return BankBizModel(
      bankName: bank_name ?? "",
      bankNo: bank_no ?? "",
      privacyId: privacy_id ?? 0,
    );
  }
}
