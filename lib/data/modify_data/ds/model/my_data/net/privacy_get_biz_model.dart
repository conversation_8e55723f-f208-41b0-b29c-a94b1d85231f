import 'dart:convert';

class PrivacyGetBizModel {

  /// 是否已实名 1是 0否
  int isSelfVerify;

  /// 默认身份证信息
  CardBizModel? card;

  /// 默认银行卡信息
  BankBizModel? bank;

  PrivacyGetBizModel({
    this.isSelfVerify = 0,
    this.card,
    this.bank,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class CardBizModel {

  /// 姓名
  String cardName;

  /// 身份证号
  String cardNo;

  CardBizModel({
    this.cardName = "",
    this.cardNo = "",
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

class BankBizModel {

  /// 开户行
  String bankName;

  /// 银行卡号
  String bankNo;

  /// id
  int privacyId;

  BankBizModel({
    this.bankName = "",
    this.bankNo = "",
    this.privacyId = 0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}

