class BankCardListBizModel {
  /// 银行卡列表
  List<BankCardBizModel> bankList;

  BankCardListBizModel({
    this.bankList = const [],
  });
}

class BankCardBizModel {
  /// 隐私ID
  final int privacyId;

  /// 开户行
  final String bankName;

  /// 支行
  final String bankSubName;

  /// 银行卡号
  final String bankNo;

  /// 此银行卡已授权班组长列表
  final List<GrantListBizModel> grantList;

  BankCardBizModel({
    this.privacyId = 0,
    this.bankName = "",
    this.bankSubName = "",
    this.bankNo = "",
    this.grantList = const [],
  });
}


class GrantListBizModel {
  /// 班组长ID
  String leaderId;

  /// 班组长姓名
  String leaderName;

  GrantListBizModel({
    this.leaderId = "",
    this.leaderName = "",
  });
}
