import UIKit
import Flutter
import HTNetwork
import net_plugin
import AnyCodable
import Alamo<PERSON>

@main
@objc class AppDelegate: FlutterAppDelegate {
    override func application(
        _ application: UIApplication,
        didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
    ) -> Bool {
        GeneratedPluginRegistrant.register(with: self)
        NetPlugin.shared.register(self)
        return super.application(application, didFinishLaunchingWithOptions: launchOptions)
    }
}

extension AppDelegate: NetChannelDelegate {
    func request(param: BaseBizRequestEntity, completion: @escaping (Result<Any, any Error>) -> Void) {
        let method: HTTPMethod
        switch param.method {
        case .gET:
            method = .get
        case .pOST:
            method = .post
        default:
            method = .get
        }
        NetManager.request(param.url ?? "", model: AnyCodable.self, method: method, parameters: param.content, headers: param.requestExtra?.header) {
            [weak self] result in
            guard let this = self else { return }
            switch result {
            case let .success(response):
                var resDic: [String: Any] = [:]
                resDic["code"] = response.code
                resDic["message"] = response.msg
                resDic["data"] = response.data?.value
                completion(.success(resDic))
            case let .failure(error):
                completion(.failure(error))
            }
        }
    }

    func getSpecificDeviceInfo(completion: @escaping (Result<[String: Any], any Error>) -> Void) {
        completion(.success([:]))
    }
}

