//
//  DeviceInfoManager.swift
//  Runner
//
//  Created by Python on 2025/9/18.
//

import UIKit
import KeychainAccess
import AppTrackingTransparency
import AdSupport

class DeviceInfoManager {
    static let shared = DeviceInfoManager()

    private init() { }

    /// 设备型号
    var deviceModel: String {
        return UIDevice.current.model
    }

    /// 系统版本
    var systemVersion: String {
        return UIDevice.current.systemVersion
    }

    /// 系统名称
    var systemName: String {
        return UIDevice.current.systemName
    }

    /// 设备UUID
    var deviceUUID: String {
        let key = "uuid"
        let keychain = Keychain(service: AppInfoManager.shared.bundleIdentifier)
        guard let uuid = keychain[key] else {
            let uuid: String? = UIDevice.current.identifierForVendor?.uuidString
            keychain[key] = uuid
            return uuid ?? ""
        }
        return uuid
    }

    /// 获取设备名称,比如"iPhone11,8"
    var deviceName: String = {
        var systemInfo = utsname()
        uname(&systemInfo)
        let machineMirror = Mirror(reflecting: systemInfo.machine)
        let identifier = machineMirror.children.reduce("") { identifier, element in
            guard let value = element.value as? Int8, value != 0
            else { return identifier }
            return identifier + String(UnicodeScalar(UInt8(value)))
        }
        return identifier
    }()

    /// idfa
    var idfa: String {
        let isTrackingEnable: Bool
        if #available(iOS 14, *) {
            let status = ATTrackingManager.trackingAuthorizationStatus
            isTrackingEnable = status == .authorized
        } else {
            isTrackingEnable = ASIdentifierManager.shared().isAdvertisingTrackingEnabled
        }
        if isTrackingEnable {
            return ASIdentifierManager.shared().advertisingIdentifier.uuidString
        }
        return ""
    }
}
