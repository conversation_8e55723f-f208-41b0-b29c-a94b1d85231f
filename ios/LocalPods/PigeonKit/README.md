# PigeonKit

[![CI Status](https://img.shields.io/travis/<EMAIL>/PigeonKit.svg?style=flat)](https://travis-ci.org/<EMAIL>/PigeonKit)
[![Version](https://img.shields.io/cocoapods/v/PigeonKit.svg?style=flat)](https://cocoapods.org/pods/PigeonKit)
[![License](https://img.shields.io/cocoapods/l/PigeonKit.svg?style=flat)](https://cocoapods.org/pods/PigeonKit)
[![Platform](https://img.shields.io/cocoapods/p/PigeonKit.svg?style=flat)](https://cocoapods.org/pods/PigeonKit)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

PigeonKit is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'PigeonKit'
```

## Author

<EMAIL>, <EMAIL>

## License

PigeonKit is available under the MIT license. See the LICENSE file for more info.
