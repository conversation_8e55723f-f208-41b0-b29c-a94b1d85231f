<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>

    <style name="WelcomeTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:navigationBarColor">@color/transparent</item>
        <item name="android:statusBarColor">@color/transparent</item>
        <item name="android:windowBackground">@drawable/welcome_bitmap_splash_bg</item>
        <item name="android:windowFullscreen">true</item>
    </style>
    <style name="LaunchThemeNoBackground" parent="WelcomeTheme">
        <item name="android:windowBackground">@null</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>

    <item name="click_time" type="id" />
</resources>
