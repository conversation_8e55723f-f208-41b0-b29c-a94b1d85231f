plugins {
    id "com.android.application"
    id "kotlin-android"
    id 'kotlin-kapt'
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

android {
    namespace = "com.yupao.gongdijigong"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17
    }

    buildFeatures {
        dataBinding true
        buildConfig true
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.yupao.gongdijigong"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 24
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName

        javaCompileOptions {
            annotationProcessorOptions {
                arguments += [AROUTER_MODULE_NAME: project.getName(),"room.schemaLocation": "$projectDir/schemas".toString()]
            }
        }

        /**
         * 基础配置
         */
        manifestPlaceholders = [
                "JPUSH_PKGNAME"    : "com.yupao.gongdijigong",
                "JPUSH_APPKEY"     : "7cdae5d1328563434a79abf9",
                "JPUSH_CHANNEL"    : "developer-default",
                "XIAOMI_APPID"     : "MI-2882303761520004282",
                "XIAOMI_APPKEY"    : "MI-5832000432282",
                "MEIZU_APPID"      : "MZ-145523",
                "MEIZU_APPKEY"     : "MZ-d0f8d431c1d3492b8f712481eb676ccc",
                "OPPO_APPID"       : "***********",
                "OPPO_APPKEY"      : "OP-4a9643ca913245db9540072cfbd1c7b0",
                "OPPO_APPSECRET"   : "OP-f6940b95d6f0458282c28f63fd075193",
                "VIVO_APPID"       : "105501108",
                "VIVO_APPKEY"      : "6c5c77e9c9cf9957c68a922304ca5917",
                "WX_APP_ID"        : "wxf5529085b9a995bb",
                "WX_APP_SECRET"    : "60c705aa7b169041a5d011bf9c5d788d",
                "ONE_KEY_LOGIN_ID" : "fci9wwHq",
                "GD_APP_KEY"       : "39688b6ae31eb5a7d5eab960820f21d3",
                "GETUI_APPID"      : "",
                "GM_AD_APP_ID"     : "5322370",
                "SIG_MOB_AD_APP_ID": "27863",
                "HONOR_APPID"      : "",
                "UMENG_APP_KEY"    : "611dcaec652b2303f70efad0"
        ]
        def isRelease = getGradle().getStartParameter().getTaskRequests().toString().contains("Release")
        if (isRelease) { //正式火山
            manifestPlaceholders.put("APPLOG_SCHEME", "rangersapplog.34177c29b74db4ec")
        } else {
            manifestPlaceholders.put("APPLOG_SCHEME", "rangersapplog.a3c5342fb4933b75")
        }
        configurations.configureEach {
            resolutionStrategy {
                force rootProject.ext.dependencies.kotlinstdLib
                force 'androidx.core:core-ktx:1.7.0'
                force 'androidx.media:media:1.0.0'
            }
        }
    }


    signingConfigs {
        release {
            storeFile file('com.yupao.gdjg.jks')
            storePassword 'gdjg9527'
            keyAlias 'gdjg'
            keyPassword 'gdjg9527'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.release
        }

        debug {
            signingConfig signingConfigs.release
        }
    }

    dependencies {
        implementation 'com.yupao.ht_net:ht_net:1.0.29'
        implementation "androidx.appcompat:appcompat:1.6.1"
        implementation 'com.alibaba:fastjson:1.2.31'
        implementation rootProject.ext.dependencies.feature_mvvm_base

        implementation 'com.meituan.android.walle:library:1.1.6'
        implementation 'com.yupao.statistics:feature_statistics:1.1.0'

        modules {
            module("com.tencent:mmkv-static") {
                replacedBy("com.tencent:mmkv", "Using mmkv for flutter")
            }
            module("com.tencent:mmkv-shared") {
                replacedBy("com.tencent:mmkv", "Using mmkv for flutter")
            }
        }

        implementation rootProject.ext.dependencies.storage
        implAdBizData(it)
        implAdCore(it)
        implAdService(it)
        implementation rootProject.ext.dependencies.kotlinstdLib
        //router
        kapt rootProject.ext.dependencies.arouterCompiler
        implementation rootProject.ext.dependencies.arouterApi
        implementation rootProject.ext.dependencies.java_net
        implementation rootProject.ext.dependencies.workandaccount_net

        implementation rootProject.ext.dependencies.utils_system
        implementation rootProject.ext.dependencies.utils_log
        implementation rootProject.ext.dependencies.utils_lang
        implementation rootProject.ext.dependencies.utils_datetime
        implementation rootProject.ext.dependencies.utils_str
        implementation rootProject.ext.dependencies.permissionX
        implementation rootProject.ext.dependencies.map
        implementation rootProject.ext.dependencies.buried_point
        implementation rootProject.ext.dependencies.rxpermissions2
        implementation rootProject.ext.dependencies.crash_help
        implementation rootProject.ext.dependencies.volcengine
        implementation rootProject.ext.dependencies.volcengine_scheme
    }
}

flutter {
    source = "../.."
}
