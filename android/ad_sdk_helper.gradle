def includeYPAdSDK(String moduleName) {
    include(moduleName)
    String moduleFileName = moduleName.replace(":", "/")
    project(moduleName).projectDir = new File(ypAdSdkDir, "$moduleFileName")
}

def includeAdSdk() {
    if (useAdSdkLocalLibraries.toBoolean()) {
        includeYPAdSDK ':ad_service'
        includeYPAdSDK ':ad_core'
        includeYPAdSDK ':ad_model'
        includeYPAdSDK ':ad_common'
        includeYPAdSDK ':ad_helper'
        includeYPAdSDK ':ad_protocol'
        includeYPAdSDK ':ad_tobid'
        includeYPAdSDK ':ad_render'
        includeYPAdSDK ':ad_material'
        includeYPAdSDK ':ad_tobid_adn'
        includeYPAdSDK ':ad_tobid_adn:huawei'
        includeYPAdSDK ':ad_tobid_adn:meiyue'
        includeYPAdSDK ':ad_tobid_adn:mercury'
        includeYPAdSDK ':ad_tobid_adn:octopus'
        includeYPAdSDK ':ad_tobid_adn:qumeng'
        includeYPAdSDK ':ad_tobid_adn:noah'
        includeYPAdSDK ':ad_biz_data'
        includeYPAdSDK ':ad_tobid_adn:fanwei'
//        includeYPAdSDK ':ad_tobid_adn:baichuan'
    }
}

def snapshotCompat(url) {
    return isSnapshot.toBoolean() ? "$url-SNAPSHOT" : url
}

def changingCompat() {
    return isSnapshot.toBoolean()
}

def implAdBizData(dependencyHandler) {
    if (useAdSdkLocalLibraries.toBoolean()) {
        dependencyHandler.implementation project(':ad_biz_data')
    } else {
        dependencyHandler.implementation (snapshotCompat("com.yupao.ad_service:ad_biz_data:$AD_SDK_VERSION")){
            changing = changingCompat()
        }
    }
}

def implAdService(dependencyHandler) {
    if (useAdSdkLocalLibraries.toBoolean()) {
        dependencyHandler.implementation project(':ad_service')
    } else {
        dependencyHandler.implementation (snapshotCompat("com.yupao.ad_service:ad_service:$AD_SDK_VERSION")){
            changing = changingCompat()
        }
    }
}

def implAdCore(dependencyHandler) {
    if (useAdSdkLocalLibraries.toBoolean()) {
        dependencyHandler.implementation project(':ad_core')
    } else {
        dependencyHandler.implementation (snapshotCompat("com.yupao.ad_service:ad_core:$AD_SDK_VERSION")){
            changing = changingCompat()
        }
    }
}

ext {
    includeAdSdk = this.&includeAdSdk
    implAdBizData = this.&implAdBizData
    implAdService = this.&implAdService
    implAdCore = this.&implAdCore
}