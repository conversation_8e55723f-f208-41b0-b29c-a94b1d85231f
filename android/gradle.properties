#
#
COMPILE_SDK_VERSION=33
MIN_SDK_VERSION=24
TARGET_SDK_VERSION=33
BUILD_TOOLS_VERSION=33.0.2
android.enableJetifier=true
android.useAndroidX=true
org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:+HeapDumpOnOutOfMemoryError
#org.gradle.java.home=/Users/<USER>/Library/Java/JavaVirtualMachines/jbr-17.0.14/Contents/Home

#local dependencies yp_ad_sdk
ypAdSdkDir=/Users/<USER>/StudioProjects/ad_service
isSnapshot = true
useAdSdkLocalLibraries=false
AD_SDK_VERSION = 10.12.0

#yupao dependencies version
COMMON_VERSION=3.9.4.1
STR_VERSION=6.1.0.1
LANG_VERSION=3.1.0
LOG_VERSION=3.1.0.4
DATATIME_VERSION=5.7.0.1
DATA_PROTOCOL_VERSION=1.0.30
SYSTEM_VERSION=6.2.0.3
VIEW_VERSION=3.1.0.5
KEYBOARD_VERSION=3.0.3.3
BURIED_POINT_VERSION=1.2.1
#yupao widget dependencies version
WIDGET_BASE_VERSION=1.8
WIDGET_BANNER_VERSION=1.0
WIDGET_FONT_VERSION=1.0
WIDGET_GUIDE_VERSION=1.2.0
WIDGET_IMAGE_VERSION=1.3.3
WIDGET_PICK_VERSION=1.1
WIDGET_RECYCLERVIEW_VERSION=1.0.1
WIDGET_TEXT_VERSION=3.9.0.5
WIDGET_VIEW_VERSION=4.0.0.7
BRIDGE_WEB_VERSION=0.2.20
MODEL_NET_BUSINESS=1.0.2
NETLIBRARY_VERSION=1.1.0
STORAGE_VERSION=1.0.3
FEATURE_MVVM_VERSION=1.0.14
FEATURE_MVVM_PROTOCOL_VERSION=1.0.15
XBUS_VERSION=3.0.3.1
QR_VERSION=4.0.0.9
FEATURE_MVVM_BASE_VERSION=1.0.22
WORKANDACCOUNT_NET_VERSION=1.0.1
CRASH_HELPER_VERSION=1.0.2
MAP_VERSION=1.0.5
