# 1. 命名方式

* 类名应使用大驼峰命名法（PascalCase），如 `SendCodeNetModel`。

* 变量和方法名应使用小驼峰命名法（camelCase），如 `token`、`toJson()`。

* 文件名也应使用小驼峰命名法，如 `send_code_net_model.dart`。

* 文件名字和类名必须保持一致，如文件名字为：`send_code_net_model.dart`，类名为：`SendCodeNetModel`

# 2. Data层开发范式

## 2.1 参数模型

* 这是一个参数模型类，通常用于方法的入参，如一个方法的入参很多，会使用参数模型把参数封装起来。

* 文件结尾必须是`param_model`。如`code_login_param_model.dart`。

* 模型中的所有字段类型可以为空。

* 必须需要一个toMap方法，其方法返回值类型必须为`Map<String, Object>`

* 不允许出现任何的其他的方法。

以下是参数模型的示例：

```dart
import 'login_share_param_model.dart';

class CodeLoginParamModel {
  String? code;
  ShareReq? shareReq;
  String? tel;
  String? verifyToken;

  CodeLoginParamModel();

  Map<String, Object> toMap() {
    final Map<String, Object> map = {};

    if (code != null) map['code'] = code!;
    
    if (shareReq != null) map['shareReq'] = shareReq!.toMap();
    
    if (tel != null) map['tel'] = tel!;
 
    if (verifyToken != null) map['verifyToken'] = verifyToken!;
   
    return map;
  }
}
```

## 2.2 网络模型

* 这是一个网络模型类，通常用于接收接口数据返回解析。

* 文件的结尾必须是net\_model结尾。如`login_code_login_net_model.dart`。

* 必须包含`toJson`、`fromJson`两个解析方法，

* 可以包含`transform`方法，该方法会把网络模型转成业务模型。（非必须）

* 除了`toJson`、`fromJson`、`transform` 三种方法以外不允许出现其他的方法。

* 所有的成员变量必须为可空类型。

以下是`send_code_net_model.dart`示例：

```dart
class LoginIgnoreSendNetModel {

  /// 剩余可发送次数
  int? remainingSendTimes;

  /// 校验token（校验验证码时使用）
  String? verifyToken;

  /// 发送间隔（单位秒）
  int? sendInterval;

  LoginIgnoreSendNetModel();

  Map<String, dynamic> toJson() {
    var map = <String, Object>{};
    if (remainingSendTimes != null) map["remainingSendTimes"] = remainingSendTimes!;
    if (verifyToken != null) map["verifyToken"] = verifyToken!;
    if (sendInterval != null) map["sendInterval"] = sendInterval!;
    return map;
  }

  factory LoginIgnoreSendNetModel.fromJson(Map<String, dynamic> json) {
    var netModel = LoginIgnoreSendNetModel();
    netModel.remainingSendTimes = int.tryParse(json["remainingSendTimes"].toString());
    netModel.verifyToken = json["verifyToken"]?.toString();
    netModel.sendInterval = int.tryParse(json["sendInterval"].toString());
    return netModel;
  }
  
  @override
  String toString() {
    return jsonEncode(this);
  }

}
```

## 2.3 远程数据来源

* 这是一个远程数据来源类，通常用于发起网络请求。相同业务域下的网络请求，可以放在同一个远程数据来源类中。

* 文件结尾必须是`rds`。如：`auth_rds`

### 2.3.1 声明接口请求方法

* 这是一个声明接口请求方法，该方法必须为异步请求。返回类型固定为`Future<RespResult<T>>` ,其中泛型`T`为任意网络模型。

* 方法发起请求的类必须是 `NetCore`类。

* 在发起请求代码之前必须添加 `await`

以下是请求方法的示例：

```dart
  Future<RespResult<SendCodeNetModel>> sendCode(String phone) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/reach/v1/verifyCode/loginIgnore/send',
            method: HTTP_METHOD.POST,
            content: {
              "biz": "login",
              "tel": phone,
            }),
        (json) => SendCodeNetModel.fromJson(json));
  }
  
```

以下是远程来源类的完整示例：

```dart
import 'package:gdjg_pure_flutter/data/account/ds/model/net/send_code_net_model.dart';
import 'package:net_plugin/pigeon/api_generated/net_channel.g.dart';
import 'package:net_plugin/pigeon/net_result/resp_result.dart';
import 'package:net_plugin/pigeon/net_service/net_core.dart';

import 'model/net/login_code_login_net_model.dart';
import 'model/net/waa_login_net_model.dart';
import 'model/param/code_login_param_model.dart';
import 'model/param/one_key_login_params_model.dart';
import 'model/param/waa_login_param_model.dart';
import 'model/param/we_chat_login_params_model.dart';


class AuthRds {
  ///  发送验证码
  Future<RespResult<SendCodeNetModel>> sendCode(String phone) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/reach/v1/verifyCode/loginIgnore/send',
            method: HTTP_METHOD.POST,
            content: {
              "biz": "login",
              "tel": phone,
            }),
        (json) => SendCodeNetModel.fromJson(json));
  }

  ///  验证码登陆
  Future<RespResult<LoginResultNetModel>> codeLogin(CodeLoginParamModel param) async {
    return await NetCore.requestYPJAVA(
        BaseBizRequestEntity(
            url: '/account/v1/login/codeLogin',
            method: HTTP_METHOD.POST,
            content: param.toMap()),
        (json) => LoginResultNetModel.fromJson(json));
  }
}
```

## 2.4 本地数据来源（数据可监听）

* 这是本地监听的数据来源，通常用于本地贮存少量数据，且值的变化可以监听。

* 文件结尾必须为`lds`。如：`account_lds`。

* 类必须继承为`BaseLds<T>`，其中泛型`<T>`为需要保存数据的类型。

* 其中方法 `getBizName()`，必须实现。该方法实现返回的值通常为当前业务域的名字。

* 其中`toJson()`和`fromJson()`两个方法，当泛型`<T>`的类型为`Object`的时候必须实现。

* 该本地来源推荐保存网络模型。

* `getStoreIndexExpand()`方法来拓展Key，非必须实现。如实现返回手机号、用户ID等，可存不同用户的数据。

以下是`account_lds`示例：

```dart
import '../../../utils/store_util/base_lds.dart';
import 'model/net/waa_login_net_model.dart';
import 'model/net/waa_login_net_model_transform.dart';

class AccountLds extends BaseLds<WaaLoginNetModel> {

  @override
  String getBizName() {
    return "account";
  }

  @override
  Map<String, dynamic> toJson(WaaLoginNetModel value) {
    return waaLoginNetModelToJson(value);
  }

  @override
  fromJson(Map<String, dynamic> json) {
    return waaLoginNetModelFromJson(json);
  }
}
```

* 本地数据来源只能在业务仓库中使用。以下是使用示例:

```dart
class AuthRepo {
  final _accountLds = AccountLds();

  /**其他代码**/

  Future<RespResult<WaaLoginBizModel>> _waaLogin(
      LoginResultNetModel? param, ShareReq? shareReq) async {
  /**其他代码**/

    if (re.isOK()) {
      final data = re.getSucData();
      if (data != null) {
        _accountLds.save(data);
      }
    }

    return re.map(waaLoginTransform);
  }

  WaaLoginBizModel getAccount() {
    return waaLoginTransform(_accountLds.get());
  }
}
```

## 2.5 本地数据来源（不可监听，简单版，有风险）

* 该本地数据来源，通常也是用于保存数据。

* 该本本地数据来源没有有单独的类是只是一个工具的调用。

* LDS必须使用 `KVUtil.getKV()`来获取，必须为私有。

* `KVUtil.getKV()`*&#x20;*&#x65B9;法的入参是一个字符串，通常使用当前的业务的的字符串。

* 还需要声明私有的贮存数据的`KEY`，用于存贮、获取方法的入参。

* 除了贮存`Object`的方法还有其他类型的方法。`setString()`、`setInt()`、`setBool()`、`setDouble()`等基本类型的存贮。

以下是使用实例:

```dart
class AuthRepo {
  final _accountKey = "account_data";
  final _accountLds = KVUtil.getKV("account");

  /**其他代码**/

  Future<RespResult<WaaLoginBizModel>> _waaLogin(
      LoginResultNetModel? param, ShareReq? shareReq) async {
 /**其他代码**/
    if (re.isOK()) {
      final data = re.getSucData();
      if (data != null) {
        _accountLds.setObject(_accountKey, () => data.toJson());
      }
    }

    return re.map(waaLoginTransform);
  }

  WaaLoginBizModel getAccount() {
    return waaLoginTransform(
    _accountLds.getObject(_accountKey, (s) => waaLoginNetModelFromJson(s)));
  }
}
```

## 2.6 业务模型

* 这是一个业务模型类，通常用于业务仓库数据获取方法返回值的实体类型。

* 文件结尾必须为`biz_model`。如：`waa_login_biz_model`

* 必须包含构造函数。

* 原则上字段为不可空，允许存在少量的可空字段。

以下是`waa_login_biz_model`示例：

```dart
import 'dart:convert';

class SendCodeBizModel {
  /// 剩余可发送次数
  int remainingSendTimes;

  /// 校验token（校验验证码时使用）
  String verifyToken;

  /// 发送间隔（单位秒）
  int sendInterval;

  SendCodeBizModel({
    this.remainingSendTimes = 0,
    this.verifyToken = "",
    this.sendInterval = 0,
  });

  @override
  String toString() {
    return jsonEncode(this);
  }
}
```

## 2.7 业务仓库

* 类中的成员变量只能为私有，且只有远程数据来源、本地数据来源、原生数据来源这几类成员，一个业务仓库中可持有多个、多种数据来源。通常我们把远程数据来源、本地数据来源、原生数据来源统称为数据来源。

* 这是一个业务仓库类，通常用于调用一个或多个数据来源的方法获取数据，并对其进行计算、转换、合并、读写等操作生成**业务模型**、**业务状态**、**错误业务状态**。

### 2.7.1 声明获取业务仓库数据的方法

* 这是一个声明接口请求方法，该方法必须为异步请求。返回类型固定为`Future<RespResult<T>>` ,其中泛型`T`为任意业务模型。`RespResult`包含成功、失败两种状态。其中成功状态下包含业务模&#x578B;**。**

* 获取数据的方法为数据来源。

* 在发起请求代码之前必须添加 `await`

以下是请求方法的示例：

1. `phone`两个字段校验的代码，返回的为错误的业务状态。

2. `SendCodeBizModel`则为包含的业务模型。

3. `sendCodeTransform`为转换操作。

```dart
Future<RespResult<SendCodeBizModel>> sendCode(String phone) async {
  if (!RegexUtils.isMobile(phone)) {
    return RespFail.buildProcessFail("请输入正确手机号");
  }
  final resp = await _authRds.sendCode(phone);
  return resp.map(sendCodeTransform);
}

SendCodeBizModel sendCodeTransform(SendCodeNetModel? model) {
  return SendCodeBizModel(
    remainingSendTimes: model?.remainingSendTimes ?? 0,
    verifyToken: model?.verifyToken ?? "",
    sendInterval: model?.sendInterval ?? 60,
  );
}
```

以下是完整的业务仓库

1. `isLogin()`则返回的为业务状态。

```dart
class AuthRepo {
  final _authRds = AuthRds();
  final _accountLds = AccountLds();

  Future<RespResult<WaaLoginBizModel>> loginCodeLogin(
      CodeLoginParamModel param) async {
    if (!RegexUtils.isMobile(param.tel)) {
      return RespFail.buildProcessFail("请输入正确手机号");
    }
    if (!RegexUtils.isVerificationCode(param.code)) {
      return RespFail.buildProcessFail("请输入正确验证码");
    }
    final resp = await _authRds.codeLogin(param);

    final data = resp.getSucData();
    if (data == null) {
      return RespFail.buildBizFail<WaaLoginBizModel>(
          resp.fail?.code, resp.fail?.errorMsg, null, resp.fail?.askId);
    }

    return await _waaLogin(data, param.shareReq);
  }

  Future<RespResult<SendCodeBizModel>> sendCode(String phone) async {
    if (!RegexUtils.isMobile(phone)) {
      return RespFail.buildProcessFail("请输入正确手机号");
    }
    final resp = await _authRds.sendCode(phone);
    return resp.map(sendCodeTransform);
  }

  Future<RespResult<WaaLoginBizModel>> _waaLogin(
      LoginResultNetModel? param, ShareReq? shareReq) async {
    final re = await _authRds.waaLogin(WaaLoginParamModel(
        token: param?.token,
        newMember: "",
        origin: "",
        refid: shareReq?.refTenantId));

    if (re.isOK()) {
      final data = re.getSucData();
      if (data != null) {
        _accountLds.save(data);
      }
    }

    return re.map(waaLoginTransform);
  }

  WaaLoginBizModel getAccount() {
    return waaLoginTransform(_accountLds.get());
  }

  bool isLogin() {
    final user = getAccount();
    return user.uid != 0;
  }
}
```
